/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.user-welcome.data-v-c2bb628e {
  margin-bottom: var(--app-spacing-lg);
  text-align: center;
  padding: var(--app-spacing-base) 0;
}
.workspace-menu-item.data-v-c2bb628e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-base);
  min-height: 120rpx;
  transition: all var(--app-duration-base) var(--app-ease-out);
}
.workspace-menu-item.data-v-c2bb628e:hover {
  background: var(--app-color-gray-50);
}
.workspace-menu-item.data-v-c2bb628e:active {
  background: var(--app-color-gray-100);
  transform: scale(0.98);
}
.workspace-menu-text.data-v-c2bb628e {
  margin-top: var(--app-spacing-xs);
  font-size: var(--app-font-size-xs);
  color: var(--app-text-secondary);
  text-align: center;
  line-height: var(--app-line-height-tight);
}
.user-welcome.data-v-c2bb628e {
  margin-bottom: var(--app-spacing-md);
  padding: var(--app-spacing-sm) 0;
}
.workspace-menu-item.data-v-c2bb628e {
  min-height: 100rpx;
  padding: var(--app-spacing-sm);
}