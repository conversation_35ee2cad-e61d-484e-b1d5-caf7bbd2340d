"""
Product inference service for identifying product_id/product_name from text
"""

import logging
import json
from typing import List, Dict, Any, Optional
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from core.config import get_settings
from core.database import get_database_connection
from utils.serialization import safe_json_dumps

logger = logging.getLogger(__name__)


class ProductInferenceService:
    """Service for inferring product_id and product_name from commodity text"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize OpenAI model for product inference
        self.model = ChatOpenAI(
            temperature=0.2,
            openai_api_base=self.settings.openai_api_base,
            model=self.settings.openai_model,
            api_key=self.settings.openai_api_key,
            max_retries=2
        )
        
        # Product inference prompt template
        self.prompt = PromptTemplate(
            template="""你是一个大宗商品期货专家。根据用户的文本内容和提供的期货产品清单，推断最匹配的期货产品。

用户文本内容：{user_text}
商品名称（如果提供）：{commodity_name}

可用的期货产品清单：
{products_data}

请分析文本中的商品关键词，从提供的产品清单中选择最匹配的产品。

常见商品映射规则：
- 螺纹钢、钢筋 -> 螺纹钢
- 铜、沪铜、精炼铜 -> 沪铜
- 铝、沪铝、电解铝、铝锭 -> 沪铝  
- 豆粕 -> 豆粕
- 玉米 -> 玉米
- 白糖、糖 -> 白糖
- 焦炭 -> 焦炭
- 焦煤 -> 焦煤
- 铁矿石 -> 铁矿石

请返回最匹配的产品信息，包括：
1. product_id（产品代码）
2. product_name（产品名称）
3. 匹配置信度（0-1之间）
4. 匹配理由

返回格式必须是有效的JSON：
{{
    "product_id": "产品代码",
    "product_name": "产品名称", 
    "confidence": 置信度（0-1浮点数）,
    "reasoning": "详细匹配理由"
}}

如果无法匹配到合适的产品，请返回置信度为0。
""",
            input_variables=["user_text", "commodity_name", "products_data"]
        )
    
    async def get_available_products(self) -> List[Dict[str, Any]]:
        """
        从数据库获取所有可用的期货产品清单
        
        Returns:
            List[Dict]: 产品清单，包含product_id和product_name
        """
        try:
            db = await get_database_connection()
            
            # 查询所有不重复的产品
            query = """
            SELECT DISTINCT product_id, product_name, exchange_id
            FROM instruments 
            WHERE inst_life_phase IN ('0', '1')  -- 未上市和上市状态
            ORDER BY product_id
            """
            
            results = await db.execute_query(query)
            logger.info(f"Retrieved {len(results)} unique products from database")
            return results
            
        except Exception as e:
            logger.error(f"Failed to fetch products from database: {e}")
            return []
    
    async def infer_product(self, text: str, commodity_name: Optional[str] = None) -> Dict[str, Any]:
        """
        根据文本内容推断最匹配的期货产品
        
        Args:
            text: 需要分析的文本内容
            commodity_name: 商品名称（可选，用于辅助推断）
            
        Returns:
            Dict: 包含推断结果的字典
        """
        try:
            logger.info(f"Inferring product for text: {text[:100]}...")
            
            # 获取产品清单
            products = await self.get_available_products()
            if not products:
                return {
                    "success": False,
                    "message": "无法获取产品数据"
                }
            
            # 格式化产品数据为文本（处理Decimal类型）
            products_text = safe_json_dumps(products, indent=2)
            
            # 构建推断请求
            prompt_input = {
                "user_text": text,
                "commodity_name": commodity_name or "未指定",
                "products_data": products_text
            }
            
            # 调用LLM进行推断
            response = await self.model.ainvoke(
                self.prompt.format(**prompt_input)
            )
            
            # 解析LLM返回的JSON
            try:
                result = json.loads(response.content)
                product_id = result.get("product_id")
                product_name = result.get("product_name")
                confidence = result.get("confidence", 0.0)
                reasoning = result.get("reasoning", "")
                
                logger.info(f"Product inference result: {product_id} - {product_name} (confidence: {confidence})")
                
                return {
                    "success": True,
                    "product_id": product_id,
                    "product_name": product_name,
                    "confidence": confidence,
                    "reasoning": reasoning
                }
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM JSON response: {e}")
                return {
                    "success": False,
                    "message": "AI返回格式错误"
                }
                
        except Exception as e:
            logger.error(f"Unexpected error during product inference: {e}")
            return {
                "success": False,
                "message": "产品推断服务暂时不可用"
            }


# Global service instance (lazy initialization)
_product_inference_service = None


def get_product_inference_service() -> ProductInferenceService:
    """Get or create the global product inference service instance"""
    global _product_inference_service
    if _product_inference_service is None:
        _product_inference_service = ProductInferenceService()
    return _product_inference_service