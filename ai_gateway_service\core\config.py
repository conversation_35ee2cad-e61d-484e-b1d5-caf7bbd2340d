"""
Configuration management for AI Gateway service
"""

import os
from functools import lru_cache
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings using Pydantic Settings"""
    
    # Application settings
    app_name: str = Field(default="AI Gateway Service", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # Server settings
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8001, description="服务器端口")
    
    # OpenAI settings
    openai_api_base: str = Field(default="", description="OpenAI API基础URL")
    openai_api_key: str = Field(..., description="OpenAI API密钥")
    openai_model: str = Field(default="gpt-4-turbo", description="使用的OpenAI模型")
    openai_timeout: int = Field(default=30, description="OpenAI API超时时间（秒）")
    
    # Logging settings
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="日志格式"
    )
    
    # CORS settings
    cors_origins: str = Field(
        default="http://localhost:3000,http://localhost:5173", 
        description="允许的CORS源（逗号分隔）"
    )
    
    # Database settings
    db_host: str = Field(default="localhost", description="数据库主机")
    db_port: int = Field(default=3306, description="数据库端口")
    db_user: str = Field(default="root", description="数据库用户名")
    db_password: str = Field(default="123456", description="数据库密码")
    db_name: str = Field(default="dianjia", description="数据库名称")
    db_charset: str = Field(default="utf8mb4", description="数据库字符集")
    
    def get_cors_origins_list(self) -> list[str]:
        """Parse comma-separated CORS origins into a list"""
        return [origin.strip() for origin in self.cors_origins.split(",")]
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()