"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const http_http = require("../http/http.js");
function createQuotation(data) {
  return http_http.http.post("/dianjia/quotations", data);
}
function updateQuotation(data) {
  return http_http.http.put(`/dianjia/quotations/${data.id}`, data);
}
function deleteQuotation(quotationId) {
  return http_http.http.delete(`/dianjia/quotations/${quotationId}`);
}
function publishQuotation(data) {
  return http_http.http.post(`/dianjia/quotations/${data.id}/publish`, data);
}
function toggleQuotationStatus(quotationId) {
  return http_http.http.post(`/dianjia/quotations/${quotationId}/toggle`);
}
function getPublicQuotationList(params) {
  return http_http.http.post("/dianjia/public-quotations", params);
}
function getMyQuotationList(params) {
  return http_http.http.get("/dianjia/my-quotations", params);
}
function getPublicQuotationDetail(quotationId) {
  return http_http.http.get(`/dianjia/public-quotations/${quotationId}`);
}
function getQuotationDetail(quotationId) {
  return http_http.http.get(`/dianjia/quotations/${quotationId}`);
}
function saveQuotationDraft(data) {
  const requestData = __spreadProps(__spreadValues({}, data), { status: "Draft" });
  return createQuotation(requestData);
}
exports.deleteQuotation = deleteQuotation;
exports.getMyQuotationList = getMyQuotationList;
exports.getPublicQuotationDetail = getPublicQuotationDetail;
exports.getPublicQuotationList = getPublicQuotationList;
exports.getQuotationDetail = getQuotationDetail;
exports.publishQuotation = publishQuotation;
exports.saveQuotationDraft = saveQuotationDraft;
exports.toggleQuotationStatus = toggleQuotationStatus;
exports.updateQuotation = updateQuotation;
