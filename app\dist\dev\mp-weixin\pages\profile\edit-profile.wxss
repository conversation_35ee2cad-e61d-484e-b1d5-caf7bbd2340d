/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.profile-list.data-v-5d91d807 {
  overflow: hidden;
}
.section-title.data-v-5d91d807 {
  padding: 20rpx 24rpx 12rpx;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.list-item.data-v-5d91d807 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: background-color 0.3s ease;
}
.list-item.data-v-5d91d807:last-child {
  border-bottom: none;
}
.list-item.data-v-5d91d807:active {
  background-color: #f8f9fa;
}
.item-left.data-v-5d91d807 {
  display: flex;
  align-items: center;
}
.item-left .item-label.data-v-5d91d807 {
  margin-left: 16rpx;
  font-size: 28rpx;
  color: #333;
}
.item-right.data-v-5d91d807 {
  display: flex;
  align-items: center;
}
.item-right .item-value.data-v-5d91d807 {
  font-size: 26rpx;
  color: #606266;
  margin-right: 12rpx;
}
.item-right .item-tip.data-v-5d91d807 {
  font-size: 22rpx;
  color: #909399;
  margin-right: 12rpx;
}
.edit-popup.data-v-5d91d807 {
  padding: 32rpx;
  background: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
}
.popup-header.data-v-5d91d807 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32rpx;
}
.popup-header .popup-title.data-v-5d91d807 {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
}
.popup-content.data-v-5d91d807 {
  margin-bottom: 48rpx;
}
.popup-actions.data-v-5d91d807 {
  display: flex;
  gap: 20rpx;
}
.popup-actions .cancel-btn.data-v-5d91d807,
.popup-actions .save-btn.data-v-5d91d807 {
  flex: 1;
  height: 80rpx !important;
  border-radius: 40rpx !important;
  font-size: 28rpx !important;
}
.data-v-5d91d807 .save-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
}