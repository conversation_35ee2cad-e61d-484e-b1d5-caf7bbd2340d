"""
Contract inference service using LangChain and database queries
"""

import logging
import json
from typing import List, Optional, Dict, Any
from langchain_openai import ChatOpenAI
from langchain.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser
from langchain.schema import OutputParserException

from schemas.quotation import InstrumentSelectItem, ContractInferenceRequest, ContractInferenceResponse
from core.config import get_settings
from core.database import get_database_connection
from services.product_inference import get_product_inference_service
from utils.serialization import safe_json_dumps

logger = logging.getLogger(__name__)


class ContractInferenceService:
    """Service for inferring instrumentRefID based on text content and database contracts"""
    
    def __init__(self):
        self.settings = get_settings()
        
        # Initialize OpenAI model for inference
        self.model = ChatOpenAI(
            temperature=0.3,  # Slightly higher temperature for reasoning
            openai_api_base=self.settings.openai_api_base,
            model=self.settings.openai_model,
            api_key=self.settings.openai_api_key,
            max_retries=2
        )
        
        # Contract inference prompt template
        self.prompt = PromptTemplate(
            template="""你是一个期货合约匹配专家。根据用户的报价文本内容和提供的合约数据库信息，推断最合适的期货合约。

用户文本内容：{user_text}
商品名称（如果提供）：{commodity_name}

可用的期货合约数据：
{contracts_data}

请分析文本中的关键信息（如商品名称、交割月份、具体品种等），并从提供的合约列表中选择最匹配的合约。

考虑因素：
1. 商品名称匹配度（铜、螺纹钢、豆粕等）
2. 交割月份信息（如"5月"对应05合约）
3. 交易所信息（如上期所、大商所等）
4. 合约活跃度（优先选择主力合约）

请返回：
1. 最匹配的合约ID和详细信息
2. 匹配置信度（0-1之间的数值）
3. 详细的推断理由

如果无法找到合适的合约，请返回置信度为0并说明原因。

返回格式必须是有效的JSON：
{{
    "instrument_id": 合约ID（数字）,
    "confidence": 置信度（0-1浮点数）,
    "reasoning": "详细推断理由"
}}
""",
            input_variables=["user_text", "commodity_name", "contracts_data"]
        )
    
    async def get_contracts_by_product(self, product_id: str, limit: int = 20) -> List[Dict[str, Any]]:
        """
        根据product_id从数据库获取相关合约数据
        
        Args:
            product_id: 产品ID（如rb、cu、al等）
            limit: 返回的合约数量限制
            
        Returns:
            List[Dict]: 合约数据列表
        """
        try:
            db = await get_database_connection()
            
            # 查询指定产品的活跃合约
            query = """
            SELECT id, instrument_id, instrument_name, product_id, product_name, 
                   exchange_id, delivery_year, delivery_month, inst_life_phase,
                   volume_multiple, price_tick
            FROM instruments 
            WHERE product_id = %s 
            AND inst_life_phase IN ('0', '1')  -- 未上市和上市状态
            ORDER BY delivery_year DESC, delivery_month DESC
            LIMIT %s
            """
            
            results = await db.execute_query(query, (product_id, limit))
            logger.info(f"Retrieved {len(results)} contracts for product {product_id}")
            return results
            
        except Exception as e:
            logger.error(f"Failed to fetch contracts from database: {e}")
            return []
    
    async def infer_contract(self, request: ContractInferenceRequest) -> ContractInferenceResponse:
        """
        两阶段合约推断：先推断产品，再推断具体合约
        
        Args:
            request: 合约推断请求
            
        Returns:
            ContractInferenceResponse: 推断结果
        """
        try:
            logger.info(f"Inferring contract for text: {request.text[:100]}...")
            
            # 第一阶段：推断产品
            product_service = get_product_inference_service()
            product_result = await product_service.infer_product(request.text, request.commodity_name)
            
            if not product_result.get("success") or product_result.get("confidence", 0) < 0.5:
                return ContractInferenceResponse(
                    success=False,
                    message=f"无法确定产品类型: {product_result.get('message', '置信度过低')}"
                )
            
            product_id = product_result["product_id"]
            logger.info(f"Product inference successful: {product_id} (confidence: {product_result['confidence']})")
            
            # 第二阶段：根据产品ID获取相关合约并推断具体合约
            contracts = await self.get_contracts_by_product(product_id)
            if not contracts:
                return ContractInferenceResponse(
                    success=False,
                    message=f"产品 {product_id} 没有可用合约"
                )
            
            # 格式化合约数据为文本（处理Decimal类型）
            contracts_text = safe_json_dumps(contracts, indent=2)
            
            # 构建推断请求
            prompt_input = {
                "user_text": request.text,
                "commodity_name": request.commodity_name or "未指定",
                "contracts_data": contracts_text
            }
            
            # 调用LLM进行具体合约推断
            response = await self.model.ainvoke(
                self.prompt.format(**prompt_input)
            )
            
            # 解析LLM返回的JSON
            try:
                result = json.loads(response.content)
                instrument_id = result.get("instrument_id")
                confidence = result.get("confidence", 0.0)
                reasoning = result.get("reasoning", "")
                
                # 验证推断结果
                if instrument_id and confidence > 0:
                    # 根据ID查找对应的合约详细信息
                    selected_contract = None
                    for contract in contracts:
                        if contract["id"] == instrument_id:
                            selected_contract = contract
                            break
                    
                    if selected_contract:
                        instrument_ref = InstrumentSelectItem(
                            id=selected_contract["id"],
                            instrument_id=selected_contract["instrument_id"],
                            instrument_name=selected_contract["instrument_name"],
                            product_name=selected_contract["product_name"],
                            exchange_id=selected_contract["exchange_id"]
                        )
                        
                        # 综合置信度（产品推断 * 合约推断）
                        combined_confidence = product_result["confidence"] * confidence
                        combined_reasoning = f"产品推断: {product_result['reasoning']}; 合约推断: {reasoning}"
                        
                        logger.info(f"Successfully inferred contract: {selected_contract['instrument_id']} with combined confidence {combined_confidence}")
                        
                        return ContractInferenceResponse(
                            success=True,
                            instrumentRefID=instrument_id,
                            instrumentRef=instrument_ref,
                            confidence=combined_confidence,
                            reasoning=combined_reasoning,
                            message="合约推断成功"
                        )
                    else:
                        logger.warning(f"Inferred contract ID {instrument_id} not found in database")
                        return ContractInferenceResponse(
                            success=False,
                            message=f"推断的合约ID {instrument_id} 在数据库中不存在"
                        )
                else:
                    logger.info(f"Low confidence contract inference: {confidence}")
                    return ContractInferenceResponse(
                        success=False,
                        confidence=confidence,
                        reasoning=reasoning,
                        message="无法确定具体的期货合约"
                    )
                    
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM JSON response: {e}")
                return ContractInferenceResponse(
                    success=False,
                    message="AI返回格式错误"
                )
                
        except Exception as e:
            logger.error(f"Unexpected error during contract inference: {e}")
            return ContractInferenceResponse(
                success=False,
                message="合约推断服务暂时不可用"
            )


# Global service instance (lazy initialization)
_contract_inference_service = None


def get_contract_inference_service() -> ContractInferenceService:
    """Get or create the global contract inference service instance"""
    global _contract_inference_service
    if _contract_inference_service is None:
        _contract_inference_service = ContractInferenceService()
    return _contract_inference_service