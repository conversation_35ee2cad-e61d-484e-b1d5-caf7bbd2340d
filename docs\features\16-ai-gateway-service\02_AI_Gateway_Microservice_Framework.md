# AI 网关微服务：整体框架方案

> **版本**: 1.0
> **状态**: 已批准
> **技术栈**: Python, FastAPI, LangChain, Pydantic, UV

---

## 1. 综述与设计目标

### 1.1. 服务定位

AI网关是一套独立的、统一的微服务，其核心定位是作为项目中所有人工智能（AI）和大型语言模型（LLM）任务的**唯一入口和处理中心**。它旨在将快速发展的AI能力与相对稳定的主业务系统（Go后端）进行物理和逻辑上的解耦。

所有AI相关的需求，从初期的“报价智能填写”，到未来的“市场数据分析”、“风险事件预警”、“财报智能提取”和“自然语言交互”等，都将通过此微服务提供标准化的API接口供主业务系统调用。

### 1.2. 核心设计目标

- **高内聚、低耦合**: 将AI相关的复杂逻辑（如Prompt工程、模型调用、输出解析）封装在单一服务内，主业务系统只需关注业务流程和API调用。
- **技术栈隔离**: 允许AI服务采用最适合其任务的技术栈（Python生态），而不影响主业务系统的技术选型（Go）。
- **可扩展性**: 架构设计必须支持未来快速、低成本地集成新的AI功能和模型，而无需改动现有核心框架。
- **可维护性**: 清晰的分层和模块化设计，使得代码易于理解、测试和维护。
- **统一管理**: 集中管理API密钥、模型配置、Prompt版本和AI服务成本，便于监控和优化。

---

## 2. 技术栈选型

| 分类 | 技术/库 | 用途与说明 |
| :--- | :--- | :--- |
| **语言** | Python 3.10+ | 主流AI/ML开发语言，拥有最丰富的生态系统。 |
| **Web框架** | FastAPI | 构建高性能、异步的API接口，自带Swagger文档，与Pydantic无缝集成。 |
| **AI框架** | LangChain | 核心AI编排框架，用于管理Prompt、链接LLM、处理输出，极大提升开发效率。 |
| **数据校验** | Pydantic | 定义结构化的数据模型（请求/响应体），确保API接口的类型安全和数据一致性。 |
| **包管理** | UV | 新一代的Python包管理器，提供极速的依赖解析和安装体验。 |
| **LLM供应商** | OpenAI / Azure / Anthropic | 可通过LangChain轻松切换，初期建议使用支持JSON模式的`gpt-4-turbo`或`claude-3-opus`。 |

---

## 3. 系统架构设计

服务采用经典的分层架构，确保职责清晰、易于扩展。

### 3.1. 项目目录结构

```
ai_gateway_service/
├── api/
│   └── v1/
│       ├── __init__.py
│       ├── router.py           # 主API路由，聚合所有功能模块的端点
│       └── endpoints/
│           ├── __init__.py
│           ├── quotations.py     # 【当前】报价处理模块
│           └── data_analysis.py  # 【未来】数据分析模块
├── core/
│   ├── __init__.py
│   ├── config.py             # 配置管理 (Pydantic-Settings)
│   └── logging.py            # 日志配置 (Loguru)
├── services/
│   ├── __init__.py
│   └── quotation_parser.py    # 【当前】报价解析服务，封装核心业务逻辑
├── schemas/
│   ├── __init__.py
│   └── quotation.py          # 【当前】报价相关的Pydantic数据模型
├── prompts/
│   ├── __init__.py
│   └── quotation_prompts.py    # 【当前】报价解析的Prompt模板
├── .env                      # 环境变量 (API Keys, 模型名称等)
├── main.py                   # FastAPI应用入口
├── pyproject.toml            # 项目元数据与依赖管理 (uv)
└── README.md                 # 项目说明
```

### 3.2. 架构分层职责

- **`api/` (API层)**: 负责处理HTTP请求和响应。定义API路径、参数和响应模型，将请求转发给服务层。不包含任何业务逻辑。
- **`services/` (服务层)**: 核心业务逻辑层。编排LangChain调用链，处理数据，执行具体的AI任务。
- **`schemas/` (模型层)**: 使用Pydantic定义所有结构化数据，包括API的请求/响应体和LangChain的输出模型。
- **`prompts/` (提示词层)**: 存放所有Prompt模板。将Prompt与代码分离，便于非开发人员（如产品经理）进行优化和版本管理。
- **`core/` (核心层)**: 应用的基础设施，如配置加载、日志系统初始化等。
- **`main.py` (入口层)**: FastAPI应用的启动入口，负责装载配置、路由和中间件。

---

## 4. API设计与路由策略

### 4.1. 统一路由管理

所有API都将通过版本化的路由进行管理（`/api/v1/...`），并按功能域进行模块化划分。

- **主路由 (`api/v1/router.py`)**: 聚合所有功能模块的子路由，是API的统一入口点。

  ```python
  from fastapi import APIRouter
  from .endpoints import quotations

  api_router = APIRouter()
  api_router.include_router(quotations.router, prefix="/quotations", tags=["Quotations"])
  
  # 未来新增功能时，只需在此处添加一行
  # from .endpoints import data_analysis
  # api_router.include_router(data_analysis.router, prefix="/data", tags=["Data Analysis"])
  ```

### 4.2. 未来接口扩展规划

| 功能域 | HTTP 方法 | 规划路径 | 描述 |
| :--- | :--- | :--- | :--- |
| **报价 (Quotations)** | `POST` | `/api/v1/quotations/parse` | **（当前实现）** 从文本解析报价信息。 |
| **数据分析 (Data)** | `POST` | `/api/v1/data/summarize` | **（未来）** 对给定的结构化数据生成自然语言摘要。 |
| **文档处理 (Docs)** | `POST` | `/api/v1/documents/extract` | **（未来）** 从PDF或图片中提取关键信息。 |
| **交互式问答 (Chat)** | `POST` | `/api/v1/chat` | **（未来）** 提供一个与系统知识库交互的聊天接口。 |

---

## 5. 初始功能实现案例：报价智能填写

以下是如何在上述框架下实现第一个功能的具体步骤。

1.  **定义数据模型 (`schemas/quotation.py`)**
    - 定义API请求体 `QuotationParseRequest` 和响应体 `ParsedQuotationData`。

2.  **创建Prompt模板 (`prompts/quotation_prompts.py`)**
    - 将指导LLM进行解析的提示词文本定义为常量，与业务代码分离。

3.  **实现服务逻辑 (`services/quotation_parser.py`)**
    - 引入LangChain的 `PydanticOutputParser`，并与Pydantic模型绑定。
    - 创建 `PromptTemplate`，加载模板并注入解析器的格式指令。
    - 初始化 `ChatOpenAI` 模型。
    - 使用 `|` 操作符构建 `prompt | model | parser` 的调用链。
    - 封装成一个异步函数 `parse_quotation_text` 供API层调用。

4.  **暴露API端点 (`api/v1/endpoints/quotations.py`)**
    - 创建一个FastAPI的 `APIRouter`。
    - 定义 `POST /parse` 路径，使用上一步定义的Pydantic模型作为请求体和响应模型。
    - 在函数体内调用服务层的 `parse_quotation_text` 方法并返回结果。

---

## 6. 基础设施与运维 (Infrastructure & DevOps)

- **依赖管理**: 项目根目录的 `pyproject.toml` 将作为唯一的依赖声明文件。使用 `uv pip install -r requirements.txt` 或 `uv pip sync` 进行环境管理。
- **配置管理**: 在 `core/config.py` 中使用 `pydantic-settings` 库。它能从 `.env` 文件、系统环境变量等多种来源加载配置，并自动进行类型校验。API密钥等敏感信息严禁硬编码，必须通过环境变量注入。
- **容器化**: 提供一个 `Dockerfile`，将FastAPI服务打包成轻量级的Docker镜像，便于在任何环境中一致地部署。
- **CI/CD**: 建议为该服务建立独立的CI/CD流水线，流程包括：安装依赖 -> 运行测试 -> 构建Docker镜像 -> 推送至镜像仓库 -> 部署到目标环境。

---

## 7. 总结

此AI网关微服务框架方案提供了一个清晰、健壮且高度可扩展的平台。通过将AI能力集中化、服务化，不仅降低了主业务系统的复杂性，也为未来快速集成更多、更强大的AI功能铺平了道路，是支撑项目长期智能化发展的关键基础设施。
