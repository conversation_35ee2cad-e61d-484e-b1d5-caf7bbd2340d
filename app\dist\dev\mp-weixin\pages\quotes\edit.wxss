/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.app-container.data-v-363e46db {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom, 0px));
}
.loading-container.data-v-363e46db {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}
.form-container.data-v-363e46db {
  padding: 0 var(--app-spacing-sm) var(--app-spacing-xl);
  overflow-y: auto;
}
.data-v-363e46db .form-section {
  margin-bottom: var(--app-spacing-sm);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
}
.price-unit.data-v-363e46db {
  color: var(--app-text-secondary);
  font-size: var(--app-font-size-xs);
  margin-left: var(--app-spacing-xs);
}
.form-actions.data-v-363e46db {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: var(--app-spacing-sm);
  padding: var(--app-spacing-sm);
  padding-bottom: calc(var(--app-spacing-sm) + env(safe-area-inset-bottom, 0px));
  background: var(--app-bg-primary);
  border-top: 1rpx solid var(--app-border-secondary);
  box-shadow: var(--app-shadow-lg);
  z-index: var(--app-z-fixed);
}
.form-actions.data-v-363e46db .wd-button {
  flex: 1;
  height: 88rpx;
  border-radius: var(--app-radius-md);
}
.data-v-363e46db  .btn-draft {
  background: var(--app-bg-secondary) !important;
  border: 2rpx solid var(--app-border-primary) !important;
  color: var(--app-text-primary) !important;
  font-weight: var(--app-font-weight-medium) !important;
  border-radius: var(--app-radius-lg) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  box-shadow: none !important;
}
.data-v-363e46db  .btn-draft:hover {
  background: var(--app-bg-tertiary) !important;
  border-color: var(--app-color-primary) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: var(--app-shadow-sm) !important;
}
.data-v-363e46db  .btn-draft:active {
  transform: translateY(0) !important;
  box-shadow: none !important;
}
.data-v-363e46db  .btn-draft:disabled {
  opacity: 0.6 !important;
  transform: none !important;
}
.data-v-363e46db  .btn-publish {
  background: linear-gradient(135deg, var(--app-color-success), #059669) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  font-weight: var(--app-font-weight-semibold) !important;
  border-radius: var(--app-radius-lg) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  box-shadow: var(--app-shadow-base) !important;
}
.data-v-363e46db  .btn-publish:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: var(--app-shadow-md) !important;
}
.data-v-363e46db  .btn-publish:active {
  transform: translateY(0) !important;
  box-shadow: var(--app-shadow-base) !important;
}
.data-v-363e46db  .btn-publish:disabled {
  opacity: 0.6 !important;
  transform: none !important;
}
.data-v-363e46db  .trade-intention-radio-group {
  display: flex;
  align-items: center;
  gap: var(--app-spacing-lg);
}
.data-v-363e46db  .trade-intention-radio-group .trade-intention-radio {
  font-size: var(--app-font-size-sm);
}
.data-v-363e46db  .trade-intention-radio-group .trade-intention-radio :deep(.wd-radio) {
  display: flex !important;
  align-items: center !important;
  gap: var(--app-spacing-xs);
  flex-direction: row !important;
}
.data-v-363e46db  .trade-intention-radio-group .trade-intention-radio :deep(.wd-radio__icon) {
  flex-shrink: 0;
  align-self: center;
  margin: 0 !important;
}
.data-v-363e46db  .trade-intention-radio-group .trade-intention-radio :deep(.wd-radio__label) {
  padding: var(--app-spacing-xs) var(--app-spacing-sm);
  border-radius: var(--app-radius-sm);
  transition: all var(--app-duration-fast) var(--app-ease-out);
  display: flex;
  align-items: center;
  line-height: var(--app-line-height-tight);
}
.data-v-363e46db  .trade-intention-radio-group .trade-intention-radio :deep(.wd-radio--checked) .wd-radio__label {
  background-color: rgba(59, 130, 246, 0.1);
  color: var(--app-color-primary);
  font-weight: var(--app-font-weight-semibold);
}
.data-v-363e46db  .price-type-section {
  padding: 0 var(--app-spacing-base);
}
.data-v-363e46db  .price-type-section .price-type-tabs :deep(.wd-tabs__nav) {
  background-color: var(--app-bg-secondary);
  border-radius: var(--app-radius-md);
  padding: var(--app-spacing-xs);
  margin-bottom: 0;
}
.data-v-363e46db  .price-type-section .price-type-tabs :deep(.wd-tab) {
  flex: 1;
  border-radius: var(--app-radius-sm);
  transition: all var(--app-duration-base) var(--app-ease-out);
  font-size: var(--app-font-size-sm);
  font-weight: var(--app-font-weight-medium);
}
.data-v-363e46db  .price-type-section .price-type-tabs :deep(.wd-tab).is-active {
  background-color: var(--app-color-primary);
  color: var(--app-text-inverse);
  font-weight: var(--app-font-weight-semibold);
}
.data-v-363e46db  .price-type-section .price-type-tabs :deep(.wd-tabs__line) {
  display: none;
}
.data-v-363e46db  .price-type-section .price-type-tabs :deep(.wd-tabs__content) {
  display: none;
}
.data-v-363e46db  .wd-textarea__count {
  z-index: 1 !important;
}