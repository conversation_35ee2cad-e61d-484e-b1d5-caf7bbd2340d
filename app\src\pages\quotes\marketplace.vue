<route lang="jsonc" type="page">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "报价市场",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { getPublicQuotationList } from '@/api/quotation'
import QuotationCard from '@/components/marketplace/QuotationCard.vue'
import CommoditySectionSelector from '@/components/CommoditySectionSelector.vue'
import type {
  IQuotationResponse,
  IQuotationListRequest,
  QuotationPriceType
} from '@/types/quotation'
import { navigateToPage } from '@/utils'
import { useCommodityStore } from '@/store/commodity'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'QuotationMarketplace'
})

// ==================== Store 实例 ====================
const commodityStore = useCommodityStore()
const userStore = useUserStore()

// ==================== 响应式数据声明 ====================

// 页面状态管理
const isLoading = ref<boolean>(false)
const isRefreshing = ref<boolean>(false)
const hasMore = ref<boolean>(true)

// 搜索和筛选状态
const searchKeyword = ref<string>('')
const showFilterPanel = ref<boolean>(false)
const selectedPriceType = ref<QuotationPriceType | ''>('')
const selectedTradeType = ref<'buy' | 'sell' | ''>('')

// 品种选择相关状态
const showCommoditySelector = ref<boolean>(false)
const isFirstTimeUser = ref<boolean>(false)

// ActionSheet交互状态
const showActionSheet = ref<boolean>(false)
const selectedQuotation = ref<IQuotationResponse | null>(null)

// 数据存储
const quotationList = ref<IQuotationResponse[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

// 静态配置数据
const priceTypeOptions = [
  { label: '全部类型', value: '' },
  { label: '一口价', value: 'Fixed' },
  { label: '基差报价', value: 'Basis' },
  { label: '商议', value: 'Negotiable' }
] as const

const tradeTypeOptions = [
  { label: '全部', value: '' },
  { label: '求购', value: 'buy' },
  { label: '出售', value: 'sell' }
] as const

// ==================== 计算属性 ====================

const hasActiveFilters = computed(() => {
  return selectedPriceType.value || selectedTradeType.value || searchKeyword.value.trim()
})

// 检查是否有关注的品种
const hasFavoriteCommodities = computed(() => {
  return userStore.favoriteCommodities.length > 0
})

// ==================== 数据获取相关函数 ====================


/**
 * 加载报价列表
 * @param refresh 是否刷新（重置分页）
 */
async function loadQuotationList(refresh: boolean = false): Promise<void> {
  if (refresh) {
    currentPage.value = 1
    quotationList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // 添加筛选条件
    if (selectedPriceType.value) {
      params.priceType = selectedPriceType.value
    }
    if (selectedTradeType.value) {
      params.isBuyRequest = selectedTradeType.value === 'buy'
    }
    if (searchKeyword.value.trim()) {
      params.keyword = searchKeyword.value.trim()
    }
    
    // 添加用户关注品种筛选
    if (hasFavoriteCommodities.value) {
      params.favoriteCommodities = userStore.favoriteCommodities
    }

    const res = await getPublicQuotationList(params)
    const { list, total: totalCount } = res.data

    if (refresh) {
      quotationList.value = list
    } else {
      quotationList.value.push(...list)
    }

    total.value = totalCount
    hasMore.value = quotationList.value.length < totalCount

  } catch (error) {
    console.error('加载报价列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

/**
 * 加载更多数据
 */
async function loadMore(): Promise<void> {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadQuotationList()
}

/**
 * 下拉刷新
 */
async function onRefresh(): Promise<void> {
  await loadQuotationList(true)
}

// ==================== 搜索和筛选相关函数 ====================

/**
 * 执行搜索
 */
async function onSearch(): Promise<void> {
  await loadQuotationList(true)
}

/**
 * 清空搜索关键词
 */
async function clearSearch(): Promise<void> {
  searchKeyword.value = ''
  await loadQuotationList(true)
}

/**
 * 显示筛选面板
 */
function showFilters(): void {
  showFilterPanel.value = true
}


/**
 * 价格类型筛选
 * @param priceType 价格类型
 */
async function onPriceTypeFilter(priceType: QuotationPriceType | ''): Promise<void> {
  selectedPriceType.value = priceType
  showFilterPanel.value = false
  await loadQuotationList(true)
}

/**
 * 交易类型筛选
 * @param tradeType 交易类型
 */
async function onTradeTypeFilter(tradeType: 'buy' | 'sell' | ''): Promise<void> {
  selectedTradeType.value = tradeType
  showFilterPanel.value = false
  await loadQuotationList(true)
}

/**
 * 清空所有筛选条件
 */
async function clearFilters(): Promise<void> {
  selectedPriceType.value = ''
  selectedTradeType.value = ''
  searchKeyword.value = ''
  showFilterPanel.value = false
  await loadQuotationList(true)
}

/**
 * 显示品种选择器
 */
function showCommoditySelectorDialog(): void {
  showCommoditySelector.value = true
}

/**
 * 处理品种选择确认
 */
async function handleCommoditySelectionConfirm(selectedCommodities: string[]): Promise<void> {
  showCommoditySelector.value = false

  // 保存用户选择的关注品种
  try {
    await userStore.updateFavoriteCommodities(selectedCommodities)
  } catch (error) {
    console.error('保存关注品种失败:', error)
  }

  // 刷新报价列表
  await loadQuotationList(true)
}

/**
 * 处理品种选择取消
 */
function handleCommoditySelectionCancel(): void {
  showCommoditySelector.value = false
}

/**
 * 初始化用户关注品种
 */
async function initUserFavorites(): Promise<void> {
  try {
    await userStore.loadFavoriteCommodities()
    
    // 检查是否为首次用户（没有关注任何品种）
    if (userStore.isFirstTimeUser) {
      // 首次用户自动显示品种选择器
      showCommoditySelector.value = true
    }
  } catch (error) {
    console.error('初始化用户关注品种失败:', error)
  }
}

// ==================== 交互处理函数 ====================

// ActionSheet操作选项配置
const actionSheetOptions = [
  { name: '查看详情', value: 'detail' },
  { name: '发起点价', value: 'contact' }
]

/**
 * 显示报价操作菜单
 * @param quotation 选中的报价
 */
function showQuotationActions(quotation: IQuotationResponse): void {
  selectedQuotation.value = quotation
  showActionSheet.value = true
}

/**
 * 处理ActionSheet选择
 * @param item 选中的操作项
 */
function handleActionSelect({ item }: { item: { name: string; value: string } }): void {
  showActionSheet.value = false

  if (!selectedQuotation.value) return

  switch (item.value) {
    case 'detail':
      viewDetail(selectedQuotation.value)
      break
    case 'contact':
      contactPublisher(selectedQuotation.value)
      break
  }

  selectedQuotation.value = null
}

/**
 * 查看报价详情
 * @param quotation 报价对象
 */
function viewDetail(quotation: IQuotationResponse): void {
  navigateToPage({
    url: `/pages/quotes/detail?id=${quotation.id}&from=marketplace`
  })
}

/**
 * 联系发布者/发起点价
 * @param _quotation 报价对象（暂未使用）
 */
function contactPublisher(_quotation: IQuotationResponse): void {
  // TODO: 实现联系功能，可能是发起点价请求
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

/**
 * 处理发布者点击事件，跳转到用户报价主页
 * @param userID 用户ID
 */
function handlePublisherClick(userID: number): void {
  navigateToPage({
    url: `/pages/quotes/public-list?id=${userID}`
  })
}

// ==================== 格式化和工具函数 ====================
// 注意：报价卡片相关的格式化函数已迁移到 QuotationCard.vue 组件中

// ==================== 页面生命周期函数 ====================

/**
 * 页面挂载时初始化数据
 */
onMounted(async () => {
  // 并行加载商品列表和用户关注品种
  await Promise.all([
    commodityStore.loadCommodityList(),
    initUserFavorites()
  ])
  
  // 然后加载报价列表
  await loadQuotationList()
})
</script>

<template>
  <view class="app-container">
    <view class="app-content">
      <!-- 搜索栏 -->
      <view class="glass-card mb-4 p-4 animate-slide-up">
        <view class="flex-between gap-3">
          <view class="flex-1">
            <wd-input
              v-model="searchKeyword"
              placeholder="搜索报价标题、企业名称..."
              clearable
              custom-class="input-base"
              @confirm="onSearch"
              @clear="clearSearch"
            >
              <template #prefix>
                <wd-icon name="search" size="32rpx" custom-class="text-primary-600" />
              </template>
            </wd-input>
          </view>

          <view class="flex gap-2">
            <view class="btn-ghost" @click="showFilters">
              <wd-icon name="filter" size="32rpx" custom-class="text-primary-600" />
              <text class="text-caption">筛选</text>
              <view v-if="hasActiveFilters" class="w-2 h-2 bg-error-500 rounded-full absolute top-1 right-1"></view>
            </view>

            <view class="btn-ghost" @click="showCommoditySelectorDialog">
              <wd-icon name="menu" size="32rpx" custom-class="text-primary-600" />
              <text class="text-caption">品种</text>
              <text v-if="hasFavoriteCommodities" class="text-caption text-primary-600">({{ userStore.favoriteCommodities.length }})</text>
              <view v-if="hasFavoriteCommodities" class="w-2 h-2 bg-success-500 rounded-full absolute top-1 right-1"></view>
            </view>
          </view>
        </view>

        <!-- 活跃筛选标签 -->
        <view v-if="hasActiveFilters || hasFavoriteCommodities" class="flex flex-wrap gap-2 mt-3">
          <view v-if="selectedPriceType" class="app-card px-3 py-1 bg-primary-500 text-white text-caption rounded-full flex items-center gap-1">
            {{ priceTypeOptions.find(opt => opt.value === selectedPriceType)?.label }}
            <wd-icon name="close" size="20rpx" custom-class="text-white" @click="onPriceTypeFilter('')" />
          </view>
          <view v-if="selectedTradeType" class="app-card px-3 py-1 bg-primary-500 text-white text-caption rounded-full flex items-center gap-1">
            {{ tradeTypeOptions.find(opt => opt.value === selectedTradeType)?.label }}
            <wd-icon name="close" size="20rpx" custom-class="text-white" @click="onTradeTypeFilter('')" />
          </view>
        </view>
      </view>

      <!-- 列表内容 -->
      <scroll-view 
        class="flex-1"
        scroll-y
        refresher-enabled
        :refresher-triggered="isRefreshing"
        @refresherrefresh="onRefresh"
        @scrolltolower="loadMore"
      >
        <view class="space-y-4">
          <!-- 报价列表 -->
          <view v-if="quotationList.length > 0">
            <QuotationCard
              v-for="quotation in quotationList"
              :key="quotation.id"
              :quotation="quotation"
              @click="showQuotationActions"
              @publisherClick="handlePublisherClick"
            />
          </view>
          
          <!-- 空状态 -->
          <view v-else-if="!isLoading" class="app-card flex flex-col items-center justify-center py-12 text-center">
            <text class="app-text-caption text-gray-500">
              {{ hasActiveFilters ? '没有找到匹配的报价' : '市场暂无报价' }}
            </text>
            <wd-button
              v-if="hasActiveFilters"
              type="primary"
              custom-class="btn-primary mt-4"
              @click="clearFilters"
            >
              清空筛选条件
            </wd-button>
          </view>

          <!-- 加载更多 -->
          <view v-if="isLoading && quotationList.length > 0" class="app-card flex items-center justify-center py-8">
            <wd-loading size="24rpx" custom-class="text-primary-600" />
            <text class="app-text-caption text-primary-600 ml-2">加载中...</text>
          </view>

          <!-- 没有更多 -->
          <view v-if="!hasMore && quotationList.length > 0" class="app-card flex items-center justify-center py-8">
            <text class="app-text-caption text-gray-500">没有更多报价了</text>
          </view>
        </view>
      </scroll-view>

      <!-- 操作菜单 -->
      <wd-action-sheet
        v-model="showActionSheet"
        :actions="actionSheetOptions"
        cancel-text="取消"
        custom-class="action-sheet"
        :z-index="1000"
        @select="handleActionSelect"
      />

      <!-- 筛选面板 -->
      <wd-popup
        v-model="showFilterPanel"
        position="right"
        :z-index="999"
        custom-style="width: 75vw; height: 100vh;"
        custom-class="filter-popup"
      >
        <view class="app-card h-full flex flex-col">
          <view class="app-card-header flex-between">
            <text class="app-text-subtitle">筛选条件</text>
            <wd-button type="text" custom-class="text-primary-600" @click="clearFilters">重置</wd-button>
          </view>

          <view class="app-card-body flex-1 space-y-6">
            <!-- 价格类型筛选 -->
            <view>
              <text class="app-text-body font-medium mb-3 block">价格类型</text>
              <view class="flex flex-wrap gap-2">
                <wd-tag
                  v-for="option in priceTypeOptions"
                  :key="option.value"
                  :type="selectedPriceType === option.value ? 'primary' : 'default'"
                  custom-class="transition-all hover:scale-105"
                  @click="onPriceTypeFilter(option.value as any)"
                >
                  {{ option.label }}
                </wd-tag>
              </view>
            </view>

            <!-- 交易类型筛选 -->
            <view>
              <text class="app-text-body font-medium mb-3 block">交易类型</text>
              <view class="flex flex-wrap gap-2">
                <wd-tag
                  v-for="option in tradeTypeOptions"
                  :key="option.value"
                  :type="selectedTradeType === option.value ? 'primary' : 'default'"
                  custom-class="transition-all hover:scale-105"
                  @click="onTradeTypeFilter(option.value as any)"
                >
                  {{ option.label }}
                </wd-tag>
              </view>
            </view>
          </view>
        </view>
      </wd-popup>

      <!-- 品种选择器 -->
      <CommoditySectionSelector
        v-model:visible="showCommoditySelector"
        @confirm="handleCommoditySelectionConfirm"
        @cancel="handleCommoditySelectionCancel"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 页面特定的样式补充
// 大部分样式已通过 UnoCSS shortcuts 和全局样式系统处理

// 搜索栏固定定位增强
.glass-card {
  position: sticky;
  top: 0;
  z-index: 100;
}

// 按钮交互增强
.btn-ghost {
  position: relative;
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:active {
    transform: scale(0.95);
  }
}

// 筛选标签动画
.app-card.px-3.py-1 {
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:active {
    transform: scale(0.95);
  }
}

// 列表项悬停效果
:deep(.quotation-card) {
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:active {
    transform: scale(0.98);
  }
}

// 标签交互效果
:deep(.wd-tag) {
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:active {
    transform: scale(0.95);
  }
}
</style>
