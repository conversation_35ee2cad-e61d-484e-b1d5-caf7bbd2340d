package dianjia

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/dianjia"
	dianjiaService "github.com/flipped-aurora/gin-vue-admin/server/service/dianjia"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type InstrumentApi struct{}

var instrumentService = dianjiaService.InstrumentService{}

// CreateInstrument 创建期货合约
// @Tags Instrument
// @Summary 创建期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.InstrumentRequest true "期货合约信息"
// @Success 200 {object} response.Response{data=dianjia.Instrument,msg=string} "创建成功"
// @Router /instrument/createInstrument [post]
func (instrumentApi *InstrumentApi) CreateInstrument(c *gin.Context) {
	var req dianjia.InstrumentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = utils.Verify(req, utils.InstrumentVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	instrument, err := instrumentService.CreateInstrument(req)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithDetailed(instrument, "创建成功", c)
}

// DeleteInstrument 删除期货合约
// @Tags Instrument
// @Summary 删除期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /instrument/deleteInstrument [delete]
func (instrumentApi *InstrumentApi) DeleteInstrument(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	err = instrumentService.DeleteInstrument(uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败", c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteInstrumentByIds 批量删除期货合约
// @Tags Instrument
// @Summary 批量删除期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除期货合约"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /instrument/deleteInstrumentByIds [delete]
func (instrumentApi *InstrumentApi) DeleteInstrumentByIds(c *gin.Context) {
	IDs := c.QueryArray("IDs[]")
	var ids []uint
	for _, id := range IDs {
		ID, err := strconv.Atoi(id)
		if err != nil {
			response.FailWithMessage("ID格式错误", c)
			return
		}
		ids = append(ids, uint(ID))
	}
	err := instrumentService.DeleteInstrumentByIds(ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败", c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateInstrument 更新期货合约
// @Tags Instrument
// @Summary 更新期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body dianjia.InstrumentRequest true "期货合约信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /instrument/updateInstrument [put]
func (instrumentApi *InstrumentApi) UpdateInstrument(c *gin.Context) {
	var req dianjia.InstrumentRequest
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(req, utils.InstrumentVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	err = instrumentService.UpdateInstrument(req)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindInstrument 用id查询期货合约
// @Tags Instrument
// @Summary 用id查询期货合约
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.Instrument true "用id查询期货合约"
// @Success 200 {object} response.Response{data=dianjia.Instrument,msg=string} "查询成功"
// @Router /instrument/findInstrument [get]
func (instrumentApi *InstrumentApi) FindInstrument(c *gin.Context) {
	ID := c.Query("ID")
	id, err := strconv.Atoi(ID)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	instrument, err := instrumentService.GetInstrument(uint(id))
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败", c)
		return
	}
	response.OkWithDetailed(instrument, "查询成功", c)
}

// GetInstrumentList 分页获取期货合约列表
// @Tags Instrument
// @Summary 分页获取期货合约列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query dianjia.InstrumentListRequest true "分页获取期货合约列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /instrument/getInstrumentList [get]
func (instrumentApi *InstrumentApi) GetInstrumentList(c *gin.Context) {
	var pageInfo dianjia.InstrumentListRequest
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	if pageInfo.PageSize == 0 {
		pageInfo.PageSize = 10
	}
	if pageInfo.Page == 0 {
		pageInfo.Page = 1
	}

	list, total, err := instrumentService.GetInstrumentInfoList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// GetInstrumentSelectList 获取期货合约选择器列表
// @Tags Instrument
// @Summary 获取期货合约选择器列表
// @accept application/json
// @Produce application/json
// @Param data query dianjia.InstrumentSelectRequest true "期货合约选择器查询条件"
// @Success 200 {object} response.Response{data=[]dianjia.InstrumentSelectResponse,msg=string} "获取成功"
// @Router /instrument/getInstrumentSelectList [get]
func (instrumentApi *InstrumentApi) GetInstrumentSelectList(c *gin.Context) {
	var req dianjia.InstrumentSelectRequest
	err := c.ShouldBindQuery(&req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, err := instrumentService.GetInstrumentSelectList(req)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}

// GetInstrumentsByExchange 获取按交易所分组的期货合约列表
// @Tags Instrument
// @Summary 获取按交易所分组的期货合约列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=map[string]map[string][]dianjia.InstrumentSelectResponse,msg=string} "获取成功"
// @Router /instrument/getInstrumentsByExchange [get]
func (instrumentApi *InstrumentApi) GetInstrumentsByExchange(c *gin.Context) {
	result, err := instrumentService.GetInstrumentsByExchange()
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(result, "获取成功", c)
}
