"""
Prompt templates for quotation parsing functionality
"""

PARSE_QUOTATION_PROMPT = """
你是一个专业的大宗商品交易助理，擅长从非结构化文本中提取关键的报价信息。

请从以下文本中提取报价的关键信息，并严格按照指定的JSON格式返回。
注意：字段名必须与前端表单完全一致（使用驼峰命名法）。

【重要规则】：
1. priceType 字段只能是 "Fixed" 或 "Basis"：
   - 如果文本提到"盘面+XX"、"升水XX"、"贴水XX"、"基差+XX"等，设为 "Basis"
   - 如果文本提到"一口价"、"XX元/吨"等固定价格，设为 "Fixed"
   
2. 必须提取的核心字段：
   - title: 为报价生成简洁的标题
   - commodityName: 商品名称（如螺纹钢、铜、豆粕等）
   - deliveryLocation: 交货地点
   - priceType: 报价方式
   - price: 价格或基差值
   
3. status 字段默认为 "Draft"（草稿状态）

4. expiresAt 字段处理：
   - 根据有效期天数计算具体的过期时间
   - 返回ISO格式的时间字符串（如"2025-08-09T23:59:59.000Z"）

{format_instructions}

用户输入的文本:
{user_text}

请严格按照JSON格式返回，不要包含任何额外的解释或说明。
"""

EXAMPLE_QUOTATIONS = [
    {
        "input": "上海地区仓库自提，中天螺纹钢，规格HRB400E 20mm，500吨，价格比RB2501盘面高120，过磅含税，3天内有效。联系王经理。",
        "expected_output": {
            "title": "中天螺纹钢基差报价",
            "commodityName": "螺纹钢",
            "deliveryLocation": "上海地区仓库",
            "brand": "中天",
            "specifications": "HRB400E 20mm",
            "description": "过磅含税，联系王经理",
            "priceType": "Basis",
            "price": 120,
            "instrumentRefID": None,
            "instrumentRef": None,
            "expiresAt": "2025-08-09T23:59:59.000Z",
            "status": "Draft"
        }
    },
    {
        "input": "铝锭现货，一口价18000元/吨，品牌忠旺，99.7%纯度，杭州提货，50吨起订",
        "expected_output": {
            "title": "忠旺铝锭现货报价",
            "commodityName": "铝锭",
            "deliveryLocation": "杭州",
            "brand": "忠旺",
            "specifications": "99.7%纯度",
            "description": "50吨起订",
            "priceType": "Fixed",
            "price": 18000,
            "instrumentRefID": None,
            "instrumentRef": None,
            "expiresAt": None,
            "status": "Draft"
        }
    }
]