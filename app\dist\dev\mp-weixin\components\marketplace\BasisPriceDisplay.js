"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const store_market = require("../../store/market.js");
if (!Array) {
  const _component_transition = common_vendor.resolveComponent("transition");
  _component_transition();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "BasisPriceDisplay"
}), {
  __name: "BasisPriceDisplay",
  props: {
    instrumentId: {},
    exchangeId: {},
    price: {},
    showPrice: { type: Boolean, default: false }
  },
  setup(__props) {
    const props = __props;
    const marketStore = store_market.useMarketStore();
    const futuresPrice = common_vendor.ref(null);
    const fetchingPrice = common_vendor.ref(false);
    const priceError = common_vendor.ref(false);
    const showFlash = common_vendor.ref(false);
    function getFuturesPrice(instrumentId) {
      try {
        const marketData = marketStore.getMarketData(instrumentId);
        if (marketData && marketData.last_price) {
          const price = parseFloat(marketData.last_price);
          return isNaN(price) ? null : price;
        }
        return null;
      } catch (error) {
        console.error("获取期货价格失败:", error);
        return null;
      }
    }
    function subscribeAndGetPrice() {
      if (!props.instrumentId || !props.exchangeId) {
        priceError.value = true;
        fetchingPrice.value = false;
        return;
      }
      try {
        const existingPrice = getFuturesPrice(props.instrumentId);
        if (existingPrice !== null) {
          futuresPrice.value = existingPrice;
          priceError.value = false;
          fetchingPrice.value = false;
          return;
        }
        fetchingPrice.value = true;
        priceError.value = false;
        marketStore.subscribe(props.instrumentId, props.exchangeId);
        setTimeout(() => {
          if (fetchingPrice.value) {
            const price = getFuturesPrice(props.instrumentId);
            if (price !== null) {
              futuresPrice.value = price;
              priceError.value = false;
            } else {
              priceError.value = true;
            }
            fetchingPrice.value = false;
          }
        }, 5e3);
      } catch (error) {
        console.error("订阅期货价格失败:", error);
        fetchingPrice.value = false;
        priceError.value = true;
      }
    }
    common_vendor.watch(
      () => [props.showPrice, props.instrumentId],
      ([showPrice, instrumentId]) => {
        showFlash.value = true;
        setTimeout(() => {
          showFlash.value = false;
        }, 600);
        if (showPrice && instrumentId) {
          subscribeAndGetPrice();
        } else {
          futuresPrice.value = null;
          fetchingPrice.value = false;
          priceError.value = false;
        }
      },
      { immediate: true }
    );
    common_vendor.watch(
      () => marketStore.marketData,
      (marketData) => {
        if (props.showPrice && props.instrumentId) {
          const price = getFuturesPrice(props.instrumentId);
          if (price !== null) {
            futuresPrice.value = price;
            priceError.value = false;
            fetchingPrice.value = false;
          }
        }
      },
      { deep: true }
    );
    const finalPrice = common_vendor.computed(() => {
      if (props.showPrice && futuresPrice.value !== null) {
        return futuresPrice.value + props.price;
      }
      return null;
    });
    function formatLargeNumber(num) {
      const absNum = Math.abs(num);
      const sign = num < 0 ? "-" : "";
      if (absNum < 1e4) {
        return sign + absNum.toLocaleString();
      }
      if (absNum < 1e8) {
        const wan = absNum / 1e4;
        if (wan >= 100) {
          return sign + Math.round(wan) + "万";
        } else {
          return sign + wan.toFixed(1) + "万";
        }
      }
      const yi = absNum / 1e8;
      if (yi >= 100) {
        return sign + Math.round(yi) + "亿";
      } else {
        return sign + yi.toFixed(1) + "亿";
      }
    }
    function formatBasisPrice(price) {
      const formatted = formatLargeNumber(Math.abs(price));
      return price >= 0 ? `+${formatted}` : `-${formatted}`;
    }
    function calculateDynamicFontSize(text, baseSize = 48, minSize = 28) {
      const textLength = text.length;
      if (textLength <= 6)
        return baseSize;
      if (textLength <= 8)
        return Math.max(baseSize * 0.9, minSize);
      if (textLength <= 10)
        return Math.max(baseSize * 0.8, minSize);
      return minSize;
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: !_ctx.showPrice
      }, !_ctx.showPrice ? {
        b: common_vendor.t(_ctx.instrumentId),
        c: common_vendor.t(formatBasisPrice(_ctx.price)),
        d: calculateDynamicFontSize(formatBasisPrice(_ctx.price), 42, 26) + "rpx",
        e: _ctx.price >= 0,
        f: _ctx.price < 0,
        g: (_ctx.price >= 0 ? "+" : "") + _ctx.price.toLocaleString()
      } : common_vendor.e({
        h: fetchingPrice.value
      }, fetchingPrice.value ? {} : priceError.value || finalPrice.value === null ? {} : {
        j: common_vendor.t(formatLargeNumber(finalPrice.value)),
        k: calculateDynamicFontSize(formatLargeNumber(finalPrice.value), 42, 26) + "rpx",
        l: finalPrice.value.toLocaleString()
      }, {
        i: priceError.value || finalPrice.value === null
      }), {
        m: common_vendor.p({
          name: "price-switch",
          mode: "out-in"
        }),
        n: showFlash.value ? 1 : ""
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-80bbf42c"]]);
wx.createComponent(Component);
