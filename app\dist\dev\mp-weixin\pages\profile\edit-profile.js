"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_toast = require("../../utils/toast.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_wd_input2 + _easycom_wd_button2 + _easycom_wd_popup2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_input + _easycom_wd_button + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "edit-profile",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const showEditPopup = common_vendor.ref(false);
    const editField = common_vendor.ref("");
    const editFieldLabel = common_vendor.ref("");
    const editValue = common_vendor.ref("");
    const saving = common_vendor.ref(false);
    common_vendor.onMounted(() => __async(this, null, function* () {
      try {
        yield userStore.getUserProfile();
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
    }));
    const editFieldHandler = (field) => {
      const fieldMap = {
        nickName: "昵称",
        companyName: "企业名称"
      };
      editField.value = field;
      editFieldLabel.value = fieldMap[field];
      editValue.value = userStore.userInfo[field] || "";
      showEditPopup.value = true;
    };
    const closeEditPopup = () => {
      showEditPopup.value = false;
      editField.value = "";
      editFieldLabel.value = "";
      editValue.value = "";
    };
    const saveEdit = () => __async(this, null, function* () {
      if (!editValue.value.trim()) {
        utils_toast.toast.error(`${editFieldLabel.value}不能为空`);
        return;
      }
      saving.value = true;
      try {
        const updateData = {
          [editField.value]: editValue.value.trim()
        };
        yield userStore.updateUserProfile(updateData);
        closeEditPopup();
        utils_toast.toast.success("保存成功");
      } catch (error) {
        console.error("保存失败:", error);
        utils_toast.toast.error("保存失败，请重试");
      } finally {
        saving.value = false;
      }
    });
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          name: "user",
          size: "32rpx",
          color: "#667eea"
        }),
        b: common_vendor.t(common_vendor.unref(userStore).userInfo.nickName || "未设置"),
        c: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        d: common_vendor.o(($event) => editFieldHandler("nickName")),
        e: common_vendor.p({
          name: "home",
          size: "32rpx",
          color: "#667eea"
        }),
        f: common_vendor.t(common_vendor.unref(userStore).userInfo.companyName || "未设置"),
        g: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        h: common_vendor.o(($event) => editFieldHandler("companyName")),
        i: common_vendor.p({
          name: "phone",
          size: "32rpx",
          color: "#667eea"
        }),
        j: common_vendor.t(common_vendor.unref(userStore).userInfo.phone || "未绑定"),
        k: common_vendor.t(editFieldLabel.value),
        l: common_vendor.o(closeEditPopup),
        m: common_vendor.p({
          name: "close",
          size: "32rpx"
        }),
        n: common_vendor.o(($event) => editValue.value = $event),
        o: common_vendor.p({
          placeholder: `请输入${editFieldLabel.value}`,
          clearable: true,
          maxlength: 50,
          modelValue: editValue.value
        }),
        p: common_vendor.o(closeEditPopup),
        q: common_vendor.p({
          type: "default",
          size: "large",
          ["custom-class"]: "cancel-btn"
        }),
        r: common_vendor.o(saveEdit),
        s: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "save-btn",
          loading: saving.value
        }),
        t: common_vendor.o(($event) => showEditPopup.value = $event),
        v: common_vendor.p({
          position: "center",
          ["close-on-click-modal"]: true,
          modelValue: showEditPopup.value
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-5d91d807"]]);
wx.createPage(MiniProgramPage);
