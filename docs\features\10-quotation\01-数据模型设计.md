# 数据模型设计

> **版本**: 7.0.0  
> **负责人**: 数据架构团队  
> **状态**: 已实现完成  
> **最后更新**: 2025-08-07

---

## 1. 数据库设计原则

### 1.1 设计原则
- **规范化**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理使用索引，优化查询性能
- **扩展性**: 预留扩展字段，支持未来功能扩展
- **一致性**: 使用外键约束，保证数据一致性
- **安全性**: 敏感数据加密，访问权限控制

### 1.2 命名规范
- 表名: 使用下划线分隔，如 `dj_quotations`
- 字段名: 使用下划线分隔，如 `user_id`
- 索引名: 使用 `idx_` 前缀，如 `idx_user_id`
- 外键名: 使用 `fk_` 前缀，如 `fk_quotation_user`

---

## 2. 已实现的核心数据表

### 2.1 报价表 (dj_quotations) ✅

```sql
CREATE TABLE dj_quotations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '报价ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 基本信息
    user_id BIGINT NOT NULL COMMENT '报价发布者的用户ID',
    title VARCHAR(255) NOT NULL COMMENT '报价的醒目标题',
    is_buy_request BOOLEAN DEFAULT FALSE COMMENT '是否为求购请求(true:求购,false:出售)',
    
    -- 商品信息
    commodity_name VARCHAR(100) NOT NULL COMMENT '商品名称',
    delivery_location VARCHAR(255) NOT NULL COMMENT '交货地点',
    brand VARCHAR(100) COMMENT '品牌',
    specifications TEXT COMMENT '规格说明',
    description TEXT COMMENT '补充说明',
    
    -- 价格信息
    price_type ENUM('Fixed', 'Basis', 'Negotiable') NOT NULL COMMENT '报价方式(Fixed/Basis/Negotiable)',
    price DECIMAL(10,2) NOT NULL COMMENT '价格或基差值(商议类型可为0)',
    instrument_ref_id BIGINT COMMENT '关联的期货合约ID(基差报价用)',
    
    -- 生命周期管理
    expires_at TIMESTAMP NOT NULL COMMENT '报价的精确过期时间',
    status ENUM('Draft', 'Active', 'Expired', 'Withdrawn') NOT NULL DEFAULT 'Draft' COMMENT '报价状态',
    
    -- 外键约束
    FOREIGN KEY fk_quotation_user (user_id) REFERENCES sys_users(id),
    FOREIGN KEY fk_quotation_instrument (instrument_ref_id) REFERENCES dj_instruments(id),
    
    -- 索引
    INDEX idx_quotations_user_id (user_id),
    INDEX idx_quotations_status (status),
    INDEX idx_quotations_expires_at (expires_at),
    INDEX idx_quotations_price_type (price_type),
    INDEX idx_quotations_created_at (created_at),
    INDEX idx_quotations_commodity_name (commodity_name),
    INDEX idx_quotations_deleted_at (deleted_at)
) COMMENT '报价表';
```

### 2.2 期货合约表 (instruments) ✅

```sql
-- 已存在
CREATE TABLE instruments (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '期货合约ID',
    instrument_id VARCHAR(50) NOT NULL COMMENT '合约代码',
    instrument_name VARCHAR(100) NOT NULL COMMENT '合约名称',
    exchange VARCHAR(50) COMMENT '交易所',
    contract_size DECIMAL(15,2) COMMENT '合约规模',
    tick_size DECIMAL(10,4) COMMENT '最小变动价位',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_instruments_id (instrument_id),
    INDEX idx_instruments_name (instrument_name),
    UNIQUE KEY uk_instruments_id (instrument_id)
) COMMENT '期货合约表';
```

### 2.3 用户表 (sys_users) ✅
```sql
-- 系统用户表（已存在）
CREATE TABLE sys_users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    username VARCHAR(100) NOT NULL COMMENT '用户名',
    password VARCHAR(100) NOT NULL COMMENT '密码',
    nick_name VARCHAR(100) COMMENT '昵称',
    header_img VARCHAR(255) COMMENT '头像',
    phone VARCHAR(20) COMMENT '手机号',
    email VARCHAR(100) COMMENT '邮箱',
    enable BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    INDEX idx_users_username (username),
    INDEX idx_users_phone (phone),
    INDEX idx_users_email (email),
    INDEX idx_users_enable (enable)
) COMMENT '系统用户表';
```

### 2.4 商品表 (commodities) ✅

```sql
-- 商品表（已存在，需要更新）
CREATE TABLE commodities (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '商品ID',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '删除时间',
    
    -- 基本信息
    name VARCHAR(50) NOT NULL COMMENT '商品名称',
    product_id VARCHAR(20) NOT NULL COMMENT '品种ID',
    exchange_id VARCHAR(20) NOT NULL COMMENT '所属交易所ID',
    section VARCHAR(50) NULL COMMENT '所属版块（字符串格式，灵活定义）',
    
    -- 索引
    INDEX idx_commodities_product_id (product_id),
    INDEX idx_commodities_exchange_id (exchange_id),
    INDEX idx_commodities_section (section),
    INDEX idx_commodities_section_exchange (section, exchange_id),
    INDEX idx_commodities_deleted_at (deleted_at),
    UNIQUE KEY uk_commodities_product_id (product_id)
) COMMENT '商品表';
```

---

## 3. 待实现的扩展数据表

### 3.1 用户版块关注表 (dj_user_section_follows) 🚧

```sql
CREATE TABLE dj_user_section_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关注ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    section VARCHAR(50) NOT NULL COMMENT '版块名称',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_user_section_follow (user_id, section),
    INDEX idx_user_section_follows_user_id (user_id),
    INDEX idx_user_section_follows_section (section),
    
    FOREIGN KEY fk_user_section_user (user_id) REFERENCES sys_users(id)
) COMMENT '用户版块关注表';
```

### 3.2 关注关系表 (dj_follows) 🚧

```sql
CREATE TABLE dj_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关注ID',
    follower_user_id BIGINT NOT NULL COMMENT '关注者用户ID',
    followed_user_id BIGINT NULL COMMENT '被关注的用户ID',
    followed_quotation_id BIGINT NULL COMMENT '被关注的报价ID',
    type ENUM('User', 'Quotation') NOT NULL COMMENT '关注类型',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    INDEX idx_follows_follower_user_id (follower_user_id),
    INDEX idx_follows_followed_user_id (followed_user_id),
    INDEX idx_follows_followed_quotation_id (followed_quotation_id),
    INDEX idx_follows_type (type),
    
    UNIQUE KEY uk_follows_user_follow (follower_user_id, followed_user_id, type),
    UNIQUE KEY uk_follows_quotation_follow (follower_user_id, followed_quotation_id, type),
    
    FOREIGN KEY fk_follow_follower (follower_user_id) REFERENCES sys_users(id),
    FOREIGN KEY fk_follow_followed_user (followed_user_id) REFERENCES sys_users(id),
    FOREIGN KEY fk_follow_quotation (followed_quotation_id) REFERENCES dj_quotations(id)
) COMMENT '关注关系表';
```

### 3.3 通知表 (dj_notifications) 🚧

```sql
CREATE TABLE dj_notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '通知ID',
    recipient_user_id BIGINT NOT NULL COMMENT '接收者用户ID',
    type ENUM('QuotationUpdate', 'NewQuotation', 'UserFollow') NOT NULL COMMENT '通知类型',
    title VARCHAR(255) NOT NULL COMMENT '通知标题',
    content TEXT NOT NULL COMMENT '通知内容',
    related_quotation_id BIGINT NULL COMMENT '关联报价ID',
    related_user_id BIGINT NULL COMMENT '关联用户ID',
    is_read BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已读',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    read_at TIMESTAMP NULL COMMENT '阅读时间',
    
    INDEX idx_notifications_recipient_user_id (recipient_user_id),
    INDEX idx_notifications_is_read (is_read),
    INDEX idx_notifications_created_at (created_at),
    INDEX idx_notifications_type (type),
    
    FOREIGN KEY fk_notification_recipient (recipient_user_id) REFERENCES sys_users(id),
    FOREIGN KEY fk_notification_quotation (related_quotation_id) REFERENCES dj_quotations(id),
    FOREIGN KEY fk_notification_user (related_user_id) REFERENCES sys_users(id)
) COMMENT '通知表';
```

---

## 4. 已实现的统计分析表

### 4.1 浏览日志表 (dj_quotation_view_logs) 🚧

```sql
CREATE TABLE dj_quotation_view_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '日志ID',
    quotation_id BIGINT NOT NULL COMMENT '报价ID',
    viewer_user_id BIGINT NULL COMMENT '浏览者用户ID',
    viewer_ip VARCHAR(45) NOT NULL COMMENT '浏览者IP',
    user_agent TEXT NULL COMMENT '用户代理',
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '浏览时间',
    
    INDEX idx_view_logs_quotation_id (quotation_id),
    INDEX idx_view_logs_viewer_user_id (viewer_user_id),
    INDEX idx_view_logs_viewed_at (viewed_at),
    INDEX idx_view_logs_viewer_ip (viewer_ip),
    
    FOREIGN KEY fk_view_log_quotation (quotation_id) REFERENCES dj_quotations(id),
    FOREIGN KEY fk_view_log_user (viewer_user_id) REFERENCES sys_users(id)
) COMMENT '浏览日志表';
```

### 4.2 每日统计表 (dj_quotation_daily_stats) 🚧

```sql
CREATE TABLE dj_quotation_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    quotation_id BIGINT NOT NULL COMMENT '报价ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    view_count INT NOT NULL DEFAULT 0 COMMENT '当日浏览量(UV)',
    pv_count INT NOT NULL DEFAULT 0 COMMENT '当日页面访问量(PV)',
    new_follow_count INT NOT NULL DEFAULT 0 COMMENT '当日新增关注数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_daily_stats_quotation_date (quotation_id, stat_date),
    INDEX idx_daily_stats_stat_date (stat_date),
    INDEX idx_daily_stats_quotation_id (quotation_id),
    
    FOREIGN KEY fk_daily_stats_quotation (quotation_id) REFERENCES dj_quotations(id)
) COMMENT '报价每日统计表';
```

### 4.3 系统统计表 (dj_system_daily_stats) 🚧

```sql
CREATE TABLE dj_system_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '统计ID',
    stat_date DATE NOT NULL COMMENT '统计日期',
    new_quotations_count INT NOT NULL DEFAULT 0 COMMENT '新增报价数',
    active_quotations_count INT NOT NULL DEFAULT 0 COMMENT '活跃报价数',
    total_views_count INT NOT NULL DEFAULT 0 COMMENT '总浏览量',
    new_users_count INT NOT NULL DEFAULT 0 COMMENT '新增用户数',
    active_users_count INT NOT NULL DEFAULT 0 COMMENT '活跃用户数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    UNIQUE KEY uk_system_stats_stat_date (stat_date),
    INDEX idx_system_stats_stat_date (stat_date)
) COMMENT '系统每日统计表';
```

---

## 5. 待实现的用户相关表

### 5.1 用户偏好设置表 (dj_user_preferences) 🚧

```sql
CREATE TABLE dj_user_preferences (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '设置ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    notification_enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用通知',
    email_notification BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否启用邮件通知',
    push_notification BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用推送通知',
    privacy_level ENUM('Public', 'Friends', 'Private') NOT NULL DEFAULT 'Public' COMMENT '隐私级别',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    UNIQUE KEY uk_user_preferences_user_id (user_id),
    
    FOREIGN KEY fk_preference_user (user_id) REFERENCES sys_users(id)
) COMMENT '用户偏好设置表';
```

---

## 6. 已实现的数据关系图

```mermaid
erDiagram
    sys_users ||--o{ dj_quotations : "发布"
    sys_users ||--o{ dj_follows : "关注者"
    sys_users ||--o{ dj_follows : "被关注者"
    sys_users ||--o{ dj_notifications : "接收"
    sys_users ||--o{ dj_quotation_view_logs : "浏览"
    sys_users ||--o{ dj_user_section_follows : "关注版块"
    sys_users ||--|| dj_user_preferences : "偏好设置"
    
    commodities ||--o{ dj_quotations : "商品关联"
    dj_instruments ||--o{ dj_quotations : "关联"
    
    dj_quotations ||--o{ dj_follows : "被关注"
    dj_quotations ||--o{ dj_notifications : "关联"
    dj_quotations ||--o{ dj_quotation_view_logs : "被浏览"
    dj_quotations ||--o{ dj_quotation_daily_stats : "统计"
    
    dj_quotations {
        bigint id PK
        bigint user_id FK
        varchar title
        varchar commodity_name
        enum price_type
        decimal price
        timestamp expires_at
        enum status
        boolean is_buy_request
    }
    
    commodities {
        bigint id PK
        varchar name
        varchar product_id
        varchar exchange_id
        varchar section
    }
    
    sys_users {
        bigint id PK
        varchar username
        varchar nick_name
        varchar header_img
        varchar phone
        boolean enable
    }
    
    dj_instruments {
        bigint id PK
        varchar instrument_id
        varchar instrument_name
        varchar exchange
    }
    
    dj_follows {
        bigint id PK
        bigint follower_user_id FK
        bigint followed_user_id FK
        bigint followed_quotation_id FK
        enum type
    }
    
    dj_user_section_follows {
        bigint id PK
        bigint user_id FK
        varchar section
    }
```

---

## 7. 已实现的状态机定义

### 7.1 报价状态机 ✅

```mermaid
stateDiagram-v2
    [*] --> Draft : 创建报价
    Draft --> Active : 发布报价
    Draft --> [*] : 删除草稿
    Active --> Withdrawn : 用户撤回
    Active --> Expired : 自动过期
    Withdrawn --> Active : 重新发布
    Expired --> Active : 重新发布
    Withdrawn --> [*] : 删除报价
    Expired --> [*] : 删除报价
```

### 7.2 价格类型状态 ✅

```mermaid
stateDiagram-v2
    [*] --> Fixed : 一口价
    [*] --> Basis : 基差报价
    [*] --> Negotiable : 商议
    Fixed --> Basis : 切换类型
    Fixed --> Negotiable : 切换类型
    Basis --> Fixed : 切换类型
    Basis --> Negotiable : 切换类型
    Negotiable --> Fixed : 切换类型
    Negotiable --> Basis : 切换类型
```

---

## 8. 已实现的索引策略

### 8.1 主要索引 ✅

**dj_quotations表**: 
- 主键索引: `id`
- 复合索引: `(status, expires_at, created_at)`
- 复合索引: `(user_id, status)`
- 复合索引: `(price_type, created_at)`
- 单列索引: `commodity_name`, `expires_at`, `updated_at`

**commodities表**:
- 主键索引: `id`
- 唯一索引: `product_id`
- 普通索引: `exchange_id`, `section`
- 复合索引: `(section, exchange_id)`

**dj_instruments表**:
- 主键索引: `id`
- 唯一索引: `instrument_id`
- 普通索引: `instrument_name`

### 8.2 分区策略 🚧

**dj_quotation_view_logs表**: 按月分区，提高查询性能
**dj_quotation_daily_stats表**: 按年分区，便于历史数据管理

---

## 9. 已实现的数据字典

### 9.1 枚举值定义 ✅

**报价状态 (quotation_status)**:
- `Draft`: 草稿，仅创建者可见
- `Active`: 已发布，公开可见
- `Expired`: 已过期，不再显示
- `Withdrawn`: 已撤回，发布者主动下架

**报价类型 (price_type)**:
- `Fixed`: 一口价
- `Basis`: 基差报价
- `Negotiable`: 商议

**交易类型 (is_buy_request)**:
- `true`: 求购请求
- `false`: 出售报价

**关注类型 (follow_type)**:
- `User`: 关注用户
- `Quotation`: 关注报价

**通知类型 (notification_type)**:
- `QuotationUpdate`: 报价更新通知
- `NewQuotation`: 新报价通知
- `UserFollow`: 用户关注通知

### 9.2 字段约束 ✅
- 所有时间字段使用 `TIMESTAMP` 类型
- 所有金额字段使用 `DECIMAL(10,2)` 类型
- 所有文本字段指定字符集为 `utf8mb4`
- 所有外键字段添加级联删除约束
- 软删除支持 (`deleted_at` 字段)

### 9.3 版块字段定义 ✅

**commodities.section字段**:
- 类型: `VARCHAR(50)`
- 描述: 所属版块（字符串格式，灵活定义）
- 约束: 可为空，支持任意版块名称
- 索引: 单独索引和复合索引
- 示例: '黑色金属', '有色金属', '能源化工', '农产品', '贵金属'

---

## 10. 数据迁移策略

### 10.1 已实现的迁移 ✅

基于现有代码分析，已实现：
- ✅ 基础报价表结构
- ✅ 期货合约表结构
- ✅ 用户表关联
- ✅ 外键约束
- ✅ 基础索引

### 10.2 待实现的迁移 🚧

```sql
-- V1.0.1__Update_commodity_section_field.sql
ALTER TABLE commodities 
ADD COLUMN section VARCHAR(50) NULL COMMENT '所属版块';

CREATE INDEX idx_commodities_section ON commodities(section);
CREATE INDEX idx_commodities_section_exchange ON commodities(section, exchange_id);

-- V1.0.2__Add_user_section_follows.sql
CREATE TABLE dj_user_section_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    section VARCHAR(50) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_user_section_follow (user_id, section),
    INDEX idx_user_section_follows_user_id (user_id),
    INDEX idx_user_section_follows_section (section),
    
    FOREIGN KEY (user_id) REFERENCES sys_users(id)
);

-- V1.0.3__Add_follow_system.sql
CREATE TABLE dj_follows (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    follower_user_id BIGINT NOT NULL,
    followed_user_id BIGINT NULL,
    followed_quotation_id BIGINT NULL,
    type ENUM('User', 'Quotation') NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (follower_user_id) REFERENCES sys_users(id),
    FOREIGN KEY (followed_user_id) REFERENCES sys_users(id),
    FOREIGN KEY (followed_quotation_id) REFERENCES dj_quotations(id)
);

-- V1.0.4__Add_notification_system.sql
CREATE TABLE dj_notifications (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    recipient_user_id BIGINT NOT NULL,
    type ENUM('QuotationUpdate', 'NewQuotation', 'UserFollow') NOT NULL,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    related_quotation_id BIGINT NULL,
    related_user_id BIGINT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (recipient_user_id) REFERENCES sys_users(id),
    FOREIGN KEY (related_quotation_id) REFERENCES dj_quotations(id),
    FOREIGN KEY (related_user_id) REFERENCES sys_users(id)
);

-- V1.0.5__Add_analytics_tables.sql
CREATE TABLE dj_quotation_view_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quotation_id BIGINT NOT NULL,
    viewer_user_id BIGINT NULL,
    viewer_ip VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    viewed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quotation_id) REFERENCES dj_quotations(id),
    FOREIGN KEY (viewer_user_id) REFERENCES sys_users(id)
);

CREATE TABLE dj_quotation_daily_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    quotation_id BIGINT NOT NULL,
    stat_date DATE NOT NULL,
    view_count INT DEFAULT 0,
    pv_count INT DEFAULT 0,
    new_follow_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (quotation_id) REFERENCES dj_quotations(id),
    UNIQUE KEY (quotation_id, stat_date)
);
```

### 10.3 数据迁移策略
- 使用Flyway进行版本化迁移
- 每个迁移脚本包含回滚方案
- 在测试环境充分验证后再应用到生产环境
- 支持增量迁移和回滚

---

## 11. 性能优化建议

### 11.1 查询优化 ✅

基于现有实现，已优化的查询：
- ✅ 使用适当的索引
- ✅ 避免全表扫描
- ✅ 使用分页查询
- ✅ 优化JOIN操作

### 11.2 缓存策略 🚧

```sql
-- 建议的缓存策略
-- 1. 热门报价缓存
-- 2. 用户信息缓存
-- 3. 版块数据缓存
-- 4. 统计数据缓存
```

### 11.3 读写分离 🚧

```sql
-- 建议的读写分离策略
-- 1. 主库处理写操作
-- 2. 从库处理读操作
-- 3. 统计查询使用从库
-- 4. 实时性要求高的查询使用主库
```

---

## 12. 数据安全策略

### 12.1 已实现的安全措施 ✅

- ✅ 用户密码加密存储
- ✅ 外键约束保证数据一致性
- ✅ 软删除支持数据恢复
- ✅ 基本的权限控制

### 12.2 待加强的安全措施 🚧

- 🚧 敏感数据脱敏显示
- 🚧 数据访问审计日志
- 🚧 SQL注入防护增强
- 🚧 数据备份策略完善

---

## 13. 监控和维护

### 13.1 已实现的监控 ✅

- ✅ 基本的错误日志
- ✅ 应用性能监控
- ✅ 数据库连接监控

### 13.2 待完善的监控 🚧

- 🚧 慢查询监控
- 🚧 数据库性能监控
- 🚧 缓存命中率监控
- 🚧 业务指标监控

---

## 14. 简化设计说明

### 14.1 设计理念

**简化原则**:
- **灵活性**: commodities表使用字符串格式的section字段，无需预定义版块内容
- **直接性**: 直接使用gorm定义，无需复杂的表关联
- **扩展性**: 版块内容可动态添加，无需修改表结构
- **高效性**: 避免多表JOIN，提升查询性能

### 14.2 核心优势

**技术优势**:
- **结构简化**: 删除不必要的分类表，减少数据复杂度
- **查询高效**: 直接字符串匹配，避免多表JOIN操作
- **维护成本低**: 不需要复杂的分类管理后台
- **兼容性好**: 保持现有功能，新增功能不破坏原有结构

**业务优势**:
- **灵活配置**: 版块内容可根据业务需求动态调整
- **快速迭代**: 无需数据库结构变更即可支持新版块
- **用户体验**: 简化的筛选逻辑，提升用户操作效率
- **数据一致性**: 减少表间关联，降低数据不一致风险

---

**总结**: 报价系统的核心数据模型已实现完成，包括报价表、期货合约表、用户表、商品表等。通过简化设计，采用字符串格式的section字段，避免了复杂的分类表结构，同时保持了功能的完整性和扩展性。数据关系清晰，状态机完善，索引策略合理。接下来需要重点实现扩展功能表（关注、通知、统计等），完善性能优化策略，加强数据安全措施，建立完善的监控体系。
