"use strict";
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
if (!Math) {
  _easycom_wd_icon();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "about",
  setup(__props) {
    const appVersion = common_vendor.ref("V1.0");
    const buildTime = common_vendor.ref("2025年08月05日");
    const handleLogoError = () => {
      console.log("Logo加载失败，使用默认图标");
    };
    const openWebsite = () => {
      common_vendor.index.showModal({
        title: "打开网站",
        content: "是否要在浏览器中打开官方网站？",
        success: (res) => {
          if (res.confirm) {
            common_vendor.index.showToast({
              title: "请在浏览器中访问 www.dianjia.com",
              icon: "none",
              duration: 3e3
            });
          }
        }
      });
    };
    return (_ctx, _cache) => {
      return {
        a: common_assets._imports_0,
        b: common_vendor.o(handleLogoError),
        c: common_vendor.t(appVersion.value),
        d: common_vendor.t(buildTime.value),
        e: common_vendor.p({
          name: "home",
          size: "32rpx",
          color: "#667eea"
        }),
        f: common_vendor.p({
          name: "link",
          size: "32rpx",
          color: "#667eea"
        }),
        g: common_vendor.o(openWebsite)
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-8a2c2bc0"]]);
wx.createPage(MiniProgramPage);
