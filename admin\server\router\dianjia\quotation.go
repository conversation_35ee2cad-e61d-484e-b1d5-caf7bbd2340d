package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type QuotationRouter struct{}

// InitQuotationRouter 初始化报价路由信息
func (s *QuotationRouter) InitQuotationRouter(privateRouter *gin.RouterGroup, publicRouter *gin.RouterGroup) {
	// 公开路由组（无需认证）
	s.InitQuotationPublicRouter(publicRouter)

	// 私有路由组（需要认证）
	s.InitQuotationPrivateRouter(privateRouter)
}

// InitQuotationPublicRouter 初始化公开报价路由（无需认证）
func (s *QuotationRouter) InitQuotationPublicRouter(Router *gin.RouterGroup) {
	// 公开的报价相关路由（无需认证）
	publicQuotationRouter := Router.Group("dianjia/public-quotations")
	{
		publicQuotationRouter.POST("", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.GetPublicQuotationList)        // 获取公开报价列表
		publicQuotationRouter.GET(":quotationId", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.GetPublicQuotation) // 获取公开报价详情
	}
}

// InitQuotationPrivateRouter 初始化私有报价路由（需要认证）
func (s *QuotationRouter) InitQuotationPrivateRouter(Router *gin.RouterGroup) {
	// 需要操作记录的路由组（写操作）
	quotationRouter := Router.Group("dianjia/quotations").Use(middleware.OperationRecord())

	// 不需要操作记录的路由组（读操作）
	quotationRouterWithoutRecord := Router.Group("dianjia/quotations")

	// 我的报价相关路由
	myQuotationRouterWithoutRecord := Router.Group("dianjia/my-quotations")

	// 需要认证的报价管理路由
	{
		// 基础CRUD操作
		quotationRouter.POST("", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.CreateQuotation)               // 创建报价
		quotationRouter.PUT(":quotationId", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.UpdateQuotation)    // 更新报价
		quotationRouter.DELETE(":quotationId", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.DeleteQuotation) // 删除报价

		// 状态管理操作
		quotationRouter.POST(":quotationId/publish", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.PublishQuotation)     // 发布报价
		quotationRouter.POST(":quotationId/toggle", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.ToggleQuotationStatus) // 切换报价状态

		// 私有报价详情查看（需要权限验证）
		quotationRouterWithoutRecord.GET(":quotationId", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.GetQuotationDetail) // 获取报价详情（带权限验证）
	}

	// 我的报价相关路由（需要认证）
	{
		myQuotationRouterWithoutRecord.GET("", v1.ApiGroupApp.DianjiaApiGroup.QuotationApi.GetMyQuotationList) // 获取我的报价列表
	}
}
