/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.selector-container.data-v-14aff35e {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: rgba(255, 255, 255, 0.95);
  overflow: hidden;
}
.header.data-v-14aff35e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 36rpx;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}
.header .title.data-v-14aff35e {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.header.data-v-14aff35e .close-button {
  color: #666 !important;
}
.fixed-top.data-v-14aff35e {
  position: sticky;
  top: 0;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-bottom: 2rpx solid #f0f0f0;
}
.search-section.data-v-14aff35e {
  padding: 20rpx 36rpx;
}
.search-section.data-v-14aff35e .search-input {
  border-radius: 12rpx !important;
  border: 2rpx solid #e4e7ed !important;
}
.search-section.data-v-14aff35e .search-input:focus-within {
  border-color: #667eea !important;
}
.search-section.data-v-14aff35e .search-icon {
  color: #667eea !important;
}
.selected-count.data-v-14aff35e {
  color: #667eea;
  font-size: 26rpx;
  text-align: center;
  font-weight: 500;
}
.content.data-v-14aff35e {
  flex: 1;
  overflow: hidden;
}
.scroll-content.data-v-14aff35e {
  height: 100%;
  padding: 20rpx 0;
}
.empty-state.data-v-14aff35e {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx 36rpx;
}
.empty-state .empty-text.data-v-14aff35e {
  color: #999;
  font-size: 28rpx;
}
.section-list.data-v-14aff35e {
  padding: 0 36rpx;
}
.section-group.data-v-14aff35e {
  margin-bottom: 20rpx;
}
.section-group.data-v-14aff35e:last-child {
  margin-bottom: 0;
}
.section-header.data-v-14aff35e {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.section-header .section-title-content.data-v-14aff35e {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  cursor: pointer;
}
.section-header .section-title.data-v-14aff35e {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  flex: 1;
}
.section-header .section-count.data-v-14aff35e {
  font-size: 24rpx;
  color: #999;
}
.section-header .section-count .selected-count.data-v-14aff35e {
  color: #667eea;
  font-weight: 500;
}
.section-header .section-toggle.data-v-14aff35e {
  padding: 8rpx;
  cursor: pointer;
  transition: transform 0.2s ease;
}
.section-header .section-toggle.data-v-14aff35e:hover {
  transform: scale(1.1);
}
.section-header.data-v-14aff35e .section-checkbox {
  flex-shrink: 0;
}
.section-header.data-v-14aff35e .toggle-icon {
  color: #666 !important;
}
.commodity-list.data-v-14aff35e {
  padding: 16rpx 0;
}
.commodity-item.data-v-14aff35e {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 0;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.commodity-item.data-v-14aff35e:hover {
  background: rgba(102, 126, 234, 0.05);
}
.commodity-item.data-v-14aff35e .commodity-checkbox {
  flex-shrink: 0;
}
.commodity-info.data-v-14aff35e {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;
}
.commodity-info .commodity-name.data-v-14aff35e {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}
.commodity-info .commodity-code.data-v-14aff35e {
  font-size: 24rpx;
  color: #666;
}
.loading-state.data-v-14aff35e {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 36rpx;
  gap: 16rpx;
}
.loading-state .loading-text.data-v-14aff35e {
  color: #667eea;
  font-size: 28rpx;
}
.footer.data-v-14aff35e {
  position: sticky;
  bottom: 0;
  z-index: 10;
  display: flex;
  gap: 16rpx;
  padding: 20rpx 36rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid #f0f0f0;
  background: #f8f9fa;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.footer.data-v-14aff35e .cancel-button {
  flex: 1;
  border-radius: 12rpx !important;
}
.footer.data-v-14aff35e .confirm-button {
  flex: 2;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 12rpx !important;
  font-weight: 600 !important;
}
.data-v-14aff35e .commodity-selector-popup {
  overflow: hidden !important;
}