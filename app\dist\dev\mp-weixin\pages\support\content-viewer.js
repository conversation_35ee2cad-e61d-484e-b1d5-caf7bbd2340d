"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const __unplugin_components_0 = () => "../../components/zero-markdown-view/zero-markdown-view.js";
if (!Array) {
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_zero_markdown_view2 = __unplugin_components_0;
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_loading2 + _easycom_wd_icon2 + _easycom_wd_button2 + _easycom_zero_markdown_view2 + _component_layout_default_uni)();
}
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_zero_markdown_view = () => "../../components/zero-markdown-view/zero-markdown-view.js";
if (!Math) {
  (_easycom_wd_loading + _easycom_wd_icon + _easycom_wd_button + _easycom_zero_markdown_view)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "content-viewer",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const markdownContent = common_vendor.ref("");
    const contentUrl = common_vendor.ref("");
    const pageTitle = common_vendor.ref("内容详情");
    const loadContent = () => __async(this, null, function* () {
      loading.value = true;
      error.value = "";
      try {
        if (!contentUrl.value) {
          throw new Error("未提供内容URL");
        }
        const response = yield common_vendor.index.request({
          url: contentUrl.value,
          method: "GET",
          responseType: "text"
        });
        if (response.statusCode !== 200) {
          throw new Error(`请求失败: ${response.statusCode}`);
        }
        markdownContent.value = response.data;
      } catch (err) {
        console.error("加载内容失败:", err);
        error.value = err.message || "加载失败，请重试";
      } finally {
        loading.value = false;
      }
    });
    common_vendor.onLoad((options) => {
      console.log("页面参数:", options);
      contentUrl.value = options.url || "https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/privacy_policy.md";
      if (options.title) {
        pageTitle.value = options.title;
        common_vendor.index.setNavigationBarTitle({
          title: options.title
        });
      }
    });
    common_vendor.onMounted(() => {
      loadContent();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: loading.value
      }, loading.value ? {} : error.value ? {
        c: common_vendor.p({
          name: "warning",
          size: "64rpx",
          color: "#f56c6c"
        }),
        d: common_vendor.t(error.value),
        e: common_vendor.o(loadContent),
        f: common_vendor.p({
          type: "primary",
          size: "small"
        })
      } : {
        g: common_vendor.p({
          markdown: markdownContent.value
        })
      }, {
        b: error.value
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-110c8481"]]);
wx.createPage(MiniProgramPage);
