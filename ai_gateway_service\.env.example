# AI Gateway Service Environment Variables
# Copy this file to .env and fill in your actual values

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=
OPENAI_MODEL=gpt-4-turbo
OPENAI_TIMEOUT=30

# Application Configuration
APP_NAME=AI Gateway Service
APP_VERSION=1.0.0
DEBUG=false

# Server Configuration
HOST=0.0.0.0
PORT=8001

# Logging Configuration
LOG_LEVEL=INFO

# CORS Configuration (comma-separated list)
CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://localhost:8080

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=dianjia
DB_CHARSET=utf8mb4