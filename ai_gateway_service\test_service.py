"""
Simple test script for AI Gateway Service functionality
Run this to verify the service works without needing actual OpenAI API calls
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch

from schemas.quotation import ParsedQuotationData, QuotationParseRequest, ContractInferenceRequest, InstrumentSelectItem
from services.quotation_parser import QuotationParsingService
from services.contract_inference import ContractInferenceService


async def test_mock_parsing():
    """Test quotation parsing with mocked OpenAI response"""
    
    # Mock response that should be returned by OpenAI (updated to match frontend schema)
    mock_parsed_data = ParsedQuotationData(
        title="中天螺纹钢基差报价",
        commodityName="螺纹钢",
        deliveryLocation="上海地区仓库",
        brand="中天",
        specifications="HRB400E 20mm",
        description="过磅含税",
        priceType="Basis",
        price=120,
        instrumentRefID=3,
        instrumentRef=InstrumentSelectItem(
            id=3,
            instrument_id="rb2505",
            instrument_name="螺纹钢2505",
            product_name="螺纹钢",
            exchange_id="SHFE"
        ),
        expiresAt="2025-08-09T23:59:59.000Z",
        status="Draft"
    )
    
    # Test input
    test_text = "上海地区仓库自提，中天螺纹钢，规格HRB400E 20mm，500吨，价格比RB2501盘面高120，过磅含税，3天内有效。"
    
    # Mock the LangChain chain to return our expected data
    with patch.object(QuotationParsingService, '__init__', lambda self: None):
        service = QuotationParsingService()
        service.chain = AsyncMock()
        service.chain.ainvoke.return_value = mock_parsed_data
        
        # Mock the validation method
        service.validate_quotation_data = lambda data: True
        
        # Test the parsing
        result = await service.parse_quotation_text(test_text)
        
        print("测试结果:")
        print(f"成功: {result.success}")
        print(f"消息: {result.message}")
        if result.data:
            print("解析数据:")
            print(json.dumps(result.data.model_dump(), ensure_ascii=False, indent=2))
        else:
            print("无数据返回")


def test_data_models():
    """Test Pydantic data models"""
    
    print("\n测试数据模型...")
    
    # Test request model
    request = QuotationParseRequest(text="测试文本")
    print(f"请求模型: {request.model_dump()}")
    
    # Test response model
    response_data = ParsedQuotationData(
        title="测试报价",
        commodityName="螺纹钢",
        deliveryLocation="上海",
        priceType="Fixed",
        price=5000
    )
    print(f"响应数据模型: {response_data.model_dump()}")
    
    print("数据模型测试通过")


def test_prompt_template():
    """Test prompt template functionality"""
    
    print("\n测试Prompt模板...")
    
    from prompts.quotation_prompts import PARSE_QUOTATION_PROMPT, EXAMPLE_QUOTATIONS
    
    print(f"Prompt模板长度: {len(PARSE_QUOTATION_PROMPT)} 字符")
    print(f"示例数量: {len(EXAMPLE_QUOTATIONS)}")
    
    # Test template formatting
    from langchain.output_parsers import PydanticOutputParser
    parser = PydanticOutputParser(pydantic_object=ParsedQuotationData)
    
    formatted_prompt = PARSE_QUOTATION_PROMPT.format(
        format_instructions=parser.get_format_instructions(),
        user_text="测试文本"
    )
    
    print(f"格式化后的Prompt长度: {len(formatted_prompt)} 字符")
    print("Prompt模板测试通过")


async def test_contract_inference():
    """Test contract inference functionality"""
    
    print("\n测试合约推断功能...")
    
    # Mock contract inference
    with patch.object(ContractInferenceService, '__init__', lambda self: None):
        service = ContractInferenceService()
        service.settings = None
        service.model = AsyncMock()
        service.prompt = AsyncMock()
        service.get_contracts_from_database = AsyncMock()
        service.get_contracts_from_database.return_value = [
            {
                "id": 3,
                "instrument_id": "rb2505",
                "instrument_name": "螺纹钢2505",
                "product_name": "螺纹钢",
                "exchange_id": "SHFE"
            }
        ]
        
        # Mock LLM response
        mock_llm_response = AsyncMock()
        mock_llm_response.content = json.dumps({
            "instrument_id": 3,
            "confidence": 0.85,
            "reasoning": "文本中提到螺纹钢，匹配到SHFE的rb2505合约"
        })
        service.model.ainvoke.return_value = mock_llm_response
        
        # Test inference
        request = ContractInferenceRequest(
            text="螺纹钢基差报价，比RB2505高120",
            commodity_name="螺纹钢"
        )
        
        result = await service.infer_contract(request)
        
        print(f"推断成功: {result.success}")
        print(f"合约ID: {result.instrumentRefID}")
        print(f"置信度: {result.confidence}")
        print(f"推断理由: {result.reasoning}")
        if result.instrumentRef:
            print(f"合约信息: {result.instrumentRef.instrument_id}")


async def main():
    """Run all tests"""
    print("AI Gateway Service 功能测试")
    print("=" * 50)
    
    test_data_models()
    test_prompt_template()
    await test_mock_parsing()
    await test_contract_inference()
    
    print("\n所有测试完成！")
    print("\n接下来的步骤:")
    print("1. 设置真实的 OPENAI_API_KEY 在 .env 文件中")
    print("2. 启动服务: uv run python main.py")
    print("3. 访问 API 文档: http://localhost:8001/docs")
    print("4. 测试解析接口: POST /api/v1/quotations/parse")
    print("5. 测试合约推断接口: POST /api/v1/contract/infer")


if __name__ == "__main__":
    asyncio.run(main())