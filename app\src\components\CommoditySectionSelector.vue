<script lang="ts" setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useCommodityStore } from '@/store/commodity'
import { useUserStore } from '@/store/user'
import type { ICommoditySectionMap, ICommodity } from '@/types/commodity'

interface Props {
  visible: boolean
}

interface Emits {
  (event: 'update:visible', visible: boolean): void
  (event: 'confirm', selectedCommodities: string[]): void
  (event: 'cancel'): void
}

defineOptions({
  name: 'CommoditySectionSelector'
})

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== Store 实例 ====================

const commodityStore = useCommodityStore()
const userStore = useUserStore()

// ==================== 响应式数据声明 ====================

const searchKeyword = ref<string>('')
const selectedCommodities = ref<string[]>([])
const originalSelected = ref<string[]>([])
const expandedSection = ref<string | null>(null) // 当前展开的section

// ==================== 计算属性 ====================

const internalVisible = computed({
  get: () => props.visible,
  set: (val) => emit('update:visible', val)
})

// 是否正在加载
const isLoading = computed(() => commodityStore.isLoading || userStore.favoritesLoading)

// 是否正在保存
const isSaving = computed(() => userStore.favoritesLoading)

// 筛选后的商品列表
const filteredSectionMap = computed(() => {
  if (!searchKeyword.value.trim()) {
    return commodityStore.commoditySectionMap
  }

  const keyword = searchKeyword.value.trim().toLowerCase()
  const filtered: ICommoditySectionMap = {}

  Object.entries(commodityStore.commoditySectionMap).forEach(([section, commodities]) => {
    const matchedCommodities = commodities.filter(commodity =>
      commodity.name.toLowerCase().includes(keyword) ||
      commodity.product_id.toLowerCase().includes(keyword)
    )

    if (matchedCommodities.length > 0) {
      filtered[section] = matchedCommodities
    }
  })

  return filtered
})

// 是否有选择的品种
const hasSelected = computed(() => selectedCommodities.value.length > 0)

// 检查是否有变更
const hasChanges = computed(() => {
  if (selectedCommodities.value.length !== originalSelected.value.length) {
    return true
  }
  return !selectedCommodities.value.every(id => originalSelected.value.includes(id))
})

// ==================== 数据获取相关函数 ====================

/**
 * 加载商品数据
 */
async function loadCommodityData(): Promise<void> {
  try {
    // 并行加载商品列表和用户关注品种
    await Promise.all([
      commodityStore.loadCommodityList(),
      userStore.loadFavoriteCommodities()
    ])

    // 从 store 中获取用户关注的品种
    selectedCommodities.value = [...userStore.favoriteCommodities]
    originalSelected.value = [...userStore.favoriteCommodities]

  } catch (error) {
    console.error('加载数据失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  }
}

// ==================== 交互处理函数 ====================

/**
 * 切换商品选择状态
 */
function toggleCommodity(commodityName: string): void {
  const index = selectedCommodities.value.indexOf(commodityName)
  if (index > -1) {
    selectedCommodities.value.splice(index, 1)
  } else {
    selectedCommodities.value.push(commodityName)
  }
}

/**
 * 检查商品是否已选择
 */
function isCommoditySelected(commodityName: string): boolean {
  return selectedCommodities.value.includes(commodityName)
}

/**
 * 切换版块折叠状态（风琴样式）
 */
function toggleSectionCollapse(section: string): void {
  if (expandedSection.value === section) {
    // 如果当前section已展开，则折叠它
    expandedSection.value = null
  } else {
    // 展开新的section，自动折叠其他section
    expandedSection.value = section
  }
}

/**
 * 检查版块是否折叠
 */
function isSectionCollapsed(section: string): boolean {
  return expandedSection.value !== section
}

/**
 * 选择整个版块
 */
function toggleSection(section: string): void {
  const commodities = commodityStore.commoditySectionMap[section] || []
  const sectionCommodityNames = commodities.map(c => c.name)

  // 检查是否全部选中
  const allSelected = sectionCommodityNames.every(name => isCommoditySelected(name))

  if (allSelected) {
    // 取消选择该版块的所有品种
    selectedCommodities.value = selectedCommodities.value.filter(
      name => !sectionCommodityNames.includes(name)
    )
  } else {
    // 选择该版块的所有品种
    sectionCommodityNames.forEach(name => {
      if (!isCommoditySelected(name)) {
        selectedCommodities.value.push(name)
      }
    })
  }
}

/**
 * 检查版块是否全选
 */
function isSectionFullySelected(section: string): boolean {
  const commodities = commodityStore.commoditySectionMap[section] || []
  const sectionCommodityNames = commodities.map(c => c.name)
  return sectionCommodityNames.length > 0 && sectionCommodityNames.every(name => isCommoditySelected(name))
}

/**
 * 检查版块是否部分选择
 */
function isSectionPartiallySelected(section: string): boolean {
  const commodities = commodityStore.commoditySectionMap[section] || []
  const sectionCommodityNames = commodities.map(c => c.name)
  const selectedCount = sectionCommodityNames.filter(name => isCommoditySelected(name)).length
  return selectedCount > 0 && selectedCount < sectionCommodityNames.length
}

/**
 * 获取版块选中数量
 */
function getSectionSelectedCount(section: string): number {
  const commodities = commodityStore.commoditySectionMap[section] || []
  const sectionCommodityNames = commodities.map(c => c.name)
  return sectionCommodityNames.filter(name => isCommoditySelected(name)).length
}

/**
 * 确认选择
 */
async function handleConfirm(): Promise<void> {
  if (selectedCommodities.value.length === 0) {
    uni.showToast({
      title: '请至少选择一个品种',
      icon: 'none'
    })
    return
  }

  try {
    await userStore.updateFavoriteCommodities(selectedCommodities.value)

    emit('confirm', selectedCommodities.value)
    internalVisible.value = false

  } catch (error) {
    console.error('保存失败:', error)
    // userStore.updateFavoriteCommodities 已经处理了错误提示
  }
}

/**
 * 取消选择
 */
function handleCancel(): void {
  // 恢复原始选择
  selectedCommodities.value = [...originalSelected.value]
  emit('cancel')
  internalVisible.value = false
}

/**
 * 清空搜索
 */
function clearSearch(): void {
  searchKeyword.value = ''
}

/**
 * 验证并清理用户关注的品种，确保只保留在商品库中存在的品种
 */
async function validateAndCleanFavoriteCommodities(): Promise<void> {
  try {
    // 获取所有可用的商品名称
    const allCommodityNames = new Set<string>()
    Object.values(commodityStore.commoditySectionMap).forEach(commodities => {
      commodities.forEach(commodity => {
        allCommodityNames.add(commodity.name)
      })
    })

    // 获取用户当前关注的品种
    const currentFavorites = userStore.favoriteCommodities

    // 计算交集：只保留在商品库中存在的关注品种
    const validFavorites = currentFavorites.filter(commodityName =>
      allCommodityNames.has(commodityName)
    )

    // 如果有无效的关注品种被移除，更新用户的关注列表
    if (validFavorites.length !== currentFavorites.length) {
      console.log(`清理无效关注品种: ${currentFavorites.length} -> ${validFavorites.length}`)
      await userStore.updateFavoriteCommodities(validFavorites)
    }
  } catch (error) {
    console.error('验证关注品种失败:', error)
  }
}

// ==================== 页面生命周期函数 ====================

onMounted(async () => {
  if (props.visible) {
    await loadCommodityData()
    await validateAndCleanFavoriteCommodities()
    // 默认所有section都折叠
    expandedSection.value = null
  }
})

// 监听visible变化，在弹窗打开时加载数据
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    await loadCommodityData()
    await validateAndCleanFavoriteCommodities()
    // 默认所有section都折叠
    expandedSection.value = null
  }
})
</script>

<template>
  <wd-popup
    v-model="internalVisible"
    position="right"
    :z-index="1000"
    :close-on-click-modal="true"
    custom-style="width: 75vw; height: 100vh;"
    custom-class="commodity-selector-popup"
  >
    <view class="selector-container">
      <!-- 标题栏 -->
      <view class="header">
        <text class="title">选择关注品种</text>
        <wd-button
          type="text"
          size="small"
          custom-class="close-button"
          @click="handleCancel"
        >
          <wd-icon name="close" size="32rpx" />
        </wd-button>
      </view>

      <!-- 固定顶部区域 -->
      <view class="fixed-top">
        <!-- 搜索栏 -->
        <view class="search-section">
          <wd-input
            v-model="searchKeyword"
            placeholder="搜索品种名称或代码..."
            clearable
            custom-class="search-input"
            @clear="clearSearch"
          >
            <template #prefix>
              <wd-icon name="search" size="28rpx" custom-class="search-icon" />
            </template>
          </wd-input>
        </view>

        <!-- 已选择统计 -->
        <view v-if="hasSelected" class="selected-count">
          已选择 {{ selectedCommodities.length }} 个品种
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="content" v-if="!isLoading">
        <scroll-view class="scroll-content" scroll-y>
          <view v-if="Object.keys(filteredSectionMap).length === 0" class="empty-state">
            <text class="empty-text">
              {{ searchKeyword ? '没有找到匹配的品种' : '暂无可选品种' }}
            </text>
          </view>
          
          <view v-else class="section-list">
            <view
              v-for="(commodities, section) in filteredSectionMap"
              :key="String(section)"
              class="section-group"
            >
              <!-- 版块标题 -->
              <view class="section-header">
                <view class="section-title-content" @click="toggleSection(String(section))">
                  <wd-checkbox
                    :model-value="isSectionFullySelected(String(section))"
                    :indeterminate="isSectionPartiallySelected(String(section))"
                    custom-class="section-checkbox"
                  />
                  <text class="section-title">{{ section }}</text>
                  <text class="section-count">
                    {{ commodities.length }}
                    <text v-if="getSectionSelectedCount(String(section)) > 0" class="selected-count">
                      （{{ getSectionSelectedCount(String(section)) }}选中）
                    </text>
                  </text>
                </view>
                <view class="section-toggle" @click="toggleSectionCollapse(String(section))">
                  <wd-icon
                    :name="isSectionCollapsed(String(section)) ? 'arrow-down' : 'arrow-up'"
                    size="24rpx"
                    custom-class="toggle-icon"
                  />
                </view>
              </view>

              <!-- 商品列表 -->
              <view v-if="!isSectionCollapsed(String(section))" class="commodity-list">
                <view
                  v-for="commodity in commodities"
                  :key="commodity.product_id"
                  class="commodity-item"
                  @click="toggleCommodity(commodity.name)"
                >
                  <wd-checkbox
                    :model-value="isCommoditySelected(commodity.name)"
                    custom-class="commodity-checkbox"
                  />
                  <view class="commodity-info">
                    <text class="commodity-name">{{ commodity.name }}</text>
                    <text class="commodity-code">{{ commodity.product_id }}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 加载状态 -->
      <view v-else class="loading-state">
        <wd-loading size="40rpx" />
        <text class="loading-text">加载中...</text>
      </view>

      <!-- 底部操作栏 -->
      <view class="footer">
        <wd-button
          type="default"
          custom-class="cancel-button"
          :disabled="isSaving"
          @click="handleCancel"
        >
          取消
        </wd-button>
        <wd-button
          type="primary"
          custom-class="confirm-button"
          :disabled="!hasSelected"
          :loading="isSaving"
          @click="handleConfirm"
        >
          确认选择
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<style lang="scss" scoped>
$primary-color: #667eea;
$primary-dark: #764ba2;
$bg-card: rgba(255, 255, 255, 0.95);
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 20rpx;
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$spacing-sm: 20rpx;
$spacing-md: 36rpx;

.selector-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: $bg-card;
  overflow: hidden;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: $spacing-sm $spacing-md;
  border-bottom: 2rpx solid #f0f0f0;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  .title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  :deep(.close-button) {
    color: #666 !important;
  }
}

.fixed-top {
  position: sticky;
  top: 0;
  z-index: 10;
  background: $bg-card;
  border-bottom: 2rpx solid #f0f0f0;
}

.search-section {
  padding: $spacing-sm $spacing-md;

  :deep(.search-input) {
    border-radius: $radius-md !important;
    border: 2rpx solid #e4e7ed !important;

    &:focus-within {
      border-color: $primary-color !important;
    }
  }

  :deep(.search-icon) {
    color: $primary-color !important;
  }
}

.selected-count {
  color: $primary-color;
  font-size: 26rpx;
  text-align: center;
  font-weight: 500;
}

.content {
  flex: 1;
  overflow: hidden;
}

.scroll-content {
  height: 100%;
  padding: $spacing-sm 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 80rpx $spacing-md;

  .empty-text {
    color: #999;
    font-size: 28rpx;
  }
}

.section-list {
  padding: 0 $spacing-md;
}

.section-group {
  margin-bottom: $spacing-sm;

  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;

  .section-title-content {
    display: flex;
    align-items: center;
    gap: 16rpx;
    flex: 1;
    cursor: pointer;
  }

  .section-title {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    flex: 1;
  }

  .section-count {
    font-size: 24rpx;
    color: #999;

    .selected-count {
      color: $primary-color;
      font-weight: 500;
    }
  }

  .section-toggle {
    padding: 8rpx;
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }
  }

  :deep(.section-checkbox) {
    flex-shrink: 0;
  }

  :deep(.toggle-icon) {
    color: #666 !important;
  }
}

.commodity-list {
  padding: 16rpx 0;
}

.commodity-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 16rpx 0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: rgba(102, 126, 234, 0.05);
  }

  :deep(.commodity-checkbox) {
    flex-shrink: 0;
  }
}

.commodity-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  flex: 1;

  .commodity-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .commodity-code {
    font-size: 24rpx;
    color: #666;
  }
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx $spacing-md;
  gap: 16rpx;

  .loading-text {
    color: $primary-color;
    font-size: 28rpx;
  }
}

.footer {
  position: sticky;
  bottom: 0;
  z-index: 10;
  display: flex;
  gap: 16rpx;
  padding: $spacing-sm $spacing-md;
  padding-bottom: calc($spacing-sm + env(safe-area-inset-bottom));
  border-top: 2rpx solid #f0f0f0;
  background: #f8f9fa;
  box-shadow: 0 -4rpx 12rpx rgba(0, 0, 0, 0.1);

  :deep(.cancel-button) {
    flex: 1;
    border-radius: $radius-md !important;
  }

  :deep(.confirm-button) {
    flex: 2;
    background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%) !important;
    border-radius: $radius-md !important;
    font-weight: 600 !important;
  }
}

:deep(.commodity-selector-popup) {
  overflow: hidden !important;
}
</style>