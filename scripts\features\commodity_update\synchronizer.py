import csv
import os
from datetime import datetime
from typing import List, Dict, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import or_

from modules.database import SessionLocal
from .models import Commodity


def read_csv_data(csv_file_path: str) -> List[Dict[str, str]]:
    """
    从CSV文件读取商品数据
    如果有重复的商品名称，保留最后一个

    Args:
        csv_file_path: CSV文件路径

    Returns:
        商品数据列表（按name去重）
    """
    if not os.path.exists(csv_file_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_file_path}")

    # 使用字典来去重，key为name，value为商品数据
    commodities_dict = {}

    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            commodity_data = {
                'product_id': row['product_id'].strip(),
                'exchange_id': row['exchange'].strip(),
                'name': row['name'].strip(),
                'section': row['section'].strip()
            }
            # 如果name重复，后面的会覆盖前面的
            commodities_dict[commodity_data['name']] = commodity_data

    # 转换为列表
    return list(commodities_dict.values())


def sync_commodities_to_database(commodities_data: List[Dict[str, str]]) -> Tuple[int, int, List[str]]:
    """
    将商品数据同步到数据库
    如果name存在就覆盖，不存在就新建
    处理product_id冲突的情况

    Args:
        commodities_data: 商品数据列表

    Returns:
        (新增数量, 更新数量, 冲突信息列表)
    """
    db: Session = SessionLocal()
    added_count = 0
    updated_count = 0
    conflicts = []

    try:
        for commodity_data in commodities_data:
            # 根据name查找现有记录
            existing_by_name = db.query(Commodity).filter(
                Commodity.name == commodity_data['name']
            ).first()

            # 根据product_id查找现有记录
            existing_by_product_id = db.query(Commodity).filter(
                Commodity.product_id == commodity_data['product_id']
            ).first()

            if existing_by_name:
                # 如果找到相同name的记录
                if existing_by_product_id and existing_by_product_id.id != existing_by_name.id:
                    # 如果product_id已被其他记录使用，这是一个冲突
                    conflict_msg = f"冲突: 商品 '{commodity_data['name']}' 的品种ID '{commodity_data['product_id']}' 已被商品 '{existing_by_product_id.name}' 使用"
                    conflicts.append(conflict_msg)
                    continue

                # 更新现有记录
                existing_by_name.product_id = commodity_data['product_id']
                existing_by_name.exchange_id = commodity_data['exchange_id']
                existing_by_name.section = commodity_data['section']
                existing_by_name.updated_at = datetime.now()
                updated_count += 1

            elif existing_by_product_id:
                # 如果只找到相同product_id的记录（但name不同）
                # 这种情况下，我们更新该记录的name
                existing_by_product_id.name = commodity_data['name']
                existing_by_product_id.exchange_id = commodity_data['exchange_id']
                existing_by_product_id.section = commodity_data['section']
                existing_by_product_id.updated_at = datetime.now()
                updated_count += 1

            else:
                # 创建新记录
                new_commodity = Commodity(
                    name=commodity_data['name'],
                    product_id=commodity_data['product_id'],
                    exchange_id=commodity_data['exchange_id'],
                    section=commodity_data['section'],
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                db.add(new_commodity)
                added_count += 1

        # 提交所有更改
        db.commit()

    except Exception as e:
        db.rollback()
        raise e
    finally:
        db.close()

    return added_count, updated_count, conflicts


def validate_csv_data(commodities_data: List[Dict[str, str]]) -> List[str]:
    """
    验证CSV数据的有效性

    Args:
        commodities_data: 商品数据列表

    Returns:
        错误信息列表
    """
    errors = []
    seen_product_ids = set()

    for i, commodity in enumerate(commodities_data, 1):
        # 检查必填字段
        if not commodity.get('name'):
            errors.append(f"第{i}行: 商品名称不能为空")
        if not commodity.get('product_id'):
            errors.append(f"第{i}行: 品种ID不能为空")
        if not commodity.get('exchange_id'):
            errors.append(f"第{i}行: 交易所ID不能为空")

        # 检查重复的product_id（品种ID应该是唯一的）
        if commodity.get('product_id') in seen_product_ids:
            errors.append(f"第{i}行: 品种ID '{commodity['product_id']}' 在CSV中重复")
        seen_product_ids.add(commodity.get('product_id'))

    return errors


def get_current_commodities_count() -> int:
    """
    获取当前数据库中的商品数量
    
    Returns:
        商品数量
    """
    db: Session = SessionLocal()
    try:
        count = db.query(Commodity).count()
        return count
    finally:
        db.close()
