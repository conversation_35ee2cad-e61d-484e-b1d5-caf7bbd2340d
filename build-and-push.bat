@echo off
chcp 65001 >nul 2>&1
echo Building and pushing Docker images to Aliyun Container Registry

:: Set registry address
set REGISTRY=registry.cn-hangzhou.aliyuncs.com/wuzhongyu

:: Login to Aliyun Container Registry
echo Logging into Aliyun Container Registry...
docker login --username=永湃科技 registry.cn-hangzhou.aliyuncs.com --password=F7Z9hTs.7YwKKvJ

if %errorlevel% neq 0 (
    echo Login failed, please check username and password
    pause
    exit /b 1
)

:: Build web frontend image
echo Building web frontend image...
cd admin\web
docker build -t %REGISTRY%/gva-web:latest .
if %errorlevel% neq 0 (
    echo Web image build failed
    pause
    exit /b 1
)

:: Build server backend image
echo Building server backend image...
cd ..\server
docker build -t %REGISTRY%/gva-server:latest .
if %errorlevel% neq 0 (
    echo Server image build failed
    pause
    exit /b 1
)

:: Build hq market data service image
echo Building hq market data service image...
cd ..\..\hq
docker build -t %REGISTRY%/gva-hq:latest .
if %errorlevel% neq 0 (
    echo HQ image build failed
    pause
    exit /b 1
)

:: Push all images
echo Pushing images to Aliyun...
docker push %REGISTRY%/gva-web:latest
docker push %REGISTRY%/gva-server:latest
docker push %REGISTRY%/gva-hq:latest

if %errorlevel% neq 0 (
    echo Image push failed
    pause
    exit /b 1
)

echo All images built and pushed successfully!
cd ..\
pause