package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type CommodityRouter struct{}

// InitCommodityRouter 初始化 商品 路由信息
func (s *CommodityRouter) InitCommodityRouter(privateRouter *gin.RouterGroup, publicRouter *gin.RouterGroup) {
	// 私有路由组（需要认证）
	s.InitCommodityPrivateRouter(privateRouter)

	// 公开路由组（无需认证）
	s.InitCommodityPublicRouter(publicRouter)
}

// InitCommodityPrivateRouter 初始化私有商品路由（需要认证）
func (s *CommodityRouter) InitCommodityPrivateRouter(Router *gin.RouterGroup) {
	commodityRouter := Router.Group("dianjia/commodity")
	commodityApi := v1.ApiGroupApp.DianjiaApiGroup.CommodityApi
	{
		commodityRouter.POST("createCommodity", commodityApi.CreateCommodity)             // 新建商品
		commodityRouter.DELETE("deleteCommodity", commodityApi.DeleteCommodity)           // 删除商品
		commodityRouter.DELETE("deleteCommodityByIds", commodityApi.DeleteCommodityByIds) // 批量删除商品
		commodityRouter.PUT("updateCommodity", commodityApi.UpdateCommodity)              // 更新商品
		commodityRouter.GET("findCommodity", commodityApi.FindCommodity)                  // 根据ID获取商品
		commodityRouter.GET("getCommodityList", commodityApi.GetCommodityList)            // 获取商品列表
	}
}

// InitCommodityPublicRouter 初始化公开商品路由（无需认证）
func (s *CommodityRouter) InitCommodityPublicRouter(Router *gin.RouterGroup) {
	// 公开的商品相关路由（无需认证）
	publicCommodityRouter := Router.Group("dianjia/commodity")
	commodityApi := v1.ApiGroupApp.DianjiaApiGroup.CommodityApi
	{
		publicCommodityRouter.GET("getAllCommodityList", commodityApi.GetAllCommodityList) // 获取所有商品列表
	}
}
