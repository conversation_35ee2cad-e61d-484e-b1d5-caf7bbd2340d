# 报价系统总览

> **版本**: 7.0.0 (重构版)  
> **负责人**: 系统架构团队  
> **状态**: 设计完成  
> **最后更新**: 2025-08-06

---

## 1. 项目背景与目标

### 1.1 项目背景
在大宗商品交易领域，传统的报价方式存在信息不透明、传播效率低、用户体验差等问题。本项目旨在构建一个现代化的数字报价平台，为市场参与者提供高效、透明、便捷的报价服务。

### 1.2 核心目标
- **透明化**: 建立公开透明的报价市场，提升信息流通效率
- **个性化**: 提供个性化的信息订阅和推荐服务
- **社交化**: 引入关注、分享等社交功能，增强用户粘性
- **数据化**: 提供完整的数据统计和分析功能
- **移动化**: 优先考虑移动端用户体验

### 1.3 业务价值
- 降低信息获取成本
- 提高交易撮合效率
- 增强平台用户活跃度
- 建立行业数据标准

---

## 2. 系统架构

### 2.1 整体架构

```mermaid
graph TB
    subgraph "前端层"
        A[移动端App] --> B[Web管理后台]
    end
    
    subgraph "业务层"
        C[报价管理服务] --> D[用户服务]
        E[通知服务] --> F[统计分析服务]
        G[搜索服务] --> H[文件服务]
    end
    
    subgraph "数据层"
        I[MySQL主库] --> J[MySQL从库]
        K[Redis缓存] --> L[文件存储]
    end
    
    subgraph "基础设施"
        M[消息队列] --> N[定时任务]
        O[监控告警] --> P[日志系统]
    end
    
    A --> C
    B --> C
    C --> I
    E --> M
    F --> K
```

### 2.2 技术栈

**后端技术栈 (admin/server)**:
- **语言**: Go - 主要开发语言
- **Web框架**: Gin - 高性能Web框架，用于构建RESTful API
- **数据库**: MySQL - 主要业务数据库，使用 InnoDB 引擎
- **时序数据库**: TDengine - 用于存储和查询海量行情时序数据
- **缓存**: Redis - 用于缓存热门数据、JWT令牌管理、实现多点登录限制等
- **ORM**: GORM - 数据库操作工具，简化数据库交互
- **配置管理**: Viper - 用于管理 config.yaml 配置文件
- **日志**: Zap - 高性能日志库，用于记录应用日志
- **API文档**: Swagger - 自动生成并维护API接口文档


**用户端应用 (app)**:
- **框架**: uni-app - 基于 Vue 3 的跨端开发框架
- **开发语言**: TypeScript - 为项目提供类型安全
- **UI样式**: UnoCSS - 原子化CSS框架，实现快速、灵活的样式开发
- **状态管理**: Pinia - 统一的状态管理方案
- **路由**: 约定式路由 - 基于文件的路由系统，由 vite-plugin-uni-pages 插件管理
- **包管理器**: pnpm - 高效的包管理工具


**运维技术栈**:
- **容器化**: Docker - 应用容器化部署
- **包管理**: 
  - 前端: pnpm - 高效的包管理工具
- **代码质量**: 
  - Go: gofmt, goimports
  - 前端: ESLint, Prettier
- **CI/CD**: GitHub Actions - 自动化测试、构建和部署

---

## 3. 核心概念定义

### 3.1 业务概念

**报价 (Quotation)**:
- 用户发布的商品价格信息
- 包含商品名称、价格、交货地点、有效期等要素
- 支持一口价和基差两种报价方式

**商品分类 (Category)**:
- 按照行业标准对商品进行分类
- 支持版块-分类的二级结构
- 用户可以订阅感兴趣的分类

**关注 (Follow)**:
- 用户可以关注其他用户或特定报价
- 被关注对象更新时会收到通知
- 支持用户级和报价级两种关注方式

**浏览统计 (View Tracking)**:
- 记录报价的浏览次数和趋势
- 支持去重统计，避免刷量
- 为发布者提供数据分析

### 3.2 状态定义

**报价状态**:
- `Draft`: 草稿状态，仅发布者可见
- `Active`: 已发布状态，公开可见
- `Expired`: 已过期状态，不再显示
- `Withdrawn`: 已撤回状态，发布者主动下架

**通知状态**:
- `Unread`: 未读状态
- `Read`: 已读状态

---

## 4. 用户角色与权限

### 4.1 用户角色

**普通用户**:
- 浏览公开报价市场
- 搜索和筛选报价
- 关注用户和报价
- 分享报价信息

**报价发布者**:
- 创建和管理报价
- 查看报价统计数据
- 管理关注者
- 推广报价内容

**平台管理员**:
- 管理商品分类
- 监控系统运行
- 处理用户反馈
- 数据分析和报告

### 4.2 权限矩阵

| 功能模块 | 普通用户 | 发布者 | 管理员 |
|---------|---------|--------|--------|
| 浏览报价 | ✓ | ✓ | ✓ |
| 创建报价 | - | ✓ | ✓ |
| 关注功能 | ✓ | ✓ | ✓ |
| 统计查看 | - | ✓ | ✓ |
| 分类管理 | - | - | ✓ |
| 系统监控 | - | - | ✓ |

---

## 5. 系统特性

### 5.1 性能特性
- **响应时间**: 页面加载时间 < 2秒
- **并发处理**: 支持1000+并发用户
- **数据处理**: 支持百万级报价数据
- **可用性**: 99.9%系统可用性

### 5.2 安全特性
- **身份认证**: JWT Token认证
- **权限控制**: RBAC权限模型
- **数据加密**: 敏感数据加密存储
- **防刷机制**: 接口限流和防刷

### 5.3 扩展特性
- **水平扩展**: 支持服务水平扩展
- **插件化**: 支持功能模块插件化
- **多租户**: 支持多租户架构
- **国际化**: 支持多语言扩展

---

## 6. 系统边界

### 6.1 包含功能
- 报价创建、编辑、发布、管理
- 公开市场浏览、搜索、筛选
- 用户关注、通知、社交分享
- 数据统计、分析、可视化
- 移动端适配、离线缓存

### 6.2 不包含功能
- 在线支付和交易
- 物流跟踪和管理
- 合同签署和管理
- 第三方系统集成
- 高级数据分析和AI

### 6.3 未来扩展
- 智能推荐算法
- 实时聊天功能
- 视频直播报价
- 区块链溯源
- 大数据分析平台

---

## 7. 项目里程碑

### 7.1 第一阶段 (MVP)
- 基础报价管理功能
- 简单的市场浏览功能
- 用户注册和认证
- 移动端基础适配

### 7.2 第二阶段 (增强版)
- 分类筛选和个性化
- 关注和通知系统
- 社交分享功能
- 基础数据统计

### 7.3 第三阶段 (完整版)
- 高级统计分析
- 性能优化
- 安全加固
- 运维监控完善

---

## 8. 风险评估

### 8.1 技术风险
- **数据一致性**: 分布式环境下的数据一致性挑战
- **性能瓶颈**: 大量并发访问可能导致的性能问题
- **安全漏洞**: 用户数据和业务数据的安全风险

### 8.2 业务风险
- **用户接受度**: 新系统的用户接受度和迁移成本
- **竞争压力**: 市场竞争对手的功能对比
- **法规合规**: 行业法规和数据保护要求

### 8.3 风险缓解
- 采用成熟的技术栈和架构模式
- 分阶段发布，逐步验证和优化
- 建立完善的测试和监控体系
- 制定详细的应急预案

---

## 9. 成功指标

### 9.1 技术指标
- 系统可用性 > 99.9%
- 平均响应时间 < 2秒
- 错误率 < 0.1%
- 代码覆盖率 > 80%

### 9.2 业务指标
- 日活跃用户数 (DAU)
- 报价发布量
- 用户留存率
- 功能使用率

### 9.3 用户体验指标
- 用户满意度评分
- 功能易用性评分
- 问题反馈响应时间
- 用户培训成本
