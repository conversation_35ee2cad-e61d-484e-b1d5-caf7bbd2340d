"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const api_quotation = require("../../api/quotation.js");
const store_commodity = require("../../store/commodity.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_radio2 = common_vendor.resolveComponent("wd-radio");
  const _easycom_wd_radio_group2 = common_vendor.resolveComponent("wd-radio-group");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_textarea2 = common_vendor.resolveComponent("wd-textarea");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_tab2 = common_vendor.resolveComponent("wd-tab");
  const _easycom_wd_tabs2 = common_vendor.resolveComponent("wd-tabs");
  const _easycom_wd_picker2 = common_vendor.resolveComponent("wd-picker");
  const _easycom_wd_datetime_picker2 = common_vendor.resolveComponent("wd-datetime-picker");
  const _easycom_wd_form2 = common_vendor.resolveComponent("wd-form");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_loading2 + _easycom_wd_radio2 + _easycom_wd_radio_group2 + _easycom_wd_cell2 + _easycom_wd_input2 + _easycom_wd_textarea2 + _easycom_wd_cell_group2 + _easycom_wd_tab2 + _easycom_wd_tabs2 + _easycom_wd_picker2 + _easycom_wd_datetime_picker2 + _easycom_wd_form2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_radio = () => "../../node-modules/wot-design-uni/components/wd-radio/wd-radio.js";
const _easycom_wd_radio_group = () => "../../node-modules/wot-design-uni/components/wd-radio-group/wd-radio-group.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_textarea = () => "../../node-modules/wot-design-uni/components/wd-textarea/wd-textarea.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_tab = () => "../../node-modules/wot-design-uni/components/wd-tab/wd-tab.js";
const _easycom_wd_tabs = () => "../../node-modules/wot-design-uni/components/wd-tabs/wd-tabs.js";
const _easycom_wd_picker = () => "../../node-modules/wot-design-uni/components/wd-picker/wd-picker.js";
const _easycom_wd_datetime_picker = () => "../../node-modules/wot-design-uni/components/wd-datetime-picker/wd-datetime-picker.js";
const _easycom_wd_form = () => "../../node-modules/wot-design-uni/components/wd-form/wd-form.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_loading + _easycom_wd_radio + _easycom_wd_radio_group + _easycom_wd_cell + _easycom_wd_input + SelectInput + _easycom_wd_textarea + _easycom_wd_cell_group + _easycom_wd_tab + _easycom_wd_tabs + InstrumentSelector + _easycom_wd_picker + _easycom_wd_datetime_picker + _easycom_wd_form + _easycom_wd_button)();
}
const InstrumentSelector = () => "../../components/InstrumentSelector.js";
const SelectInput = () => "../../components/SelectInput.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationEdit"
}), {
  __name: "edit",
  setup(__props) {
    const commodityStore = store_commodity.useCommodityStore();
    const isEdit = common_vendor.ref(false);
    const quotationId = common_vendor.ref();
    const isLoading = common_vendor.ref(false);
    const isSubmitting = common_vendor.ref(false);
    const formData = common_vendor.ref({
      title: "",
      commodityName: "",
      deliveryLocation: "",
      brand: "",
      specifications: "",
      description: "",
      isBuyRequest: false,
      // 默认为出售
      priceType: "Fixed",
      price: 0,
      instrumentRefID: void 0,
      expiresAt: "",
      status: "Draft"
    });
    const selectedInstrument = common_vendor.ref();
    const expiryPicker = common_vendor.ref();
    const customDatePicker = common_vendor.ref();
    const formRef = common_vendor.ref();
    const commodityOptions = common_vendor.computed(() => {
      return commodityStore.commodityList.map((item) => ({
        value: item.name,
        label: item.name,
        description: item.product_id
      }));
    });
    const deliveryLocationOptions = common_vendor.ref([]);
    const brandOptions = common_vendor.ref([]);
    const expiryOptions = common_vendor.ref([
      {
        label: "当日有效",
        value: "today",
        hours: 24,
        description: "到今日23:59过期"
      },
      {
        label: "3天内有效",
        value: "3days",
        hours: 72,
        description: "72小时后过期"
      },
      {
        label: "1周内有效",
        value: "1week",
        hours: 168,
        description: "7天后过期"
      },
      {
        label: "2周内有效",
        value: "2weeks",
        hours: 336,
        description: "14天后过期"
      },
      {
        label: "1个月内有效",
        value: "1month",
        hours: 720,
        description: "30天后过期"
      },
      {
        label: "自定义",
        value: "custom",
        hours: 0,
        description: "请选择具体的过期时间"
      }
    ]);
    const selectedExpiryOption = common_vendor.ref("3days");
    const customExpiryDate = common_vendor.ref("");
    const tradeIntentionValue = common_vendor.computed({
      get: () => formData.value.isBuyRequest ? "buy" : "sell",
      set: (value) => {
        formData.value.isBuyRequest = value === "buy";
      }
    });
    const formRules = {
      title: [
        { required: true, message: "请输入报价标题" }
      ],
      commodityName: [
        { required: true, message: "请输入商品名称" }
      ],
      deliveryLocation: [
        { required: true, message: "请输入交货地点" }
      ],
      // 价格验证现在动态处理
      expiresAt: [
        { required: true, message: "请设置有效期" }
      ]
    };
    const showInstrumentSelector = common_vendor.computed(() => {
      return formData.value.priceType === "Basis";
    });
    const showPriceInput = common_vendor.computed(() => {
      return formData.value.priceType !== "Negotiable";
    });
    const priceValidationRule = common_vendor.computed(() => {
      if (formData.value.priceType === "Negotiable") {
        return [];
      }
      return [{ required: true, message: "请输入价格" }];
    });
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.id) {
        quotationId.value = parseInt(options.id);
        isEdit.value = true;
        loadQuotationDetail();
      }
      updateExpiryTime();
    });
    common_vendor.onMounted(() => {
      commodityStore.loadCommodityList();
    });
    function loadQuotationDetail() {
      return __async(this, null, function* () {
        if (!quotationId.value)
          return;
        try {
          isLoading.value = true;
          const res = yield api_quotation.getQuotationDetail(quotationId.value);
          const quotation = res.data;
          formData.value = {
            title: quotation.title,
            commodityName: quotation.commodityName,
            deliveryLocation: quotation.deliveryLocation,
            brand: quotation.brand || "",
            specifications: quotation.specifications || "",
            description: quotation.description || "",
            isBuyRequest: quotation.isBuyRequest || false,
            priceType: typeof quotation.priceType === "string" ? quotation.priceType : "Fixed",
            price: quotation.price,
            instrumentRefID: quotation.instrumentRefID,
            expiresAt: quotation.expiresAt
          };
          selectedInstrument.value = quotation.instrumentRef;
          selectedExpiryOption.value = "custom";
          customExpiryDate.value = quotation.expiresAt;
          common_vendor.index.setNavigationBarTitle({
            title: "编辑报价"
          });
        } catch (error) {
          console.error("加载报价详情失败:", error);
          common_vendor.index.showToast({
            title: "加载报价失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
        }
      });
    }
    function updateExpiryTime() {
      if (selectedExpiryOption.value === "custom") {
        return;
      }
      const option = expiryOptions.value.find((opt) => opt.value === selectedExpiryOption.value);
      if (option) {
        const now = /* @__PURE__ */ new Date();
        const expiryTime = new Date(now.getTime() + option.hours * 60 * 60 * 1e3);
        formData.value.expiresAt = expiryTime.toISOString();
      }
    }
    function onInstrumentChange(instrumentId, instrument) {
      formData.value.instrumentRefID = instrumentId || void 0;
      selectedInstrument.value = instrument || void 0;
    }
    function onPriceTypeTabChange(event) {
      const priceType = event.name;
      formData.value.priceType = priceType;
      if (priceType === "Fixed") {
        formData.value.instrumentRefID = void 0;
        selectedInstrument.value = void 0;
      }
    }
    function onExpiryOptionChange(value) {
      var _a;
      const selectedValue = typeof value === "string" ? value : (value == null ? void 0 : value.value) || ((_a = value == null ? void 0 : value.detail) == null ? void 0 : _a.value);
      selectedExpiryOption.value = selectedValue;
      if (selectedExpiryOption.value !== "custom") {
        updateExpiryTime();
      }
    }
    function onCustomExpiryChange(value) {
      var _a;
      const dateValue = typeof value === "string" ? value : ((_a = value == null ? void 0 : value.detail) == null ? void 0 : _a.value) || (value == null ? void 0 : value.value);
      customExpiryDate.value = dateValue;
      if (dateValue) {
        formData.value.expiresAt = new Date(dateValue).toISOString();
      }
    }
    function saveDraft() {
      return __async(this, null, function* () {
        try {
          const { valid } = yield formRef.value.validate();
          if (!valid) {
            return;
          }
        } catch (error) {
          console.error("表单验证失败:", error);
          return;
        }
        try {
          isSubmitting.value = true;
          const requestData = __spreadProps(__spreadValues({}, formData.value), {
            price: typeof formData.value.price === "string" ? parseFloat(formData.value.price) || 0 : formData.value.price
          });
          if (isEdit.value && quotationId.value) {
            const updateData = __spreadValues({
              id: quotationId.value
            }, requestData);
            yield api_quotation.updateQuotation(updateData);
          } else {
            yield api_quotation.saveQuotationDraft(requestData);
          }
          common_vendor.index.showToast({
            title: "保存成功",
            icon: "success"
          });
          setTimeout(() => {
            utils_index.navigateBackOrTo("/pages/quotes/my-list");
          }, 1500);
        } catch (error) {
          console.error("保存草稿失败:", error);
          common_vendor.index.showToast({
            title: "保存失败",
            icon: "error"
          });
        } finally {
          isSubmitting.value = false;
        }
      });
    }
    function publishQuotation() {
      return __async(this, null, function* () {
        try {
          const { valid } = yield formRef.value.validate();
          if (!valid) {
            return;
          }
        } catch (error) {
          console.error("表单验证失败:", error);
          return;
        }
        try {
          isSubmitting.value = true;
          const requestData = __spreadProps(__spreadValues({}, formData.value), {
            price: typeof formData.value.price === "string" ? parseFloat(formData.value.price) || 0 : formData.value.price
          });
          if (isEdit.value && quotationId.value) {
            const updateData = __spreadValues({
              id: quotationId.value
            }, requestData);
            yield api_quotation.updateQuotation(updateData);
            yield api_quotation.publishQuotation({
              id: quotationId.value,
              expiresAt: requestData.expiresAt
            });
          } else {
            const createData = __spreadProps(__spreadValues({}, requestData), {
              status: "Active"
            });
            yield api_quotation.saveQuotationDraft(createData);
          }
          common_vendor.index.showToast({
            title: "发布成功",
            icon: "success"
          });
          setTimeout(() => {
            utils_index.navigateBackOrTo("/pages/quotes/my-list");
          }, 1500);
        } catch (error) {
          console.error("发布报价失败:", error);
          common_vendor.index.showToast({
            title: "发布失败",
            icon: "error"
          });
        } finally {
          isSubmitting.value = false;
        }
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          type: "ring",
          color: "var(--app-color-primary)"
        })
      } : common_vendor.e({
        c: common_vendor.p({
          value: "sell",
          ["custom-class"]: "trade-intention-radio"
        }),
        d: common_vendor.p({
          value: "buy",
          ["custom-class"]: "trade-intention-radio"
        }),
        e: common_vendor.o(($event) => tradeIntentionValue.value = $event),
        f: common_vendor.p({
          inline: true,
          shape: "dot",
          ["custom-class"]: "trade-intention-radio-group",
          modelValue: tradeIntentionValue.value
        }),
        g: common_vendor.p({
          title: "交易意向",
          required: true
        }),
        h: common_vendor.o(($event) => formData.value.title = $event),
        i: common_vendor.p({
          label: "报价标题",
          ["label-width"]: "160rpx",
          placeholder: "请输入醒目的报价标题",
          required: true,
          clearable: true,
          maxlength: 20,
          ["show-word-limit"]: true,
          prop: "title",
          modelValue: formData.value.title
        }),
        j: common_vendor.o(($event) => formData.value.commodityName = $event),
        k: common_vendor.p({
          label: "商品名称",
          ["label-width"]: "160rpx",
          placeholder: "请输入或选择商品名称",
          required: true,
          clearable: true,
          options: commodityOptions.value,
          prop: "commodityName",
          modelValue: formData.value.commodityName
        }),
        l: common_vendor.o(($event) => formData.value.deliveryLocation = $event),
        m: common_vendor.p({
          label: "交货地点",
          ["label-width"]: "160rpx",
          placeholder: "请输入或选择交货地点",
          required: true,
          clearable: true,
          options: deliveryLocationOptions.value,
          prop: "deliveryLocation",
          modelValue: formData.value.deliveryLocation
        }),
        n: common_vendor.o(($event) => formData.value.brand = $event),
        o: common_vendor.p({
          label: "品牌",
          ["label-width"]: "160rpx",
          placeholder: "请输入或选择品牌（可选）",
          clearable: true,
          options: brandOptions.value,
          modelValue: formData.value.brand
        }),
        p: common_vendor.o(($event) => formData.value.specifications = $event),
        q: common_vendor.p({
          label: "规格说明",
          ["label-width"]: "160rpx",
          placeholder: "请输入规格说明（可选）",
          maxlength: 50,
          ["show-word-limit"]: true,
          ["auto-height"]: true,
          modelValue: formData.value.specifications
        }),
        r: common_vendor.p({
          title: "基本信息",
          border: true,
          ["custom-class"]: "form-section"
        }),
        s: common_vendor.p({
          title: "一口价",
          name: "Fixed"
        }),
        t: common_vendor.p({
          title: "基差报价",
          name: "Basis"
        }),
        v: common_vendor.p({
          title: "商议",
          name: "Negotiable"
        }),
        w: common_vendor.o(onPriceTypeTabChange),
        x: common_vendor.o(($event) => formData.value.priceType = $event),
        y: common_vendor.p({
          ["custom-class"]: "price-type-tabs",
          modelValue: formData.value.priceType
        }),
        z: showInstrumentSelector.value
      }, showInstrumentSelector.value ? {
        A: common_vendor.o(onInstrumentChange),
        B: common_vendor.o(($event) => formData.value.instrumentRefID = $event),
        C: common_vendor.p({
          label: "期货合约",
          ["label-width"]: "160rpx",
          placeholder: "请选择期货合约",
          required: true,
          modelValue: formData.value.instrumentRefID
        })
      } : {}, {
        D: showPriceInput.value
      }, showPriceInput.value ? {
        E: common_vendor.t(formData.value.priceType === "Fixed" ? "元" : "点"),
        F: common_vendor.o(($event) => formData.value.price = $event),
        G: common_vendor.p({
          label: formData.value.priceType === "Fixed" ? "价格" : "基差值",
          ["label-width"]: "160rpx",
          placeholder: formData.value.priceType === "Fixed" ? "请输入价格" : "请输入基差值",
          type: "digit",
          required: true,
          prop: "price",
          rules: priceValidationRule.value,
          modelValue: formData.value.price
        })
      } : {
        H: common_vendor.p({
          title: "价格",
          value: "价格面议，可通过沟通协商",
          label: ""
        })
      }, {
        I: common_vendor.p({
          title: "价格信息",
          border: true,
          ["custom-class"]: "form-section"
        }),
        J: common_vendor.sr(expiryPicker, "363e46db-22,363e46db-21", {
          "k": "expiryPicker"
        }),
        K: common_vendor.o(onExpiryOptionChange),
        L: common_vendor.o(($event) => selectedExpiryOption.value = $event),
        M: common_vendor.p({
          label: "有效期",
          ["label-width"]: "160rpx",
          placeholder: "请选择有效期",
          columns: expiryOptions.value,
          ["label-key"]: "label",
          ["value-key"]: "value",
          required: true,
          prop: "expiresAt",
          modelValue: selectedExpiryOption.value
        }),
        N: selectedExpiryOption.value === "custom"
      }, selectedExpiryOption.value === "custom" ? {
        O: common_vendor.sr(customDatePicker, "363e46db-23,363e46db-21", {
          "k": "customDatePicker"
        }),
        P: common_vendor.o(onCustomExpiryChange),
        Q: common_vendor.o(($event) => customExpiryDate.value = $event),
        R: common_vendor.p({
          label: "自定义过期时间",
          ["label-width"]: "160rpx",
          type: "date",
          required: true,
          modelValue: customExpiryDate.value
        })
      } : {}, {
        S: common_vendor.o(($event) => formData.value.description = $event),
        T: common_vendor.p({
          label: "补充说明",
          ["label-width"]: "160rpx",
          placeholder: "请输入补充说明（可选）",
          maxlength: 200,
          ["show-word-limit"]: true,
          ["auto-height"]: true,
          modelValue: formData.value.description
        }),
        U: common_vendor.p({
          title: "有效期设置",
          border: true,
          ["custom-class"]: "form-section"
        }),
        V: common_vendor.sr(formRef, "363e46db-2,363e46db-0", {
          "k": "formRef"
        }),
        W: common_vendor.p({
          model: formData.value,
          rules: formRules
        })
      }), {
        X: common_vendor.t(isEdit.value ? "保存修改" : "保存草稿"),
        Y: common_vendor.o(saveDraft),
        Z: common_vendor.p({
          type: "info",
          ["custom-class"]: "btn-draft",
          loading: isSubmitting.value
        }),
        aa: common_vendor.t(isEdit.value ? "更新并发布" : "直接发布"),
        ab: common_vendor.o(publishQuotation),
        ac: common_vendor.p({
          type: "primary",
          ["custom-class"]: "btn-publish",
          loading: isSubmitting.value
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-363e46db"]]);
wx.createPage(MiniProgramPage);
