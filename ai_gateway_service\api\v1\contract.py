"""
Contract inference API endpoints
"""

import logging
from fastapi import APIRouter, HTTPException

from schemas.quotation import ContractInferenceRequest, ContractInferenceResponse
from services.contract_inference import get_contract_inference_service

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/contract", tags=["Contract"])


@router.post("/infer", response_model=ContractInferenceResponse)
async def infer_contract(request: ContractInferenceRequest):
    """
    推断期货合约
    
    根据文本内容和商品名称，推断最合适的期货合约ID
    """
    try:
        service = get_contract_inference_service()
        result = await service.infer_contract(request)
        
        if not result.success:
            raise HTTPException(status_code=400, detail=result.message)
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Contract inference API error: {e}")
        raise HTTPException(status_code=500, detail="合约推断服务内部错误")