package dianjia

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/gin-gonic/gin"
)

type InstrumentRouter struct{}

// InitInstrumentRouter 初始化 期货合约 路由信息
func (s *InstrumentRouter) InitInstrumentRouter(privateRouter *gin.RouterGroup, publicRouter *gin.RouterGroup) {
	// 私有路由组（需要认证）
	s.InitInstrumentPrivateRouter(privateRouter)

	// 公开路由组（无需认证）
	s.InitInstrumentPublicRouter(publicRouter)
}

// InitInstrumentPrivateRouter 初始化私有期货合约路由（需要认证）
func (s *InstrumentRouter) InitInstrumentPrivateRouter(Router *gin.RouterGroup) {
	instrumentRouter := Router.Group("dianjia/instrument")
	instrumentApi := v1.ApiGroupApp.DianjiaApiGroup.InstrumentApi
	{
		instrumentRouter.POST("createInstrument", instrumentApi.CreateInstrument)                // 新建期货合约
		instrumentRouter.DELETE("deleteInstrument", instrumentApi.DeleteInstrument)              // 删除期货合约
		instrumentRouter.DELETE("deleteInstrumentByIds", instrumentApi.DeleteInstrumentByIds)    // 批量删除期货合约
		instrumentRouter.PUT("updateInstrument", instrumentApi.UpdateInstrument)                 // 更新期货合约
		instrumentRouter.GET("findInstrument", instrumentApi.FindInstrument)                     // 根据ID获取期货合约
		instrumentRouter.GET("getInstrumentList", instrumentApi.GetInstrumentList)               // 获取期货合约列表
		instrumentRouter.GET("getInstrumentsByExchange", instrumentApi.GetInstrumentsByExchange) // 获取按交易所分组的期货合约列表
	}
}

// InitInstrumentPublicRouter 初始化公开期货合约路由（无需认证）
func (s *InstrumentRouter) InitInstrumentPublicRouter(Router *gin.RouterGroup) {
	// 公开的期货合约相关路由（无需认证）
	publicInstrumentRouter := Router.Group("dianjia/instrument")
	instrumentApi := v1.ApiGroupApp.DianjiaApiGroup.InstrumentApi
	{
		publicInstrumentRouter.GET("getInstrumentSelectList", instrumentApi.GetInstrumentSelectList) // 获取期货合约选择器列表
	}
}
