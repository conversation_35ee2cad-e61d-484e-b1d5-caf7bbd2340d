"use strict";
const common_vendor = require("./common/vendor.js");
if (!Array) {
  const _easycom_wd_select_picker2 = common_vendor.resolveComponent("wd-select-picker");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_select_picker2 + _component_layout_default_uni)();
}
const _easycom_wd_select_picker = () => "./node-modules/wot-design-uni/components/wd-select-picker/wd-select-picker.js";
if (!Math) {
  _easycom_wd_select_picker();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "CombinationSelector",
  props: {
    combinations: {},
    modelValue: {},
    currentDisplay: {},
    title: { default: "选择交易组合" },
    disabled: { type: <PERSON><PERSON><PERSON>, default: false },
    autoComplete: { type: <PERSON><PERSON><PERSON>, default: true }
  },
  emits: ["update:modelValue", "change"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    function handleConfirm({ value }) {
      console.log("SelectPicker confirm:", value);
      if (value !== void 0 && value !== null) {
        emit("update:modelValue", value);
        emit("change", value);
      }
    }
    function handleChange({ value }) {
      console.log("SelectPicker change:", value);
      if (value !== void 0 && value !== null && props.autoComplete) {
        emit("update:modelValue", value);
        emit("change", value);
      }
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.t(_ctx.currentDisplay.setterName || "请选择"),
        b: common_vendor.t(_ctx.currentDisplay.instrumentName || "交易组合"),
        c: common_vendor.o(handleConfirm),
        d: common_vendor.o(handleChange),
        e: common_vendor.p({
          columns: _ctx.combinations,
          ["model-value"]: _ctx.modelValue,
          title: _ctx.title,
          disabled: _ctx.disabled,
          type: "radio",
          ["show-confirm"]: !_ctx.autoComplete,
          filterable: true,
          ["filter-placeholder"]: "搜索交易组合",
          ["use-default-slot"]: true,
          ["root-portal"]: true,
          ["z-index"]: 9999,
          ["safe-area-inset-bottom"]: true
        })
      };
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-98d348b7"]]);
exports.MiniProgramPage = MiniProgramPage;
