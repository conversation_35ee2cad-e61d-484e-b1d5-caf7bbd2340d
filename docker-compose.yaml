# 设置mysql，redis持久化保存
volumes:
  mysql:
  redis:
# 声明一个名为network的networks,subnet为network的子网地址,默认网关是*********
networks:
  network:
    ipam:
      driver: default
      config:
        - subnet: '*********/16'

services:
  web:
    image: registry.cn-hangzhou.aliyuncs.com/wuzhongyu/gva-web:latest
    container_name: gva-web
    restart: always
    environment:
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    ports:
      - '8080:8080'
    depends_on:
      - server
    command: [ 'nginx-debug', '-g', 'daemon off;' ]
    networks:
      network:
        ipv4_address: *********1

  server:
    image: registry.cn-hangzhou.aliyuncs.com/wuzhongyu/gva-server:latest
    container_name: gva-server
    restart: always
    environment:
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
    ports:
      - '8888:8888'
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    links:
      - mysql
      - redis
    networks:
      network:
        ipv4_address: **********

  hq:
    image: registry.cn-hangzhou.aliyuncs.com/wuzhongyu/gva-hq:latest
    container_name: gva-hq
    restart: always
    environment:
      - LANG=C.UTF-8
      - LC_ALL=C.UTF-8
      - HQ_REDIS_HOST=redis
      - HQ_REDIS_PASSWORD=gA8xR0dY3zT5
    ports:
      - '40899:40899'

    networks:
      network:
        ipv4_address: *********5

  mysql:
    image: mysql:8.0.21       # 如果您是 arm64 架构：如 MacOS 的 M1，请修改镜像为 image: mysql/mysql-server:8.0.21
    container_name: gva-mysql
    command: mysqld --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci #设置utf8字符集
    restart: always
    ports:
      - "13306:3306"  # host物理直接映射端口为13306
    environment:
      LANG: C.UTF-8
      LC_ALL: C.UTF-8
      MYSQL_ROOT_PASSWORD: 'cF8jO6rU5fZ6' # root管理员用户密码
      MYSQL_DATABASE: 'dianjia' # 初始化启动时要创建的数据库的名
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "gva", "-pcF8jO6rU5fZ6"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - mysql:/var/lib/mysql
    networks:
      network:
        ipv4_address: *********3

  redis:
    image: redis:6.0.6
    container_name: gva-redis # 容器名
    restart: always
    ports:
      - '16379:6379'
    command: redis-server --requirepass gA8xR0dY3zT5  # 设置Redis密码
    healthcheck:
      test: ["CMD-SHELL", "redis-cli -a gA8xR0dY3zT5 ping | grep PONG || exit 1"]
      interval: 10s
      timeout: 5s
      retries: 3
    volumes:
      - redis:/data
    environment:
      LANG: C.UTF-8
      LC_ALL: C.UTF-8
      REDIS_PASSWORD: gA8xR0dY3zT5
    networks:
      network:
        ipv4_address: *********4

