import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { getAllCommodityList } from '@/api/commodity'
import type { ICommodity, ICommoditySectionMap } from '@/types/commodity'
import { toast } from '@/utils/toast'

export const useCommodityStore = defineStore(
  'commodity',
  () => {
    // ==================== 响应式数据声明 ====================
    
    const commodityList = ref<ICommodity[]>([])
    const isLoading = ref<boolean>(false)
    const lastUpdated = ref<Date | null>(null)
    
    // ==================== 计算属性 ====================
    
    // 按版块分组的商品列表
    const commoditySectionMap = computed<ICommoditySectionMap>(() => {
      const sectionMap: ICommoditySectionMap = {}
      
      commodityList.value.forEach(commodity => {
        const section = commodity.section || '其他'
        if (!sectionMap[section]) {
          sectionMap[section] = []
        }
        sectionMap[section].push(commodity)
      })
      
      // 对每个section内的商品按名称排序
      Object.keys(sectionMap).forEach(section => {
        sectionMap[section].sort((a, b) => a.name.localeCompare(b.name))
      })
      
      return sectionMap
    })
    
    // 获取所有版块名称列表
    const sectionList = computed<string[]>(() => {
      return Object.keys(commoditySectionMap.value).sort()
    })
    
    // 根据ProductID快速查找商品
    const getCommodityByProductID = computed(() => {
      const map = new Map<string, ICommodity>()
      commodityList.value.forEach(commodity => {
        map.set(commodity.product_id, commodity)
      })
      return (productID: string) => map.get(productID)
    })
    
    // 获取商品名称映射
    const commodityNameMap = computed(() => {
      const map = new Map<string, string>()
      commodityList.value.forEach(commodity => {
        map.set(commodity.product_id, commodity.name)
      })
      return map
    })
    
    // ==================== 数据管理方法 ====================
    
    /**
     * 加载所有商品数据
     * @param forceRefresh 是否强制刷新（跳过缓存）
     */
    async function loadCommodityList(forceRefresh: boolean = false): Promise<void> {
      // 如果有缓存且不强制刷新，则跳过
      if (!forceRefresh && commodityList.value.length > 0 && lastUpdated.value) {
        try {
          // 确保 lastUpdated.value 是 Date 对象
          const lastUpdateTime = lastUpdated.value instanceof Date
            ? lastUpdated.value
            : new Date(lastUpdated.value)

          // 检查是否是有效的日期
          if (!isNaN(lastUpdateTime.getTime())) {
            const cacheAge = Date.now() - lastUpdateTime.getTime()
            if (cacheAge < 5 * 60 * 1000) { // 5分钟缓存
              return
            }
          }
        } catch (error) {
          // 如果日期转换失败，清除无效的缓存时间
          console.warn('无效的缓存时间，将重新加载数据:', error)
          lastUpdated.value = null
        }
      }
      
      try {
        isLoading.value = true
        const res = await getAllCommodityList()
        
        if (res.code === 0) {
          commodityList.value = res.data
          lastUpdated.value = new Date()
        } else {
          throw new Error(res.msg || '获取商品列表失败')
        }
      } catch (error) {
        console.error('加载商品列表失败:', error)
        toast.error('加载商品列表失败')
        throw error
      } finally {
        isLoading.value = false
      }
    }
    
    /**
     * 根据版块获取商品列表
     * @param section 版块名称
     */
    function getCommoditiesBySection(section: string): ICommodity[] {
      return commoditySectionMap.value[section] || []
    }
    
    /**
     * 搜索商品
     * @param keyword 搜索关键词
     */
    function searchCommodities(keyword: string): ICommodity[] {
      if (!keyword.trim()) {
        return commodityList.value
      }
      
      const searchTerm = keyword.trim().toLowerCase()
      return commodityList.value.filter(commodity => 
        commodity.name.toLowerCase().includes(searchTerm) ||
        commodity.product_id.toLowerCase().includes(searchTerm)
      )
    }
    
    /**
     * 根据ProductID数组获取商品列表
     * @param productIDs 商品ProductID数组
     */
    function getCommoditiesByProductIDs(productIDs: string[]): ICommodity[] {
      return commodityList.value.filter(commodity => 
        productIDs.includes(commodity.product_id)
      )
    }
    
    /**
     * 清除缓存，强制重新加载
     */
    function clearCache(): void {
      commodityList.value = []
      lastUpdated.value = null
    }
    
    return {
      // 状态
      commodityList,
      isLoading,
      lastUpdated,
      
      // 计算属性
      commoditySectionMap,
      sectionList,
      getCommodityByProductID,
      commodityNameMap,
      
      // 方法
      loadCommodityList,
      getCommoditiesBySection,
      searchCommodities,
      getCommoditiesByProductIDs,
      clearCache,
    }
  },
  {
    persist: {
      key: 'commodity-store',
      storage: uni.getStorageSync && uni.setStorageSync ? {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      } : undefined,
      paths: ['commodityList', 'lastUpdated'],
    },
  }
)