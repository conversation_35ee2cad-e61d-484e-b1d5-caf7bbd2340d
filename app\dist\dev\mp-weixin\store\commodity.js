"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const api_commodity = require("../api/commodity.js");
const utils_toast = require("../utils/toast.js");
const useCommodityStore = common_vendor.defineStore(
  "commodity",
  () => {
    const commodityList = common_vendor.ref([]);
    const isLoading = common_vendor.ref(false);
    const lastUpdated = common_vendor.ref(null);
    const commoditySectionMap = common_vendor.computed(() => {
      const sectionMap = {};
      commodityList.value.forEach((commodity) => {
        const section = commodity.section || "其他";
        if (!sectionMap[section]) {
          sectionMap[section] = [];
        }
        sectionMap[section].push(commodity);
      });
      Object.keys(sectionMap).forEach((section) => {
        sectionMap[section].sort((a, b) => a.name.localeCompare(b.name));
      });
      return sectionMap;
    });
    const sectionList = common_vendor.computed(() => {
      return Object.keys(commoditySectionMap.value).sort();
    });
    const getCommodityByProductID = common_vendor.computed(() => {
      const map = /* @__PURE__ */ new Map();
      commodityList.value.forEach((commodity) => {
        map.set(commodity.product_id, commodity);
      });
      return (productID) => map.get(productID);
    });
    const commodityNameMap = common_vendor.computed(() => {
      const map = /* @__PURE__ */ new Map();
      commodityList.value.forEach((commodity) => {
        map.set(commodity.product_id, commodity.name);
      });
      return map;
    });
    function loadCommodityList(forceRefresh = false) {
      return __async(this, null, function* () {
        if (!forceRefresh && commodityList.value.length > 0 && lastUpdated.value) {
          try {
            const lastUpdateTime = lastUpdated.value instanceof Date ? lastUpdated.value : new Date(lastUpdated.value);
            if (!isNaN(lastUpdateTime.getTime())) {
              const cacheAge = Date.now() - lastUpdateTime.getTime();
              if (cacheAge < 5 * 60 * 1e3) {
                return;
              }
            }
          } catch (error) {
            console.warn("无效的缓存时间，将重新加载数据:", error);
            lastUpdated.value = null;
          }
        }
        try {
          isLoading.value = true;
          const res = yield api_commodity.getAllCommodityList();
          if (res.code === 0) {
            commodityList.value = res.data;
            lastUpdated.value = /* @__PURE__ */ new Date();
          } else {
            throw new Error(res.msg || "获取商品列表失败");
          }
        } catch (error) {
          console.error("加载商品列表失败:", error);
          utils_toast.toast.error("加载商品列表失败");
          throw error;
        } finally {
          isLoading.value = false;
        }
      });
    }
    function getCommoditiesBySection(section) {
      return commoditySectionMap.value[section] || [];
    }
    function searchCommodities(keyword) {
      if (!keyword.trim()) {
        return commodityList.value;
      }
      const searchTerm = keyword.trim().toLowerCase();
      return commodityList.value.filter(
        (commodity) => commodity.name.toLowerCase().includes(searchTerm) || commodity.product_id.toLowerCase().includes(searchTerm)
      );
    }
    function getCommoditiesByProductIDs(productIDs) {
      return commodityList.value.filter(
        (commodity) => productIDs.includes(commodity.product_id)
      );
    }
    function clearCache() {
      commodityList.value = [];
      lastUpdated.value = null;
    }
    return {
      // 状态
      commodityList,
      isLoading,
      lastUpdated,
      // 计算属性
      commoditySectionMap,
      sectionList,
      getCommodityByProductID,
      commodityNameMap,
      // 方法
      loadCommodityList,
      getCommoditiesBySection,
      searchCommodities,
      getCommoditiesByProductIDs,
      clearCache
    };
  },
  {
    persist: {
      key: "commodity-store",
      storage: common_vendor.index.getStorageSync && common_vendor.index.setStorageSync ? {
        getItem: (key) => common_vendor.index.getStorageSync(key),
        setItem: (key, value) => common_vendor.index.setStorageSync(key, value)
      } : void 0,
      paths: ["commodityList", "lastUpdated"]
    }
  }
);
exports.useCommodityStore = useCommodityStore;
