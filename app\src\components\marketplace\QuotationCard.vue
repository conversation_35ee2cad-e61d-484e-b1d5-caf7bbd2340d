<script lang="ts" setup>
import { computed } from 'vue'
import type { IQuotationResponse } from '@/types/quotation'
import { QUOTATION_PRICE_TYPE_CONFIG, QUOTATION_TRADE_TYPE_CONFIG } from '@/types/quotation'
import BasisPriceDisplay from './BasisPriceDisplay.vue'

defineOptions({
  name: 'QuotationCard'
})

// Props
interface Props {
  quotation: IQuotationResponse
  showPrice?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showPrice: false
})

// Emits
interface Emits {
  (e: 'click', quotation: IQuotationResponse): void
  (e: 'publisherClick', userID: number): void
}

const emit = defineEmits<Emits>()

// ==================== 计算属性 ====================

/**
 * 获取交易类型配置
 */
const tradeTypeConfig = computed(() => {
  return props.quotation.isBuyRequest ? QUOTATION_TRADE_TYPE_CONFIG.buy : QUOTATION_TRADE_TYPE_CONFIG.sell
})

/**
 * 获取价格类型配置
 */
const priceTypeConfig = computed(() => {
  return QUOTATION_PRICE_TYPE_CONFIG[props.quotation.priceType]
})

/**
 * 是否为商议类型
 */
const isNegotiable = computed(() => {
  return props.quotation.priceType === 'Negotiable'
})

// ==================== 格式化和工具函数 ====================

/**
 * 格式化剩余时间显示
 * @param quotation 报价对象
 * @returns 格式化的时间字符串
 */
function formatRemainingTime(quotation: IQuotationResponse): string {
  if (quotation.isExpired) {
    return '已过期'
  }

  if (quotation.remainingHours <= 0) {
    return '即将过期'
  } else if (quotation.remainingHours < 24) {
    return `剩余 ${quotation.remainingHours} 小时`
  } else {
    const days = Math.floor(quotation.remainingHours / 24)
    return `剩余 ${days} 天`
  }
}

/**
 * 获取企业简称
 * @param fullName 完整企业名称
 * @returns 简化后的企业名称
 */
function getCompanyShortName(fullName: string): string {
  if (!fullName) return '未知企业'

  if (fullName.length > 8) {
    return fullName.substring(0, 8) + '...'
  }
  return fullName
}

/**
 * 智能数字格式化函数
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 */
function formatLargeNumber(num: number): string {
  const absNum = Math.abs(num)
  const sign = num < 0 ? '-' : ''

  // 小于万的数字直接显示
  if (absNum < 10000) {
    return sign + absNum.toLocaleString()
  }

  // 万级别显示
  if (absNum < *********) { // 1亿以下
    const wan = absNum / 10000
    if (wan >= 100) {
      return sign + Math.round(wan) + '万'
    } else {
      return sign + wan.toFixed(1) + '万'
    }
  }

  // 亿级别显示
  const yi = absNum / *********
  if (yi >= 100) {
    return sign + Math.round(yi) + '亿'
  } else {
    return sign + yi.toFixed(1) + '亿'
  }
}

/**
 * 格式化基差价格，确保显示正负符号
 * @param price 基差价格
 * @returns 带正负符号的格式化价格
 */
function formatBasisPrice(price: number): string {
  const formatted = formatLargeNumber(Math.abs(price))
  return price >= 0 ? `+${formatted}` : `-${formatted}`
}

/**
 * 动态字体大小计算
 * @param text 文本内容
 * @param baseSize 基础字体大小
 * @param minSize 最小字体大小
 * @returns 计算后的字体大小
 */
function calculateDynamicFontSize(text: string, baseSize: number = 48, minSize: number = 28): number {
  const textLength = text.length

  // 根据文字长度动态调整字体大小
  if (textLength <= 6) return baseSize
  if (textLength <= 8) return Math.max(baseSize * 0.9, minSize)
  if (textLength <= 10) return Math.max(baseSize * 0.8, minSize)
  return minSize
}


// ==================== 事件处理 ====================

/**
 * 处理卡片点击事件
 */
function handleCardClick(): void {
  emit('click', props.quotation)
}

/**
 * 处理发布者名称点击事件
 */
function handlePublisherClick(event: Event): void {
  // 确保事件对象存在
  if (!event) return
  
  // 强制阻止事件冒泡和默认行为
  event.preventDefault()
  event.stopPropagation()
  event.cancelBubble = true // 兼容微信小程序
  
  // 阻止事件继续传播
  if (typeof event.stopImmediatePropagation === 'function') {
    event.stopImmediatePropagation()
  }
  
  if (props.quotation.userID) {
    emit('publisherClick', props.quotation.userID)
  }
}
</script>

<template>
  <view 
    class="quotation-card" 
    :class="{ 'buy-request': quotation.isBuyRequest }"
    :style="{
      background: tradeTypeConfig.bgColor,
      borderColor: tradeTypeConfig.borderColor
    }"
    @click="handleCardClick"
  >
    <!-- 标签区域 - 右上角显示交易类型和价格类型 -->
    <view class="card-tags">
      <!-- 交易类型标签 -->
      <wd-tag
        :type="quotation.isBuyRequest ? 'success' : 'primary'"
        size="small"
        custom-class="trade-type-tag"
      >
        {{ tradeTypeConfig.label }}
      </wd-tag>
      <!-- 价格类型标签 -->
      <wd-tag
        :type="quotation.priceType === 'Fixed' ? 'primary' : quotation.priceType === 'Basis' ? 'danger' : 'warning'"
        size="small"
        custom-class="price-type-tag"
      >
        {{ priceTypeConfig.label }}
      </wd-tag>
    </view>

    <!-- 卡片主体内容 -->
    <view class="card-body">
      <!-- 左侧内容区域 (2/3) -->
      <view class="left-content">
        <!-- 标题 -->
        <text class="quotation-title">{{ quotation.title }}</text>

        <!-- 报价人和有效期 -->
        <view class="publisher-info" @click.stop="handlePublisherClick">
          <text 
            class="publisher-name clickable"
          >
            {{ getCompanyShortName(quotation.user?.nickName || '') }}
          </text>
          <text class="remaining-time" :class="{ expired: quotation.isExpired }">
            {{ formatRemainingTime(quotation) }}
          </text>
        </view>

        <!-- 标签信息 -->
        <view class="tag-info">
          <wd-tag v-if="quotation.commodityName" type="primary" size="small">
            {{ quotation.commodityName }}
          </wd-tag>
          <wd-tag v-if="quotation.deliveryLocation" type="success" size="small">
            {{ quotation.deliveryLocation }}
          </wd-tag>
          <wd-tag v-if="quotation.brand" type="warning" size="small">
            {{ quotation.brand }}
          </wd-tag>
        </view>
      </view>

      <!-- 右侧价格区域 (1/3) -->
      <view class="right-content">
        <!-- 价格显示区域 -->
        <view class="price-display">
          <template v-if="isNegotiable">
            <!-- 商议价格显示 -->
            <text class="negotiable-price">
              商议
            </text>
          </template>
          <template v-else-if="quotation.priceType === 'Fixed'">
            <!-- 一口价显示 -->
            <text
              class="price-value adaptive-price"
              :style="{
                fontSize: calculateDynamicFontSize(formatLargeNumber(quotation.price)) + 'rpx'
              }"
              :title="quotation.price.toLocaleString()"
            >
              {{ formatLargeNumber(quotation.price) }}
            </text>
          </template>
          <template v-else>
            <!-- 基差显示 -->
            <BasisPriceDisplay
              :instrument-id="quotation.instrumentRef?.instrument_id || ''"
              :exchange-id="quotation.instrumentRef?.exchange_id || ''"
              :price="quotation.price"
              :show-price="showPrice"
            />
          </template>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$bg-card: rgba(255, 255, 255, 0.95);
$radius-lg: 20rpx;
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);

.quotation-card {
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  padding: 24rpx;
  margin-bottom: 20rpx;
  box-shadow: $shadow-md;
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  border: 2rpx solid;
  
  // 默认出售样式 - 细微的边缘渐变效果
  background: radial-gradient(ellipse at center, rgba(255, 255, 255, 1) 60%, rgba(230, 247, 255, 0.8) 90%, rgba(186, 231, 255, 0.6) 100%);
  border-color: #91d5ff;
  
  // 求购样式 - 细微的边缘渐变效果
  &.buy-request {
    background: radial-gradient(ellipse at center, rgba(255, 255, 255, 1) 60%, rgba(246, 255, 237, 0.8) 90%, rgba(217, 247, 190, 0.6) 100%);
    border-color: #b7eb8f;
  }

  // 卡片右上角的标签区域
  .card-tags {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    display: flex;
    flex-direction: row; // 改为左右排列
    gap: 8rpx; // 增加间距

    :deep(.wd-tag) {
      font-size: 20rpx;
      padding: 4rpx 12rpx;
      font-weight: 500;
      
      &.trade-type-tag {
        border-radius: 0;
      }
      
      &.price-type-tag {
        border-radius: 0rpx 0rpx 0rpx 0;
      }

      &[data-type="primary"] {
        background: $primary-gradient !important;
        box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
      }

      &[data-type="success"] {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%) !important;
        box-shadow: 0 2rpx 8rpx rgba(82, 196, 26, 0.3);
      }

      &[data-type="danger"] {
        background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%) !important;
        box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
      }
      
      &[data-type="warning"] {
        background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%) !important;
        box-shadow: 0 2rpx 8rpx rgba(250, 173, 20, 0.3);
      }
    }
  }

  &:hover {
    box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
    transform: translateY(-4rpx);
    border-color: rgba(102, 126, 234, 0.2);
  }

  &:active {
    transform: translateY(-2rpx);
    box-shadow: $shadow-md;
  }

  .card-body {
    display: flex;
    align-items: stretch;
    gap: 24rpx;
    width: 100%;

    .left-content {
      flex: 2;
      min-width: 0; /* 关键：允许flex子项收缩 */
      max-width: 66.666%; /* 强制限制最大宽度为2/3 */
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      min-height: 120rpx;
      overflow: hidden; /* 防止内容溢出 */
      box-sizing: border-box;

      .quotation-title {
        font-size: 32rpx;
        font-weight: 600;
        color: #1a1a1a;
        line-height: 1.4;
        margin-bottom: 12rpx;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 100%; /* 确保标题不超过容器宽度 */
      }

      .publisher-info {
        display: flex;
        align-items: flex-start;
        gap: 16rpx;
        margin-bottom: 16rpx;
        min-width: 0; /* 允许收缩 */

        .publisher-name {
          font-size: 26rpx;
          color: #666;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          flex: 1;
          min-width: 0;
          
          &.clickable {
            color: $primary-color;
            text-decoration: underline;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            z-index: 10; // 确保点击层级正确
            
            // 添加明确的点击区域
            &::before {
              content: '';
              position: absolute;
              top: -10rpx;
              left: -10rpx;
              right: -10rpx;
              bottom: -10rpx;
              z-index: -1;
              border-radius: 8rpx;
              transition: background-color 0.3s ease;
            }
            
            &:hover {
              color: #764ba2;
              text-decoration: none;
              
              &::before {
                background-color: rgba(102, 126, 234, 0.05);
              }
            }
            
            &:active {
              opacity: 0.7;
              
              &::before {
                background-color: rgba(102, 126, 234, 0.1);
              }
            }
          }
        }

        .remaining-time {
          font-size: 24rpx;
          color: #52c41a;
          padding: 4rpx 8rpx;
          background: rgba(82, 196, 26, 0.1);
          border-radius: 8rpx;
          flex-shrink: 0; /* 不允许收缩 */

          &.expired {
            color: #ff4d4f;
            background: rgba(255, 77, 79, 0.1);
          }
        }
      }

      .tag-info {
        display: flex;
        align-items: center;
        gap: 12rpx;
        flex-wrap: nowrap; /* 改为不换行，防止高度增加 */
        min-width: 0; /* 允许收缩 */
        overflow: hidden; /* 防止标签溢出 */
        width: 100%; /* 确保不超过容器宽度 */

        :deep(.wd-tag) {
          font-size: 22rpx;
          border-radius: 12rpx;
          padding: 4rpx 12rpx;
          flex-shrink: 1; /* 允许标签收缩 */
          min-width: 0;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 120rpx; /* 限制单个标签最大宽度 */
        }
      }
    }

    .right-content {
      flex: 1;
      min-width: 0; /* 允许收缩 */
      max-width: 33.333%; /* 强制限制最大宽度为1/3 */
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      justify-content: center; /* 居中对齐 */
      min-height: 120rpx;
      overflow: hidden; /* 防止内容溢出 */

      .price-display {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center; /* 改为居中对齐 */
        text-align: center; /* 改为居中对齐 */
        padding: 12rpx 0;
        min-width: 0; /* 允许收缩 */
        overflow: hidden; /* 防止内容溢出 */
        height: 80rpx; /* 设置固定高度，确保跨卡片对齐 */

        .price-value {
          font-weight: 900;
          color: #1890ff;
          line-height: 1.0;
          text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.15);
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          max-width: 100%;
          transition: font-size 0.3s ease; /* 字体大小变化动画 */
        }
        
        .negotiable-price {
          font-size: 36rpx;
          font-weight: 600;
          color: #faad14;
          text-align: center;
          background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
          background-clip: text;
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          text-shadow: 0 2rpx 4rpx rgba(250, 173, 20, 0.15);
        }

      }
    }
  }
}

// 自适应价格显示通用样式
.adaptive-price {
  // 确保价格文字在容器内完整显示
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  // 添加hover效果显示完整价格
  &:hover {
    position: relative;
    z-index: 100;

    &::after {
      content: attr(title);
      position: absolute;
      top: -40rpx;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8rpx 12rpx;
      border-radius: 8rpx;
      font-size: 24rpx;
      white-space: nowrap;
      z-index: 101;
      pointer-events: none;
      opacity: 0;
      animation: fadeIn 0.3s ease forwards;
    }
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5rpx);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

// 响应式字体大小
@media (max-width: 750rpx) {
  .adaptive-price {
    // 小屏幕下进一步缩小字体
    &.price-value {
      font-size: clamp(24rpx, 5vw, 40rpx) !important;
    }
  }
}
</style>
