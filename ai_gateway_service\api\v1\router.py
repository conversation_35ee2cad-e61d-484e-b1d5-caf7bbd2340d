"""
Main API router for v1 endpoints
"""

from fastapi import APIRouter
from .endpoints import quotations
from . import contract

api_router = APIRouter()

# Include quotation-related endpoints
api_router.include_router(
    quotations.router, 
    prefix="/quotations", 
    tags=["Quotations"]
)

# Include contract-related endpoints
api_router.include_router(
    contract.router,
    tags=["Contract"]
)

# Future endpoints can be added here
# api_router.include_router(data_analysis.router, prefix="/data", tags=["Data Analysis"])
# api_router.include_router(documents.router, prefix="/documents", tags=["Documents"])