<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "报价详情",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { onLoad, onShareAppMessage, onShareTimeline } from '@dcloudio/uni-app'
import { useUserStore } from '@/store/user'
import {
  getQuotationDetail,
  getPublicQuotationDetail,
  publishQuotation,
  toggleQuotationStatus,
  deleteQuotation
} from '@/api/quotation'
import type { IQuotationResponse } from '@/types/quotation'
import { navigateBackOrTo, navigateToPage } from '@/utils'

defineOptions({
  name: 'QuotationDetail'
})

function goBack() {
  navigateBackOrTo('/pages/quotes/my-list')
}

const userStore = useUserStore()
const quotationId = ref<number>()
const isLoading = ref(false)
const quotation = ref<IQuotationResponse>()
const isPublicAccess = ref<boolean>(false) // 标识是否从公开链接访问

const statusConfig = {
  Draft: { label: '草稿', description: '报价草稿，未公开发布' },
  Active: { label: '上线', description: '报价已公开，可被其他用户查看' }
}

const isOwner = computed(() => quotation.value && userStore.userInfo && quotation.value.userID === userStore.userInfo.ID)
const isPublicView = computed(() => !isOwner.value)
// 发布者查看时，无论什么状态都要显示相应的操作按钮
const canEdit = computed(() => isOwner.value) // 发布者任何状态都可以编辑
const canPublish = computed(() => isOwner.value && quotation.value?.status === 'Draft') // 只有草稿状态才显示发布按钮
const canToggleStatus = computed(() => isOwner.value && quotation.value?.status === 'Active') // 只有发布状态才显示下架按钮
const canDelete = computed(() => isOwner.value && quotation.value?.status === 'Draft') // 只有草稿状态才显示删除按钮
const canShare = computed(() => isOwner.value && quotation.value?.status === 'Active') // 只有发布状态才显示分享按钮
const showContactButton = computed(() => isPublicView.value && quotation.value?.status === 'Active')

onLoad((options) => {
  if (options?.id) {
    quotationId.value = parseInt(options.id)
    // 判断是否从公开分享链接访问（可以通过URL参数、来源页面等判断）
    isPublicAccess.value = options?.from === 'share' || options?.from === 'public' || options?.from === 'marketplace'
    loadQuotationDetail()
  } else {
    uni.showToast({ title: '报价ID不存在', icon: 'error' })
    setTimeout(() => navigateBackOrTo('/pages/quotes/my-list'), 1500)
  }
})

async function loadQuotationDetail() {
  if (!quotationId.value) return
  isLoading.value = true
  try {
    let res
    if (isPublicAccess.value || !userStore.isLoggedIn) {
      // 如果是公开访问或未登录，使用公开接口
      res = await getPublicQuotationDetail(quotationId.value)
    } else {
      // 如果是登录用户访问，使用带权限验证的接口
      res = await getQuotationDetail(quotationId.value)
    }
    quotation.value = res.data
    uni.setNavigationBarTitle({ title: quotation.value.title || '报价详情' })
  } catch (error) {
    console.error('加载报价详情失败:', error)
    uni.showToast({ title: '加载失败', icon: 'error' })
    setTimeout(() => navigateBackOrTo('/pages/quotes/my-list'), 1500)
  } finally {
    isLoading.value = false
  }
}

function editQuotation() {
  if (!quotation.value) return
  navigateToPage({ url: `/pages/quotes/edit?id=${quotation.value.id}` })
}

async function publishQuotationItem() {
  if (!quotation.value) return
  uni.showLoading({ title: '发布中...' })
  try {
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + 7)
    await publishQuotation({ id: quotation.value.id, expiresAt: expiresAt.toISOString() })
    uni.showToast({ title: '发布成功', icon: 'success' })
    await loadQuotationDetail()
  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({ title: '发布失败', icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

async function toggleQuotationStatusItem() {
  if (!quotation.value) return
  // 现在只有发布状态才会显示下架按钮，所以这里只处理下架逻辑
  const actionText = '下架'
  const confirmText = '确定要将此报价下架吗？下架后报价将变为草稿状态。'

  const { confirm } = await uni.showModal({ title: `确认${actionText}`, content: confirmText })
  if (!confirm) return

  uni.showLoading({ title: `${actionText}中...` })
  try {
    await toggleQuotationStatus(quotation.value.id)
    uni.showToast({ title: `${actionText}成功`, icon: 'success' })
    await loadQuotationDetail()
  } catch (error) {
    console.error('下架失败:', error)
    uni.showToast({ title: `${actionText}失败`, icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

async function deleteQuotationItem() {
  if (!quotation.value) return
  const { confirm } = await uni.showModal({ title: '确认删除', content: '确定要删除这个报价草稿吗？' })
  if (!confirm) return

  uni.showLoading({ title: '删除中...' })
  try {
    await deleteQuotation(quotation.value.id)
    uni.showToast({ title: '删除成功', icon: 'success' })
    setTimeout(() => navigateBackOrTo('/pages/quotes/my-list'), 1500)
  } catch (error) {
    console.error('删除报价失败:', error)
    uni.showToast({ title: '删除失败', icon: 'error' })
  } finally {
    uni.hideLoading()
  }
}

function contactPublisher() {
  if (!quotation.value) return
  uni.showToast({ title: '功能开发中', icon: 'none' })
}

function callPhone(phoneNumber: string) {
  if (!phoneNumber) return
  uni.makePhoneCall({ phoneNumber })
}

function formatPrice(q: IQuotationResponse): string {
  if (q.priceType === 'Fixed') return `¥ ${q.price.toFixed(2)}`
  if (q.priceType === 'Basis' && q.instrumentRef) {
    return `${q.instrumentRef.instrument_id} ${q.price >= 0 ? '+ ' : ''}${q.price}`
  }
  if (q.priceType === 'Negotiable') return '价格面议'
  return q.price.toString()
}

function getPriceTypeLabel(priceType: string): string {
  switch (priceType) {
    case 'Fixed':
      return '一口价'
    case 'Basis':
      return '基差报价'
    case 'Negotiable':
      return '商议'
    default:
      return '未知类型'
  }
}

function getPriceTypeClass(priceType: string): string {
  switch (priceType) {
    case 'Fixed':
      return 'fixed-type'
    case 'Basis':
      return 'basis-type'
    case 'Negotiable':
      return 'negotiable-type'
    default:
      return 'unknown-type'
  }
}

function formatDateTime(dateTime: string): string {
  const d = new Date(dateTime)
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')} ${String(d.getHours()).padStart(2, '0')}:${String(d.getMinutes()).padStart(2, '0')}`
}

function formatRemainingTime(): string {
  if (!quotation.value || quotation.value.status !== 'Active') return ''
  if (quotation.value.isExpired) return '已过期'
  const { remainingHours } = quotation.value
  if (remainingHours <= 0) return '即将过期'
  if (remainingHours < 24) return `剩余 ${remainingHours} 小时`
  return `剩余 ${Math.floor(remainingHours / 24)} 天`
}

// ==================== 微信小程序分享功能 ====================

// 微信小程序分享到聊天
onShareAppMessage(() => {
  if (!quotation.value) return {}
  
  const title = quotation.value.title
  const path = `/pages/quotes/detail?id=${quotation.value.id}&from=share`
  
  return {
    title,
    path,
    imageUrl: quotation.value.user?.headerImg || 'static/logo.svg'
  }
})

// 微信小程序分享到朋友圈
onShareTimeline(() => {
  if (!quotation.value) return {}
  
  const title = `${quotation.value.title} - ${quotation.value.commodityName}`
  const query = `share=timeline&id=${quotation.value.id}&from=share`
  
  return {
    title,
    query,
    imageUrl: quotation.value.user?.headerImg || 'static/logo.svg'
  }
})

function shareQuotation() {
  if (!quotation.value) return
  
  // 微信小程序环境下，不需要调用 uni.share，系统会自动处理分享
  // 这里可以添加一些提示或统计
  uni.showToast({
    title: '请点击右上角分享',
    icon: 'none',
    duration: 2000
  })
}
</script>

<template>
  <view class="app-container">
    <view class="app-content">
      <view v-if="isLoading" class="flex-col-center min-h-[80vh]">
        <wd-loading custom-class="loading-spinner" />
        <text class="text-caption mt-2">加载中...</text>
      </view>
      
      <view v-else-if="quotation" class="space-y-4 pb-[180rpx]">
        <!-- 发布者信息 -->
        <view v-if="isPublicView && quotation.user" class="app-card animate-slide-up">
          <view class="app-card-header">
            <h2 class="app-text-subtitle">报价方信息</h2>
          </view>
          <view class="app-card-body">
            <wd-cell-group border>
              <wd-cell v-if="quotation.user?.companyName" title="发布企业" :value="quotation.user.companyName" />
              <wd-cell v-if="quotation.user?.nickName" title="联系人" :value="quotation.user.nickName" />
              <wd-cell title="联系电话">
                <view class="flex items-center justify-end" @click="callPhone(quotation.user.phone)">
                  <text class="text-link mr-1">{{ quotation.user.phone }}</text>
                  <wd-icon name="phone" class="text-primary-600" />
                </view>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>

        <!-- 主要信息区域 -->
        <view class="app-card animate-slide-up" style="animation-delay: 0.1s">
          <view class="app-card-body">
            <view class="flex justify-end gap-2 mb-4">
              <view class="px-2 py-1 rounded-full text-xs font-medium text-white"
                :class="quotation.isBuyRequest ? 'bg-success' : 'bg-primary-500'">
                {{ quotation.isBuyRequest ? '求购' : '出售' }}
              </view>
              <view class="px-2 py-1 rounded-full text-xs font-medium text-white"
                :class="getPriceTypeClass(quotation.priceType)">
                {{ getPriceTypeLabel(quotation.priceType) }}
              </view>
            </view>
            
            <view class="mb-4">
              <h1 class="text-title mb-2">{{ quotation.title }}</h1>
              <text class="text-2xl font-bold text-primary-600">{{ formatPrice(quotation) }}</text>
            </view>
            
            <view class="bg-primary-50 rounded-lg p-3 space-y-2">
              <view class="flex justify-between items-center">
                <text class="text-caption">商品种类</text>
                <text class="text-body font-medium text-primary-600">{{ quotation.commodityName || '-' }}</text>
              </view>
              <view v-if="quotation.brand" class="flex justify-between items-center">
                <text class="text-caption">品牌</text>
                <text class="text-body font-medium text-primary-600">{{ quotation.brand }}</text>
              </view>
              <view class="flex justify-between items-center">
                <text class="text-caption">交货地点</text>
                <text class="text-body font-medium text-primary-600">{{ quotation.deliveryLocation }}</text>
              </view>
              <view v-if="quotation.priceType === 'Basis' && quotation.instrumentRef" class="flex justify-between items-center">
                <text class="text-caption">参考合约</text>
                <text class="text-body font-medium text-primary-600">{{ quotation.instrumentRef.instrument_name }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 次要信息区域 -->
        <view class="app-card animate-slide-up" style="animation-delay: 0.2s">
          <view class="app-card-body">
            <wd-cell-group>
              <wd-cell title="发布时间" :value="formatDateTime(quotation.createdAt)" />
              <wd-cell title="过期时间" :value="formatDateTime(quotation.expiresAt)" />
              <wd-cell v-if="quotation.status === 'Active'" title="剩余有效期">
                <text class="font-medium" :class="quotation.isExpired ? 'text-error' : 'text-success'">
                  {{ formatRemainingTime() }}
                </text>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>

        <!-- 详细信息区域 -->
        <view v-if="quotation.specifications || quotation.description" class="app-card animate-slide-up" style="animation-delay: 0.3s">
          <view class="app-card-body">
            <wd-cell-group>
              <wd-cell v-if="quotation.specifications" title="规格说明">
                <text class="text-body leading-relaxed">{{ quotation.specifications }}</text>
              </wd-cell>
              <wd-cell v-if="quotation.description" title="补充说明">
                <text class="text-body leading-relaxed">{{ quotation.description }}</text>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>
      </view>
      
      <view v-else class="flex-col-center min-h-[80vh]">
        <wd-img src="/static/images/error-404.png" width="200rpx" height="200rpx" mode="aspectFit" />
        <text class="text-caption my-6 text-center">报价不存在或已被删除</text>
        <wd-button class="btn-primary" @click="goBack">返回上一页</wd-button>
      </view>
    </view>
    
    <!-- 固定底部操作按钮 -->
    <view v-if="quotation" class="fixed bottom-0 left-0 right-0 bg-white/95 backdrop-blur-md border-t border-gray-100">
      <view class="app-content pb-safe">
        <view v-if="isOwner" class="flex gap-3 flex-wrap">
          <wd-button v-if="canEdit" custom-class="btn-edit flex-1 min-w-[30%]" @click="editQuotation">编辑</wd-button>
          <wd-button v-if="canPublish" custom-class="btn-publish flex-1 min-w-[30%]" @click="publishQuotationItem">发布</wd-button>
          <wd-button v-if="canToggleStatus" custom-class="btn-unpublish flex-1 min-w-[30%]" @click="toggleQuotationStatusItem">下架</wd-button>
          <wd-button v-if="canDelete" custom-class="btn-delete flex-1 min-w-[30%]" @click="deleteQuotationItem">删除</wd-button>
          <wd-button v-if="canShare" custom-class="btn-share flex-1 min-w-[30%]" @click="shareQuotation">分享</wd-button>
        </view>
        <view v-else class="flex gap-3">
          <wd-button v-if="showContactButton" custom-class="btn-contact flex-1" @click="contactPublisher">发起点价</wd-button>
          <wd-button custom-class="btn-share-friend flex-1" @click="shareQuotation">分享给朋友</wd-button>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss">
/* 全局样式 - 为 wot-design-uni 组件的 custom-class 提供样式 */
// 使用新的统一样式系统，大部分样式已通过 UnoCSS 类实现
// 只保留必要的自定义样式和动画效果

// 加载动画颜色
:deep(.loading-spinner) {
  --loading-color: var(--app-color-primary);
}

// 价格类型标签样式
.fixed-type {
  background: var(--app-color-primary-600);
}

.basis-type {
  background: var(--app-color-warning);
}

.negotiable-type {
  background: var(--app-color-info);
}

.unknown-type {
  background: var(--app-color-secondary-600);
}

// 动画延迟设置
.animate-slide-up[style="animation-delay: 0.1s"] {
  animation-delay: 0.1s;
}

.animate-slide-up[style="animation-delay: 0.2s"] {
  animation-delay: 0.2s;
}

.animate-slide-up[style="animation-delay: 0.3s"] {
  animation-delay: 0.3s;
}

// 响应式优化
@media (max-width: 750rpx) {
  .space-y-4   {
    margin-top: var(--app-spacing-base);
  }
  
  .flex-1 {
    min-width: calc(50% - 12rpx);
  }
}

// 按钮样式增强
:deep(.wd-button) {
  border-radius: var(--app-radius-lg);
  font-weight: var(--app-font-weight-medium);
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:hover {
    transform: translateY(-2rpx);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 固定底部按钮区域优化
.fixed {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-top: 1rpx solid var(--app-border-secondary);
}

// 自定义按钮颜色样式 - 使用 global.scss 变量
// 微信小程序兼容处理：避免使用 :deep()，使用全局类名

// 编辑按钮 - 蓝色
.btn-edit {
  background: linear-gradient(135deg, var(--app-color-primary-500), var(--app-color-primary-600)) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    background: linear-gradient(135deg, var(--app-color-primary-600), var(--app-color-primary-700)) !important;
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 发布按钮 - 绿色
.btn-publish {
  background: linear-gradient(135deg, var(--app-color-success), #059669) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    background: linear-gradient(135deg, #059669, #047857) !important;
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(16, 185, 129, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 下架按钮 - 橙色
.btn-unpublish {
  background: linear-gradient(135deg, var(--app-color-warning), #d97706) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    background: linear-gradient(135deg, #d97706, #b45309) !important;
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(245, 158, 11, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 删除按钮 - 红色
.btn-delete {
  background: linear-gradient(135deg, var(--app-color-error), #dc2626) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(239, 68, 68, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 分享按钮 - 紫色
.btn-share {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    background: linear-gradient(135deg, #7c3aed, #6d28d9) !important;
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 发起点价按钮 - 主色渐变
.btn-contact {
  background: linear-gradient(135deg, var(--app-color-primary-500), var(--app-color-primary-600)) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 分享给朋友按钮 - 紫粉渐变
.btn-share-friend {
  background: linear-gradient(135deg, #8b5cf6, #ec4899) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
  
  &:hover {
    transform: translateY(-2rpx) !important;
    box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.4) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

// 微信小程序特定的样式处理
/* #ifdef MP-WEIXIN */
.btn-edit,
.btn-publish,
.btn-unpublish,
.btn-delete,
.btn-share,
.btn-contact,
.btn-share-friend {
  position: relative;
  overflow: hidden;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  
  &:active::after {
    opacity: 1;
  }
}
/* #endif */
</style>
