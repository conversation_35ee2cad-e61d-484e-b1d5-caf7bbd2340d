"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../common/vendor.js");
const types_quotation = require("../../types/quotation.js");
if (!Array) {
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  _easycom_wd_tag2();
}
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
if (!Math) {
  (_easycom_wd_tag + BasisPriceDisplay)();
}
const BasisPriceDisplay = () => "./BasisPriceDisplay.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationCard"
}), {
  __name: "QuotationCard",
  props: {
    quotation: {},
    showPrice: { type: Boolean, default: false }
  },
  emits: ["click", "publisherClick"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const tradeTypeConfig = common_vendor.computed(() => {
      return props.quotation.isBuyRequest ? types_quotation.QUOTATION_TRADE_TYPE_CONFIG.buy : types_quotation.QUOTATION_TRADE_TYPE_CONFIG.sell;
    });
    const priceTypeConfig = common_vendor.computed(() => {
      return types_quotation.QUOTATION_PRICE_TYPE_CONFIG[props.quotation.priceType];
    });
    const isNegotiable = common_vendor.computed(() => {
      return props.quotation.priceType === "Negotiable";
    });
    function formatRemainingTime(quotation) {
      if (quotation.isExpired) {
        return "已过期";
      }
      if (quotation.remainingHours <= 0) {
        return "即将过期";
      } else if (quotation.remainingHours < 24) {
        return `剩余 ${quotation.remainingHours} 小时`;
      } else {
        const days = Math.floor(quotation.remainingHours / 24);
        return `剩余 ${days} 天`;
      }
    }
    function getCompanyShortName(fullName) {
      if (!fullName)
        return "未知企业";
      if (fullName.length > 8) {
        return fullName.substring(0, 8) + "...";
      }
      return fullName;
    }
    function formatLargeNumber(num) {
      const absNum = Math.abs(num);
      const sign = num < 0 ? "-" : "";
      if (absNum < 1e4) {
        return sign + absNum.toLocaleString();
      }
      if (absNum < 1e8) {
        const wan = absNum / 1e4;
        if (wan >= 100) {
          return sign + Math.round(wan) + "万";
        } else {
          return sign + wan.toFixed(1) + "万";
        }
      }
      const yi = absNum / 1e8;
      if (yi >= 100) {
        return sign + Math.round(yi) + "亿";
      } else {
        return sign + yi.toFixed(1) + "亿";
      }
    }
    function calculateDynamicFontSize(text, baseSize = 48, minSize = 28) {
      const textLength = text.length;
      if (textLength <= 6)
        return baseSize;
      if (textLength <= 8)
        return Math.max(baseSize * 0.9, minSize);
      if (textLength <= 10)
        return Math.max(baseSize * 0.8, minSize);
      return minSize;
    }
    function handleCardClick() {
      emit("click", props.quotation);
    }
    function handlePublisherClick(event) {
      if (!event)
        return;
      event.preventDefault();
      event.stopPropagation();
      event.cancelBubble = true;
      if (typeof event.stopImmediatePropagation === "function") {
        event.stopImmediatePropagation();
      }
      if (props.quotation.userID) {
        emit("publisherClick", props.quotation.userID);
      }
    }
    return (_ctx, _cache) => {
      var _a, _b, _c;
      return common_vendor.e({
        a: common_vendor.t(tradeTypeConfig.value.label),
        b: common_vendor.p({
          type: _ctx.quotation.isBuyRequest ? "success" : "primary",
          size: "small",
          ["custom-class"]: "trade-type-tag"
        }),
        c: common_vendor.t(priceTypeConfig.value.label),
        d: common_vendor.p({
          type: _ctx.quotation.priceType === "Fixed" ? "primary" : _ctx.quotation.priceType === "Basis" ? "danger" : "warning",
          size: "small",
          ["custom-class"]: "price-type-tag"
        }),
        e: common_vendor.t(_ctx.quotation.title),
        f: common_vendor.t(getCompanyShortName(((_a = _ctx.quotation.user) == null ? void 0 : _a.nickName) || "")),
        g: common_vendor.t(formatRemainingTime(_ctx.quotation)),
        h: _ctx.quotation.isExpired ? 1 : "",
        i: common_vendor.o(handlePublisherClick),
        j: _ctx.quotation.commodityName
      }, _ctx.quotation.commodityName ? {
        k: common_vendor.t(_ctx.quotation.commodityName),
        l: common_vendor.p({
          type: "primary",
          size: "small"
        })
      } : {}, {
        m: _ctx.quotation.deliveryLocation
      }, _ctx.quotation.deliveryLocation ? {
        n: common_vendor.t(_ctx.quotation.deliveryLocation),
        o: common_vendor.p({
          type: "success",
          size: "small"
        })
      } : {}, {
        p: _ctx.quotation.brand
      }, _ctx.quotation.brand ? {
        q: common_vendor.t(_ctx.quotation.brand),
        r: common_vendor.p({
          type: "warning",
          size: "small"
        })
      } : {}, {
        s: isNegotiable.value
      }, isNegotiable.value ? {} : _ctx.quotation.priceType === "Fixed" ? {
        v: common_vendor.t(formatLargeNumber(_ctx.quotation.price)),
        w: calculateDynamicFontSize(formatLargeNumber(_ctx.quotation.price)) + "rpx",
        x: _ctx.quotation.price.toLocaleString()
      } : {
        y: common_vendor.p({
          ["instrument-id"]: ((_b = _ctx.quotation.instrumentRef) == null ? void 0 : _b.instrument_id) || "",
          ["exchange-id"]: ((_c = _ctx.quotation.instrumentRef) == null ? void 0 : _c.exchange_id) || "",
          price: _ctx.quotation.price,
          ["show-price"]: _ctx.showPrice
        })
      }, {
        t: _ctx.quotation.priceType === "Fixed",
        z: _ctx.quotation.isBuyRequest ? 1 : "",
        A: tradeTypeConfig.value.bgColor,
        B: tradeTypeConfig.value.borderColor,
        C: common_vendor.o(handleCardClick)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-c7b08e0d"]]);
wx.createComponent(Component);
