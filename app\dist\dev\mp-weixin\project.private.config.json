{"libVersion": "3.8.12", "projectname": "unibest", "condition": {"miniprogram": {"list": [{"name": "我的报价", "pathName": "pages/quotes/my-list", "query": "", "scene": null, "launchMode": "default"}, {"name": "查看详情", "pathName": "pages/quotes/detail", "query": "id=1&from=public", "launchMode": "default", "scene": null}, {"name": "查看报价", "pathName": "pages/quotes/public-list", "query": "id=1", "launchMode": "default", "scene": null}, {"name": "登录界面", "pathName": "pages/login/index", "query": "", "launchMode": "default", "scene": null}]}}, "setting": {"urlCheck": false, "coverView": false, "lazyloadPlaceholderEnable": false, "skylineRenderEnable": false, "preloadBackgroundData": false, "autoAudits": false, "useApiHook": true, "useApiHostProcess": true, "showShadowRootInWxmlPanel": false, "useStaticServer": false, "useLanDebug": false, "showES6CompileOption": false, "compileHotReLoad": true, "checkInvalidKey": true, "ignoreDevUnusedFiles": true, "bigPackageSizeSupport": true}}