"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const api_quotation = require("../../api/quotation.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_cell2 = common_vendor.resolveComponent("wd-cell");
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_cell_group2 = common_vendor.resolveComponent("wd-cell-group");
  const _easycom_wd_img2 = common_vendor.resolveComponent("wd-img");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_loading2 + _easycom_wd_cell2 + _easycom_wd_icon2 + _easycom_wd_cell_group2 + _easycom_wd_img2 + _easycom_wd_button2 + _component_layout_default_uni)();
}
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_cell = () => "../../node-modules/wot-design-uni/components/wd-cell/wd-cell.js";
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_cell_group = () => "../../node-modules/wot-design-uni/components/wd-cell-group/wd-cell-group.js";
const _easycom_wd_img = () => "../../node-modules/wot-design-uni/components/wd-img/wd-img.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  (_easycom_wd_loading + _easycom_wd_cell + _easycom_wd_icon + _easycom_wd_cell_group + _easycom_wd_img + _easycom_wd_button)();
}
const _sfc_defineComponent = common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationDetail"
}), {
  __name: "detail",
  setup(__props) {
    function goBack() {
      utils_index.navigateBackOrTo("/pages/quotes/my-list");
    }
    const userStore = store_user.useUserStore();
    const quotationId = common_vendor.ref();
    const isLoading = common_vendor.ref(false);
    const quotation = common_vendor.ref();
    const isPublicAccess = common_vendor.ref(false);
    const isOwner = common_vendor.computed(() => quotation.value && userStore.userInfo && quotation.value.userID === userStore.userInfo.ID);
    const isPublicView = common_vendor.computed(() => !isOwner.value);
    const canEdit = common_vendor.computed(() => isOwner.value);
    const canPublish = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Draft";
    });
    const canToggleStatus = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    const canDelete = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Draft";
    });
    const canShare = common_vendor.computed(() => {
      var _a;
      return isOwner.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    const showContactButton = common_vendor.computed(() => {
      var _a;
      return isPublicView.value && ((_a = quotation.value) == null ? void 0 : _a.status) === "Active";
    });
    common_vendor.onLoad((options) => {
      if (options == null ? void 0 : options.id) {
        quotationId.value = parseInt(options.id);
        isPublicAccess.value = (options == null ? void 0 : options.from) === "share" || (options == null ? void 0 : options.from) === "public" || (options == null ? void 0 : options.from) === "marketplace";
        loadQuotationDetail();
      } else {
        common_vendor.index.showToast({ title: "报价ID不存在", icon: "error" });
        setTimeout(() => utils_index.navigateBackOrTo("/pages/quotes/my-list"), 1500);
      }
    });
    function loadQuotationDetail() {
      return __async(this, null, function* () {
        if (!quotationId.value)
          return;
        isLoading.value = true;
        try {
          let res;
          if (isPublicAccess.value || !userStore.isLoggedIn) {
            res = yield api_quotation.getPublicQuotationDetail(quotationId.value);
          } else {
            res = yield api_quotation.getQuotationDetail(quotationId.value);
          }
          quotation.value = res.data;
          common_vendor.index.setNavigationBarTitle({ title: quotation.value.title || "报价详情" });
        } catch (error) {
          console.error("加载报价详情失败:", error);
          common_vendor.index.showToast({ title: "加载失败", icon: "error" });
          setTimeout(() => utils_index.navigateBackOrTo("/pages/quotes/my-list"), 1500);
        } finally {
          isLoading.value = false;
        }
      });
    }
    function editQuotation() {
      if (!quotation.value)
        return;
      utils_index.navigateToPage({ url: `/pages/quotes/edit?id=${quotation.value.id}` });
    }
    function publishQuotationItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        common_vendor.index.showLoading({ title: "发布中..." });
        try {
          const expiresAt = /* @__PURE__ */ new Date();
          expiresAt.setDate(expiresAt.getDate() + 7);
          yield api_quotation.publishQuotation({ id: quotation.value.id, expiresAt: expiresAt.toISOString() });
          common_vendor.index.showToast({ title: "发布成功", icon: "success" });
          yield loadQuotationDetail();
        } catch (error) {
          console.error("发布报价失败:", error);
          common_vendor.index.showToast({ title: "发布失败", icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function toggleQuotationStatusItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        const actionText = "下架";
        const confirmText = "确定要将此报价下架吗？下架后报价将变为草稿状态。";
        const { confirm } = yield common_vendor.index.showModal({ title: `确认${actionText}`, content: confirmText });
        if (!confirm)
          return;
        common_vendor.index.showLoading({ title: `${actionText}中...` });
        try {
          yield api_quotation.toggleQuotationStatus(quotation.value.id);
          common_vendor.index.showToast({ title: `${actionText}成功`, icon: "success" });
          yield loadQuotationDetail();
        } catch (error) {
          console.error("下架失败:", error);
          common_vendor.index.showToast({ title: `${actionText}失败`, icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function deleteQuotationItem() {
      return __async(this, null, function* () {
        if (!quotation.value)
          return;
        const { confirm } = yield common_vendor.index.showModal({ title: "确认删除", content: "确定要删除这个报价草稿吗？" });
        if (!confirm)
          return;
        common_vendor.index.showLoading({ title: "删除中..." });
        try {
          yield api_quotation.deleteQuotation(quotation.value.id);
          common_vendor.index.showToast({ title: "删除成功", icon: "success" });
          setTimeout(() => utils_index.navigateBackOrTo("/pages/quotes/my-list"), 1500);
        } catch (error) {
          console.error("删除报价失败:", error);
          common_vendor.index.showToast({ title: "删除失败", icon: "error" });
        } finally {
          common_vendor.index.hideLoading();
        }
      });
    }
    function contactPublisher() {
      if (!quotation.value)
        return;
      common_vendor.index.showToast({ title: "功能开发中", icon: "none" });
    }
    function callPhone(phoneNumber) {
      if (!phoneNumber)
        return;
      common_vendor.index.makePhoneCall({ phoneNumber });
    }
    function formatPrice(q) {
      if (q.priceType === "Fixed")
        return `¥ ${q.price.toFixed(2)}`;
      if (q.priceType === "Basis" && q.instrumentRef) {
        return `${q.instrumentRef.instrument_id} ${q.price >= 0 ? "+ " : ""}${q.price}`;
      }
      if (q.priceType === "Negotiable")
        return "价格面议";
      return q.price.toString();
    }
    function getPriceTypeLabel(priceType) {
      switch (priceType) {
        case "Fixed":
          return "一口价";
        case "Basis":
          return "基差报价";
        case "Negotiable":
          return "商议";
        default:
          return "未知类型";
      }
    }
    function getPriceTypeClass(priceType) {
      switch (priceType) {
        case "Fixed":
          return "fixed-type";
        case "Basis":
          return "basis-type";
        case "Negotiable":
          return "negotiable-type";
        default:
          return "unknown-type";
      }
    }
    function formatDateTime(dateTime) {
      const d = new Date(dateTime);
      return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(d.getDate()).padStart(2, "0")} ${String(d.getHours()).padStart(2, "0")}:${String(d.getMinutes()).padStart(2, "0")}`;
    }
    function formatRemainingTime() {
      if (!quotation.value || quotation.value.status !== "Active")
        return "";
      if (quotation.value.isExpired)
        return "已过期";
      const { remainingHours } = quotation.value;
      if (remainingHours <= 0)
        return "即将过期";
      if (remainingHours < 24)
        return `剩余 ${remainingHours} 小时`;
      return `剩余 ${Math.floor(remainingHours / 24)} 天`;
    }
    common_vendor.onShareAppMessage(() => {
      var _a;
      if (!quotation.value)
        return {};
      const title = quotation.value.title;
      const path = `/pages/quotes/detail?id=${quotation.value.id}&from=share`;
      return {
        title,
        path,
        imageUrl: ((_a = quotation.value.user) == null ? void 0 : _a.headerImg) || "static/logo.svg"
      };
    });
    common_vendor.onShareTimeline(() => {
      var _a;
      if (!quotation.value)
        return {};
      const title = `${quotation.value.title} - ${quotation.value.commodityName}`;
      const query = `share=timeline&id=${quotation.value.id}&from=share`;
      return {
        title,
        query,
        imageUrl: ((_a = quotation.value.user) == null ? void 0 : _a.headerImg) || "static/logo.svg"
      };
    });
    function shareQuotation() {
      if (!quotation.value)
        return;
      common_vendor.index.showToast({
        title: "请点击右上角分享",
        icon: "none",
        duration: 2e3
      });
    }
    return (_ctx, _cache) => {
      var _a, _b, _c, _d;
      return common_vendor.e({
        a: isLoading.value
      }, isLoading.value ? {
        b: common_vendor.p({
          ["custom-class"]: "loading-spinner"
        })
      } : quotation.value ? common_vendor.e({
        d: isPublicView.value && quotation.value.user
      }, isPublicView.value && quotation.value.user ? common_vendor.e({
        e: (_a = quotation.value.user) == null ? void 0 : _a.companyName
      }, ((_b = quotation.value.user) == null ? void 0 : _b.companyName) ? {
        f: common_vendor.p({
          title: "发布企业",
          value: quotation.value.user.companyName
        })
      } : {}, {
        g: (_c = quotation.value.user) == null ? void 0 : _c.nickName
      }, ((_d = quotation.value.user) == null ? void 0 : _d.nickName) ? {
        h: common_vendor.p({
          title: "联系人",
          value: quotation.value.user.nickName
        })
      } : {}, {
        i: common_vendor.t(quotation.value.user.phone),
        j: common_vendor.p({
          name: "phone"
        }),
        k: common_vendor.o(($event) => callPhone(quotation.value.user.phone)),
        l: common_vendor.p({
          title: "联系电话"
        }),
        m: common_vendor.p({
          border: true
        })
      }) : {}, {
        n: common_vendor.t(quotation.value.isBuyRequest ? "求购" : "出售"),
        o: common_vendor.n(quotation.value.isBuyRequest ? "bg-success" : "bg-primary-500"),
        p: common_vendor.t(getPriceTypeLabel(quotation.value.priceType)),
        q: common_vendor.n(getPriceTypeClass(quotation.value.priceType)),
        r: common_vendor.t(quotation.value.title),
        s: common_vendor.t(formatPrice(quotation.value)),
        t: common_vendor.t(quotation.value.commodityName || "-"),
        v: quotation.value.brand
      }, quotation.value.brand ? {
        w: common_vendor.t(quotation.value.brand)
      } : {}, {
        x: common_vendor.t(quotation.value.deliveryLocation),
        y: quotation.value.priceType === "Basis" && quotation.value.instrumentRef
      }, quotation.value.priceType === "Basis" && quotation.value.instrumentRef ? {
        z: common_vendor.t(quotation.value.instrumentRef.instrument_name)
      } : {}, {
        A: common_vendor.p({
          title: "发布时间",
          value: formatDateTime(quotation.value.createdAt)
        }),
        B: common_vendor.p({
          title: "过期时间",
          value: formatDateTime(quotation.value.expiresAt)
        }),
        C: quotation.value.status === "Active"
      }, quotation.value.status === "Active" ? {
        D: common_vendor.t(formatRemainingTime()),
        E: common_vendor.n(quotation.value.isExpired ? "text-error" : "text-success"),
        F: common_vendor.p({
          title: "剩余有效期"
        })
      } : {}, {
        G: quotation.value.specifications || quotation.value.description
      }, quotation.value.specifications || quotation.value.description ? common_vendor.e({
        H: quotation.value.specifications
      }, quotation.value.specifications ? {
        I: common_vendor.t(quotation.value.specifications),
        J: common_vendor.p({
          title: "规格说明"
        })
      } : {}, {
        K: quotation.value.description
      }, quotation.value.description ? {
        L: common_vendor.t(quotation.value.description),
        M: common_vendor.p({
          title: "补充说明"
        })
      } : {}) : {}) : {
        N: common_vendor.p({
          src: "/static/images/error-404.png",
          width: "200rpx",
          height: "200rpx",
          mode: "aspectFit"
        }),
        O: common_vendor.o(goBack)
      }, {
        c: quotation.value,
        P: quotation.value
      }, quotation.value ? common_vendor.e({
        Q: isOwner.value
      }, isOwner.value ? common_vendor.e({
        R: canEdit.value
      }, canEdit.value ? {
        S: common_vendor.o(editQuotation),
        T: common_vendor.p({
          ["custom-class"]: "btn-edit flex-1 min-w-_a_30_a__a_"
        })
      } : {}, {
        U: canPublish.value
      }, canPublish.value ? {
        V: common_vendor.o(publishQuotationItem),
        W: common_vendor.p({
          ["custom-class"]: "btn-publish flex-1 min-w-_a_30_a__a_"
        })
      } : {}, {
        X: canToggleStatus.value
      }, canToggleStatus.value ? {
        Y: common_vendor.o(toggleQuotationStatusItem),
        Z: common_vendor.p({
          ["custom-class"]: "btn-unpublish flex-1 min-w-_a_30_a__a_"
        })
      } : {}, {
        aa: canDelete.value
      }, canDelete.value ? {
        ab: common_vendor.o(deleteQuotationItem),
        ac: common_vendor.p({
          ["custom-class"]: "btn-delete flex-1 min-w-_a_30_a__a_"
        })
      } : {}, {
        ad: canShare.value
      }, canShare.value ? {
        ae: common_vendor.o(shareQuotation),
        af: common_vendor.p({
          ["custom-class"]: "btn-share flex-1 min-w-_a_30_a__a_"
        })
      } : {}) : common_vendor.e({
        ag: showContactButton.value
      }, showContactButton.value ? {
        ah: common_vendor.o(contactPublisher),
        ai: common_vendor.p({
          ["custom-class"]: "btn-contact flex-1"
        })
      } : {}, {
        aj: common_vendor.o(shareQuotation),
        ak: common_vendor.p({
          ["custom-class"]: "btn-share-friend flex-1"
        })
      })) : {});
    };
  }
}));
_sfc_defineComponent.__runtimeHooks = 6;
wx.createPage(_sfc_defineComponent);
