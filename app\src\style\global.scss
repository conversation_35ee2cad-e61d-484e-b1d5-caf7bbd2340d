/**
 * 全局样式文件 - 统一的设计系统
 * 提供 CSS 变量、全局样式、标准化组件样式
 */

// ==============================================================================
// CSS 变量系统 - Design Tokens
// ==============================================================================

:root,
page {
  // 主色系 - Primary Colors
  --app-color-primary-50: #eff6ff;
  --app-color-primary-100: #dbeafe;
  --app-color-primary-200: #bfdbfe;
  --app-color-primary-300: #93c5fd;
  --app-color-primary-400: #60a5fa;
  --app-color-primary-500: #3b82f6;
  --app-color-primary-600: #2563eb;
  --app-color-primary-700: #1d4ed8;
  --app-color-primary-800: #1e40af;
  --app-color-primary-900: #1e3a8a;
  --app-color-primary: #3b82f6;
  --app-color-primary-dark: #2563eb;

  // 辅助色系 - Secondary Colors  
  --app-color-secondary-50: #f8fafc;
  --app-color-secondary-100: #f1f5f9;
  --app-color-secondary-200: #e2e8f0;
  --app-color-secondary-300: #cbd5e1;
  --app-color-secondary-400: #94a3b8;
  --app-color-secondary-500: #64748b;
  --app-color-secondary-600: #475569;
  --app-color-secondary-700: #334155;
  --app-color-secondary-800: #1e293b;
  --app-color-secondary-900: #0f172a;
  --app-color-secondary: #64748b;

  // 功能色系 - Functional Colors
  --app-color-success: #10b981;
  --app-color-success-light: #d1fae5;
  --app-color-warning: #f59e0b;
  --app-color-warning-light: #fef3c7;
  --app-color-error: #ef4444;
  --app-color-error-light: #fee2e2;
  --app-color-info: #3b82f6;
  --app-color-info-light: #dbeafe;

  // 中性色系 - Neutral Colors
  --app-color-gray-50: #f9fafb;
  --app-color-gray-100: #f3f4f6;
  --app-color-gray-200: #e5e7eb;
  --app-color-gray-300: #d1d5db;
  --app-color-gray-400: #9ca3af;
  --app-color-gray-500: #6b7280;
  --app-color-gray-600: #374151;
  --app-color-gray-700: #1f2937;
  --app-color-gray-800: #111827;
  --app-color-gray-900: #030712;

  // 文本颜色 - Text Colors
  --app-text-primary: var(--app-color-gray-900);
  --app-text-secondary: var(--app-color-gray-600);
  --app-text-tertiary: var(--app-color-gray-500);
  --app-text-placeholder: var(--app-color-gray-400);
  --app-text-disabled: var(--app-color-gray-300);
  --app-text-inverse: #ffffff;

  // 背景颜色 - Background Colors
  --app-bg-primary: #ffffff;
  --app-bg-secondary: var(--app-color-gray-50);
  --app-bg-tertiary: var(--app-color-gray-100);
  --app-bg-overlay: rgba(0, 0, 0, 0.6);
  --app-bg-page: linear-gradient(135deg, var(--app-color-gray-50) 0%, #e0f2fe 100%);

  // 边框颜色 - Border Colors
  --app-border-primary: var(--app-color-gray-200);
  --app-border-secondary: var(--app-color-gray-100);
  --app-border-focus: var(--app-color-primary);

  // 阴影 - Shadows
  --app-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --app-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --app-shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  --app-shadow-lg: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);
  --app-shadow-xl: 0 32rpx 128rpx rgba(0, 0, 0, 0.2);

  // 圆角 - Border Radius
  --app-radius-sm: 8rpx;
  --app-radius-base: 12rpx;
  --app-radius-md: 16rpx;
  --app-radius-lg: 20rpx;
  --app-radius-xl: 24rpx;
  --app-radius-2xl: 32rpx;
  --app-radius-full: 9999rpx;

  // 间距 - Spacing
  --app-spacing-xs: 8rpx;
  --app-spacing-sm: 16rpx;
  --app-spacing-base: 24rpx;
  --app-spacing-md: 32rpx;
  --app-spacing-lg: 48rpx;
  --app-spacing-xl: 64rpx;

  // 字体大小 - Font Sizes
  --app-font-size-xs: 24rpx;
  --app-font-size-sm: 28rpx;
  --app-font-size-base: 32rpx;
  --app-font-size-lg: 36rpx;
  --app-font-size-xl: 40rpx;
  --app-font-size-2xl: 48rpx;
  --app-font-size-3xl: 60rpx;

  // 行高 - Line Heights
  --app-line-height-tight: 1.25;
  --app-line-height-normal: 1.5;
  --app-line-height-relaxed: 1.75;

  // 字重 - Font Weights
  --app-font-weight-normal: 400;
  --app-font-weight-medium: 500;
  --app-font-weight-semibold: 600;
  --app-font-weight-bold: 700;

  // z-index 层级 - Z-Index Scale
  --app-z-dropdown: 1000;
  --app-z-sticky: 1020;
  --app-z-fixed: 1030;
  --app-z-modal-backdrop: 1040;
  --app-z-modal: 1050;
  --app-z-popover: 1060;
  --app-z-tooltip: 1070;

  // 动画时长 - Animation Duration
  --app-duration-fast: 0.15s;
  --app-duration-base: 0.3s;
  --app-duration-slow: 0.5s;

  // 动画缓动 - Animation Easing
  --app-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --app-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --app-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);

  // wot-design-uni 主题变量覆盖
  --wot-color-theme: var(--app-color-primary);
  --wot-color-success: var(--app-color-success);
  --wot-color-warning: var(--app-color-warning);
  --wot-color-danger: var(--app-color-error);
  --wot-color-info: var(--app-color-info);
}

// ==============================================================================
// 全局基础样式重置
// ==============================================================================

/* 微信小程序兼容的样式重置 */
/* #ifdef MP-WEIXIN */
view, text, button, input, textarea, image, scroll-view, swiper, swiper-item,
navigator, audio, video, canvas, map, open-data, web-view, ad, official-account,
rich-text, picker, picker-view, picker-view-column, slider, switch, camera,
live-player, live-pusher, cover-view, cover-image, match-media, page-meta {
  box-sizing: border-box;
}
/* #endif */

/* #ifndef MP-WEIXIN */
*, *::before, *::after {
  box-sizing: border-box;
}
/* #endif */

page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  font-size: var(--app-font-size-base);
  line-height: var(--app-line-height-normal);
  color: var(--app-text-primary);
  background: var(--app-bg-page);
  
  /* 平台兼容的字体平滑 */
  /* #ifndef MP-WEIXIN */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* #endif */
}

// ==============================================================================
// 统一的页面布局样式
// ==============================================================================

// 主要的页面背景渐变（向后兼容）
.gradient-bg-primary {
  background: var(--app-bg-page);
}

// 页面容器样式（向后兼容，但推荐使用新的 app-container）
.page-container {
  min-height: 100vh;
  background: var(--app-bg-page);
  padding: var(--app-spacing-xs);

  // 新的页面容器样式
  &.modern {
    padding: var(--app-spacing-xs); // 所有平台都使用小屏样式
  }
}

// 新的标准页面容器
.app-container {
  min-height: 100vh;
  background: var(--app-bg-page);
  
  /* 微信小程序样式 - 保持简洁 */
  /* #ifdef MP-WEIXIN */
  padding: env(safe-area-inset-top) var(--app-spacing-xs) env(safe-area-inset-bottom);
  /* #endif */
  
  /* 非微信小程序样式 - 添加外框效果 */
  /* #ifndef MP-WEIXIN */
  padding: var(--app-spacing-base);
  max-width: 1200rpx;
  margin: 0 auto;
  
  // 添加外框样式，让页面看起来更像独立应用
  border-radius: var(--app-radius-lg);
  box-shadow: var(--app-shadow-lg);
  border: 1rpx solid var(--app-border-secondary);
  background: var(--app-bg-primary);
  
  // 确保在小屏设备上有合适的外边距
  @media (max-width: 750rpx) {
    margin: var(--app-spacing-sm);
    border-radius: var(--app-radius-md);
  }
  /* #endif */
}

.app-content {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  
  // 添加内边距，确保内容不会贴着边缘
  padding: var(--app-spacing-base);
  
  /* 微信小程序中减少内边距 */
  /* #ifdef MP-WEIXIN */
  padding: var(--app-spacing-sm);
  /* #endif */
}

// ==============================================================================
// 统一的卡片组件样式
// ==============================================================================

// 通用卡片样式（向后兼容）
.common-card {
  margin: 0 var(--app-spacing-sm) var(--app-spacing-sm);
  background: var(--app-bg-primary);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  border: 1rpx solid var(--app-border-secondary);
  overflow: hidden;

  /* 条件编译处理毛玻璃效果 */
  /* #ifndef MP-WEIXIN */
  backdrop-filter: blur(20rpx);
  /* #endif */

  /* #ifdef MP-WEIXIN */
  background: var(--app-bg-primary); /* 小程序中使用纯色背景 */
  /* #endif */

  &:last-child {
    margin-bottom: 0;
  }
}

// 新的标准卡片样式
.app-card {
  background: var(--app-bg-primary);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  border: 1rpx solid var(--app-border-secondary);
  overflow: hidden;
  transition: all var(--app-duration-base) var(--app-ease-out);

  &:hover {
    box-shadow: var(--app-shadow-md);
    transform: translateY(-2rpx);
  }

  // 卡片头部
  &-header {
    padding: var(--app-spacing-base);
    border-bottom: 1rpx solid var(--app-border-secondary);
    background: var(--app-bg-secondary);
  }

  // 卡片内容
  &-body {
    padding: var(--app-spacing-base);
  }

  // 卡片底部
  &-footer {
    padding: var(--app-spacing-base);
    border-top: 1rpx solid var(--app-border-secondary);
    background: var(--app-bg-secondary);
  }
}

// 毛玻璃卡片
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  
  /* #ifndef MP-WEIXIN */
  backdrop-filter: blur(20rpx);
  /* #endif */
  
  /* #ifdef MP-WEIXIN */
  background: rgba(255, 255, 255, 0.95);
  /* #endif */
}

// ==============================================================================
// 统一的表单组件样式
// ==============================================================================

.app-form {
  &-item {
    margin-bottom: var(--app-spacing-base);

    &:last-child {
      margin-bottom: 0;
    }
  }

  &-label {
    display: block;
    font-size: var(--app-font-size-sm);
    font-weight: var(--app-font-weight-medium);
    color: var(--app-text-primary);
    margin-bottom: var(--app-spacing-xs);
    line-height: var(--app-line-height-tight);
  }

  &-control {
    position: relative;
  }

  &-input {
    width: 100%;
    padding: var(--app-spacing-base) var(--app-spacing-sm);
    font-size: var(--app-font-size-base);
    color: var(--app-text-primary);
    background: var(--app-bg-primary);
    border: 2rpx solid var(--app-border-primary);
    border-radius: var(--app-radius-base);
    transition: all var(--app-duration-base) var(--app-ease-out);

    &::placeholder {
      color: var(--app-text-placeholder);
    }

    &:focus {
      border-color: var(--app-border-focus);
      box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
      outline: none;
    }

    &.error {
      border-color: var(--app-color-error);
      
      &:focus {
        box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.1);
      }
    }

    &:disabled {
      background: var(--app-bg-tertiary);
      color: var(--app-text-disabled);
      
      /* #ifndef MP-WEIXIN */
      cursor: not-allowed;
      /* #endif */
    }
  }

  &-error {
    margin-top: var(--app-spacing-xs);
    font-size: var(--app-font-size-xs);
    color: var(--app-color-error);
    line-height: var(--app-line-height-tight);
  }

  &-help {
    margin-top: var(--app-spacing-xs);
    font-size: var(--app-font-size-xs);
    color: var(--app-text-tertiary);
    line-height: var(--app-line-height-tight);
  }
}

// ==============================================================================
// 统一的按钮样式
// ==============================================================================

.app-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-sm) var(--app-spacing-base);
  font-size: var(--app-font-size-base);
  font-weight: var(--app-font-weight-medium);
  line-height: 1;
  border: 2rpx solid transparent;
  border-radius: var(--app-radius-base);
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  /* #ifndef MP-WEIXIN */
  cursor: pointer;
  /* #endif */
  white-space: nowrap;
  
  /* #ifndef MP-WEIXIN */
  user-select: none;
  /* #endif */

  // 按钮尺寸
  &-small {
    padding: var(--app-spacing-xs) var(--app-spacing-sm);
    font-size: var(--app-font-size-sm);
  }

  &-large {
    padding: var(--app-spacing-base) var(--app-spacing-lg);
    font-size: var(--app-font-size-lg);
  }

  // 按钮类型
  &-primary {
    background: linear-gradient(135deg, var(--app-color-primary), var(--app-color-primary-dark));
    color: var(--app-text-inverse);

    &:hover:not(:disabled) {
      box-shadow: var(--app-shadow-md);
      transform: translateY(-2rpx);
    }

    &:active:not(:disabled) {
      transform: translateY(0);
    }
  }

  &-secondary {
    background: var(--app-bg-secondary);
    color: var(--app-text-primary);
    border-color: var(--app-border-primary);

    &:hover:not(:disabled) {
      background: var(--app-bg-tertiary);
      border-color: var(--app-color-primary);
    }
  }

  &-ghost {
    background: transparent;
    color: var(--app-color-primary);

    &:hover:not(:disabled) {
      background: var(--app-color-primary-50);
    }
  }

  &-text {
    background: transparent;
    color: var(--app-color-primary);
    padding: var(--app-spacing-xs);

    &:hover:not(:disabled) {
      background: var(--app-color-primary-50);
    }
  }

  // 按钮状态
  &:disabled {
    opacity: 0.5;
    transform: none !important;
    
    /* #ifndef MP-WEIXIN */
    cursor: not-allowed;
    /* #endif */
  }

  &-block {
    width: 100%;
  }
}

// ==============================================================================
// 统一的文本样式
// ==============================================================================

.app-text {
  &-title {
    font-size: var(--app-font-size-xl);
    font-weight: var(--app-font-weight-semibold);
    color: var(--app-text-primary);
    line-height: var(--app-line-height-tight);
  }

  &-subtitle {
    font-size: var(--app-font-size-lg);
    font-weight: var(--app-font-weight-medium);
    color: var(--app-text-primary);
    line-height: var(--app-line-height-normal);
  }

  &-body {
    font-size: var(--app-font-size-base);
    color: var(--app-text-secondary);
    line-height: var(--app-line-height-normal);
  }

  &-caption {
    font-size: var(--app-font-size-sm);
    color: var(--app-text-tertiary);
    line-height: var(--app-line-height-normal);
  }

  &-link {
    color: var(--app-color-primary);
    text-decoration: none;
    transition: color var(--app-duration-base) var(--app-ease-out);

    &:hover {
      color: var(--app-color-primary-700);
    }

    &:active {
      color: var(--app-color-primary-800);
    }
  }
}

// ==============================================================================
// 工具类样式
// ==============================================================================

// 间距工具类
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--app-spacing-xs) !important; }
.m-2 { margin: var(--app-spacing-sm) !important; }
.m-3 { margin: var(--app-spacing-base) !important; }
.m-4 { margin: var(--app-spacing-md) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--app-spacing-xs) !important; }
.p-2 { padding: var(--app-spacing-sm) !important; }
.p-3 { padding: var(--app-spacing-base) !important; }
.p-4 { padding: var(--app-spacing-md) !important; }

// 显示/隐藏工具类
.hidden { display: none !important; }
.visible { display: block !important; }

// 文本对齐工具类
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

// 圆角工具类
.rounded-none { border-radius: 0 !important; }
.rounded-sm { border-radius: var(--app-radius-sm) !important; }
.rounded { border-radius: var(--app-radius-base) !important; }
.rounded-lg { border-radius: var(--app-radius-lg) !important; }
.rounded-full { border-radius: var(--app-radius-full) !important; }

// ==============================================================================
// 动画样式
// ==============================================================================

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10rpx);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

// 动画工具类
.animate-fade-in { animation: fadeIn var(--app-duration-base) var(--app-ease-out); }
.animate-fade-out { animation: fadeOut var(--app-duration-base) var(--app-ease-out); }
.animate-slide-up { animation: slideUp var(--app-duration-base) var(--app-ease-out); }
.animate-slide-down { animation: slideDown var(--app-duration-base) var(--app-ease-out); }
.animate-scale { animation: scale var(--app-duration-base) var(--app-ease-out); }