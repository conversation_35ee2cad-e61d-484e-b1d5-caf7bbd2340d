/**
 * 商品相关类型定义
 */

// 商品基础信息
export interface ICommodity {
  id: number
  name: string
  product_id: string
  exchange_id: string
  section: string
  created_at: string
  updated_at: string
  instrument_count?: number
}

// 商品请求参数
export interface ICommodityRequest {
  id?: number
  name: string
  product_id: string
  exchange_id: string
  section: string
}

// 商品列表查询参数
export interface ICommodityListRequest {
  page?: number
  pageSize?: number
  name?: string
  product_id?: string
  exchange_id?: string
  section?: string
}

// 商品列表响应
export interface ICommodityListResponse {
  list: ICommodity[]
  total: number
  page: number
  pageSize: number
}

// 按版块分组的商品列表（用于品种选择器）
export interface ICommoditySectionMap {
  [section: string]: ICommodity[]
}

// 用户关注的商品类型
export interface IUserFavoriteCommodities {
  favoriteCommodities: string[] // ProductID列表
}