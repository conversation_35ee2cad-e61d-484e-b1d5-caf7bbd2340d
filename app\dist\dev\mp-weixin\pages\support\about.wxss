/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.about-container.data-v-8a2c2bc0 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}
.app-info-card.data-v-8a2c2bc0 {
  padding: 60rpx 40rpx;
  text-align: center;
}
.logo-section.data-v-8a2c2bc0 {
  margin-bottom: 40rpx;
}
.logo-section .app-logo.data-v-8a2c2bc0 {
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.logo-section .app-name.data-v-8a2c2bc0 {
  display: block;
  font-size: 40rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}
.logo-section .app-slogan.data-v-8a2c2bc0 {
  display: block;
  font-size: 26rpx;
  color: #909399;
}
.version-info.data-v-8a2c2bc0 {
  display: flex;
  justify-content: space-around;
  padding-top: 32rpx;
  border-top: 1rpx solid #f0f0f0;
}
.version-item.data-v-8a2c2bc0 {
  text-align: center;
}
.version-item .version-label.data-v-8a2c2bc0 {
  display: block;
  font-size: 24rpx;
  color: #909399;
  margin-bottom: 8rpx;
}
.version-item .version-value.data-v-8a2c2bc0 {
  display: block;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}
.company-info.data-v-8a2c2bc0 {
  overflow: hidden;
}
.section-title.data-v-8a2c2bc0 {
  padding: 32rpx 40rpx 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  border-bottom: 1rpx solid #f0f0f0;
}
.info-item.data-v-8a2c2bc0 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
}
.info-item.data-v-8a2c2bc0:last-child {
  border-bottom: none;
}
.item-left.data-v-8a2c2bc0 {
  display: flex;
  align-items: center;
}
.item-left .item-label.data-v-8a2c2bc0 {
  margin-left: 24rpx;
  font-size: 30rpx;
  color: #333;
}
.item-value.data-v-8a2c2bc0 {
  font-size: 28rpx;
  color: #606266;
}
.item-value.link-text.data-v-8a2c2bc0 {
  color: #667eea;
  text-decoration: underline;
}
.qrcode-section.data-v-8a2c2bc0 {
  padding: 40rpx;
  text-align: center;
  border-top: 1rpx solid #f8f8f8;
}
.qrcode-section .qrcode-title.data-v-8a2c2bc0 {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}
.qrcode-section .qrcode-container.data-v-8a2c2bc0 {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.qrcode-section .qrcode-container .qrcode-image.data-v-8a2c2bc0 {
  width: 200rpx;
  height: 200rpx;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}
.qrcode-section .qrcode-container .qrcode-hint.data-v-8a2c2bc0 {
  font-size: 24rpx;
  color: #909399;
  background: #f5f7fa;
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
}
.copyright.data-v-8a2c2bc0 {
  text-align: center;
  padding: 40rpx 0;
}
.copyright .copyright-text.data-v-8a2c2bc0 {
  display: block;
  font-size: 24rpx;
  color: #c0c4cc;
  line-height: 1.6;
}