"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const store_commodity = require("../store/commodity.js");
const store_user = require("../store/user.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_checkbox2 = common_vendor.resolveComponent("wd-checkbox");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  (_easycom_wd_icon2 + _easycom_wd_button2 + _easycom_wd_input2 + _easycom_wd_checkbox2 + _easycom_wd_loading2 + _easycom_wd_popup2)();
}
const _easycom_wd_icon = () => "../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_input = () => "../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_checkbox = () => "../node-modules/wot-design-uni/components/wd-checkbox/wd-checkbox.js";
const _easycom_wd_loading = () => "../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_popup = () => "../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_button + _easycom_wd_input + _easycom_wd_checkbox + _easycom_wd_loading + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "CommoditySectionSelector"
}), {
  __name: "CommoditySectionSelector",
  props: {
    visible: { type: Boolean }
  },
  emits: ["update:visible", "confirm", "cancel"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const commodityStore = store_commodity.useCommodityStore();
    const userStore = store_user.useUserStore();
    const searchKeyword = common_vendor.ref("");
    const selectedCommodities = common_vendor.ref([]);
    const originalSelected = common_vendor.ref([]);
    const expandedSection = common_vendor.ref(null);
    const internalVisible = common_vendor.computed({
      get: () => props.visible,
      set: (val) => emit("update:visible", val)
    });
    const isLoading = common_vendor.computed(() => commodityStore.isLoading || userStore.favoritesLoading);
    const isSaving = common_vendor.computed(() => userStore.favoritesLoading);
    const filteredSectionMap = common_vendor.computed(() => {
      if (!searchKeyword.value.trim()) {
        return commodityStore.commoditySectionMap;
      }
      const keyword = searchKeyword.value.trim().toLowerCase();
      const filtered = {};
      Object.entries(commodityStore.commoditySectionMap).forEach(([section, commodities]) => {
        const matchedCommodities = commodities.filter(
          (commodity) => commodity.name.toLowerCase().includes(keyword) || commodity.product_id.toLowerCase().includes(keyword)
        );
        if (matchedCommodities.length > 0) {
          filtered[section] = matchedCommodities;
        }
      });
      return filtered;
    });
    const hasSelected = common_vendor.computed(() => selectedCommodities.value.length > 0);
    common_vendor.computed(() => {
      if (selectedCommodities.value.length !== originalSelected.value.length) {
        return true;
      }
      return !selectedCommodities.value.every((id) => originalSelected.value.includes(id));
    });
    function loadCommodityData() {
      return __async(this, null, function* () {
        try {
          yield Promise.all([
            commodityStore.loadCommodityList(),
            userStore.loadFavoriteCommodities()
          ]);
          selectedCommodities.value = [...userStore.favoriteCommodities];
          originalSelected.value = [...userStore.favoriteCommodities];
        } catch (error) {
          console.error("加载数据失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        }
      });
    }
    function toggleCommodity(commodityName) {
      const index = selectedCommodities.value.indexOf(commodityName);
      if (index > -1) {
        selectedCommodities.value.splice(index, 1);
      } else {
        selectedCommodities.value.push(commodityName);
      }
    }
    function isCommoditySelected(commodityName) {
      return selectedCommodities.value.includes(commodityName);
    }
    function toggleSectionCollapse(section) {
      if (expandedSection.value === section) {
        expandedSection.value = null;
      } else {
        expandedSection.value = section;
      }
    }
    function isSectionCollapsed(section) {
      return expandedSection.value !== section;
    }
    function toggleSection(section) {
      const commodities = commodityStore.commoditySectionMap[section] || [];
      const sectionCommodityNames = commodities.map((c) => c.name);
      const allSelected = sectionCommodityNames.every((name) => isCommoditySelected(name));
      if (allSelected) {
        selectedCommodities.value = selectedCommodities.value.filter(
          (name) => !sectionCommodityNames.includes(name)
        );
      } else {
        sectionCommodityNames.forEach((name) => {
          if (!isCommoditySelected(name)) {
            selectedCommodities.value.push(name);
          }
        });
      }
    }
    function isSectionFullySelected(section) {
      const commodities = commodityStore.commoditySectionMap[section] || [];
      const sectionCommodityNames = commodities.map((c) => c.name);
      return sectionCommodityNames.length > 0 && sectionCommodityNames.every((name) => isCommoditySelected(name));
    }
    function isSectionPartiallySelected(section) {
      const commodities = commodityStore.commoditySectionMap[section] || [];
      const sectionCommodityNames = commodities.map((c) => c.name);
      const selectedCount = sectionCommodityNames.filter((name) => isCommoditySelected(name)).length;
      return selectedCount > 0 && selectedCount < sectionCommodityNames.length;
    }
    function getSectionSelectedCount(section) {
      const commodities = commodityStore.commoditySectionMap[section] || [];
      const sectionCommodityNames = commodities.map((c) => c.name);
      return sectionCommodityNames.filter((name) => isCommoditySelected(name)).length;
    }
    function handleConfirm() {
      return __async(this, null, function* () {
        if (selectedCommodities.value.length === 0) {
          common_vendor.index.showToast({
            title: "请至少选择一个品种",
            icon: "none"
          });
          return;
        }
        try {
          yield userStore.updateFavoriteCommodities(selectedCommodities.value);
          emit("confirm", selectedCommodities.value);
          internalVisible.value = false;
        } catch (error) {
          console.error("保存失败:", error);
        }
      });
    }
    function handleCancel() {
      selectedCommodities.value = [...originalSelected.value];
      emit("cancel");
      internalVisible.value = false;
    }
    function clearSearch() {
      searchKeyword.value = "";
    }
    function validateAndCleanFavoriteCommodities() {
      return __async(this, null, function* () {
        try {
          const allCommodityNames = /* @__PURE__ */ new Set();
          Object.values(commodityStore.commoditySectionMap).forEach((commodities) => {
            commodities.forEach((commodity) => {
              allCommodityNames.add(commodity.name);
            });
          });
          const currentFavorites = userStore.favoriteCommodities;
          const validFavorites = currentFavorites.filter(
            (commodityName) => allCommodityNames.has(commodityName)
          );
          if (validFavorites.length !== currentFavorites.length) {
            console.log(`清理无效关注品种: ${currentFavorites.length} -> ${validFavorites.length}`);
            yield userStore.updateFavoriteCommodities(validFavorites);
          }
        } catch (error) {
          console.error("验证关注品种失败:", error);
        }
      });
    }
    common_vendor.onMounted(() => __async(this, null, function* () {
      if (props.visible) {
        yield loadCommodityData();
        yield validateAndCleanFavoriteCommodities();
        expandedSection.value = null;
      }
    }));
    common_vendor.watch(() => props.visible, (newVisible) => __async(this, null, function* () {
      if (newVisible) {
        yield loadCommodityData();
        yield validateAndCleanFavoriteCommodities();
        expandedSection.value = null;
      }
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.p({
          name: "close",
          size: "32rpx"
        }),
        b: common_vendor.o(handleCancel),
        c: common_vendor.p({
          type: "text",
          size: "small",
          ["custom-class"]: "close-button"
        }),
        d: common_vendor.p({
          name: "search",
          size: "28rpx",
          ["custom-class"]: "search-icon"
        }),
        e: common_vendor.o(clearSearch),
        f: common_vendor.o(($event) => searchKeyword.value = $event),
        g: common_vendor.p({
          placeholder: "搜索品种名称或代码...",
          clearable: true,
          ["custom-class"]: "search-input",
          modelValue: searchKeyword.value
        }),
        h: hasSelected.value
      }, hasSelected.value ? {
        i: common_vendor.t(selectedCommodities.value.length)
      } : {}, {
        j: !isLoading.value
      }, !isLoading.value ? common_vendor.e({
        k: Object.keys(filteredSectionMap.value).length === 0
      }, Object.keys(filteredSectionMap.value).length === 0 ? {
        l: common_vendor.t(searchKeyword.value ? "没有找到匹配的品种" : "暂无可选品种")
      } : {
        m: common_vendor.f(filteredSectionMap.value, (commodities, section, i0) => {
          return common_vendor.e({
            a: "14aff35e-5-" + i0 + ",14aff35e-0",
            b: common_vendor.p({
              ["model-value"]: isSectionFullySelected(String(section)),
              indeterminate: isSectionPartiallySelected(String(section)),
              ["custom-class"]: "section-checkbox"
            }),
            c: common_vendor.t(section),
            d: common_vendor.t(commodities.length),
            e: getSectionSelectedCount(String(section)) > 0
          }, getSectionSelectedCount(String(section)) > 0 ? {
            f: common_vendor.t(getSectionSelectedCount(String(section)))
          } : {}, {
            g: common_vendor.o(($event) => toggleSection(String(section)), String(section)),
            h: "14aff35e-6-" + i0 + ",14aff35e-0",
            i: common_vendor.p({
              name: isSectionCollapsed(String(section)) ? "arrow-down" : "arrow-up",
              size: "24rpx",
              ["custom-class"]: "toggle-icon"
            }),
            j: common_vendor.o(($event) => toggleSectionCollapse(String(section)), String(section)),
            k: !isSectionCollapsed(String(section))
          }, !isSectionCollapsed(String(section)) ? {
            l: common_vendor.f(commodities, (commodity, k1, i1) => {
              return {
                a: "14aff35e-7-" + i0 + "-" + i1 + ",14aff35e-0",
                b: common_vendor.p({
                  ["model-value"]: isCommoditySelected(commodity.name),
                  ["custom-class"]: "commodity-checkbox"
                }),
                c: common_vendor.t(commodity.name),
                d: common_vendor.t(commodity.product_id),
                e: commodity.product_id,
                f: common_vendor.o(($event) => toggleCommodity(commodity.name), commodity.product_id)
              };
            })
          } : {}, {
            m: String(section)
          });
        })
      }) : {
        n: common_vendor.p({
          size: "40rpx"
        })
      }, {
        o: common_vendor.o(handleCancel),
        p: common_vendor.p({
          type: "default",
          ["custom-class"]: "cancel-button",
          disabled: isSaving.value
        }),
        q: common_vendor.o(handleConfirm),
        r: common_vendor.p({
          type: "primary",
          ["custom-class"]: "confirm-button",
          disabled: !hasSelected.value,
          loading: isSaving.value
        }),
        s: common_vendor.o(($event) => internalVisible.value = $event),
        t: common_vendor.p({
          position: "right",
          ["z-index"]: 1e3,
          ["close-on-click-modal"]: true,
          ["custom-style"]: "width: 75vw; height: 100vh;",
          ["custom-class"]: "commodity-selector-popup",
          modelValue: internalVisible.value
        })
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-14aff35e"]]);
wx.createComponent(Component);
