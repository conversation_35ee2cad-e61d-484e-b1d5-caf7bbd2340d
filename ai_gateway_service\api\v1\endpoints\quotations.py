"""
Quotation-related API endpoints
"""

import logging
from fastapi import APIRouter, HTTPException
from fastapi.responses import JSONResponse

from schemas.quotation import (
    QuotationParseRequest, 
    ParsedQuotationResponse,
    ParsedQuotationData
)
from services.quotation_parser import get_quotation_service

logger = logging.getLogger(__name__)

router = APIRouter()


@router.post("/parse", response_model=ParsedQuotationResponse, summary="解析报价文本")
async def parse_quotation(request: QuotationParseRequest) -> ParsedQuotationResponse:
    """
    解析非结构化的报价文本，提取关键信息并返回结构化数据
    
    - **text**: 用户输入的非结构化报价文本
    """
    try:
        logger.info(f"Received quotation parsing request for text length: {len(request.text)}")
        
        if not request.text.strip():
            return ParsedQuotationResponse(
                success=False,
                data=None,
                message="输入文本不能为空"
            )
        
        # Parse the quotation text using AI service
        quotation_service = get_quotation_service()
        result = await quotation_service.parse_quotation_text(request.text)
        
        # Validate parsed data if successful
        if result.success and result.data:
            is_valid = quotation_service.validate_quotation_data(result.data)
            if not is_valid:
                logger.warning("Parsed data failed validation")
                result.message = "解析成功但数据可能不完整，请检查"
        
        return result
        
    except Exception as e:
        logger.error(f"Unexpected error in parse_quotation endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="服务器内部错误，请稍后重试"
        )


@router.get("/health", summary="健康检查")
async def health_check():
    """
    AI Gateway 服务健康检查端点
    """
    return {"status": "healthy", "service": "ai_gateway_service"}


@router.get("/examples", response_model=list[dict], summary="获取解析示例")
async def get_parsing_examples():
    """
    返回一些报价文本解析的示例，供前端参考和测试使用
    """
    from prompts.quotation_prompts import EXAMPLE_QUOTATIONS
    return EXAMPLE_QUOTATIONS