import { http } from '@/http/http'
import type {
  ICommodity,
  ICommodityRequest,
  ICommodityListRequest,
  ICommodityListResponse,
} from '@/types'

/**
 * 获取所有商品列表
 */
export function getAllCommodityList() {
  return http.get<ICommodity[]>('/dianjia/commodity/getAllCommodityList')
}

/**
 * 分页获取商品列表
 */
export function getCommodityList(params: ICommodityListRequest) {
  return http.get<ICommodityListResponse>('/dianjia/commodity/getCommodityList', params)
}

/**
 * 根据ID获取商品详情
 */
export function getCommodityById(id: number) {
  return http.get<ICommodity>('/dianjia/commodity/findCommodity', { ID: id })
}

/**
 * 创建商品
 */
export function createCommodity(data: ICommodityRequest) {
  return http.post<ICommodity>('/dianjia/commodity/createCommodity', data)
}

/**
 * 更新商品
 */
export function updateCommodity(data: ICommodityRequest) {
  return http.put<boolean>('/dianjia/commodity/updateCommodity', data)
}

/**
 * 删除商品
 */
export function deleteCommodity(id: number) {
  return http.delete<boolean>('/dianjia/commodity/deleteCommodity', { params: { ID: id } })
}

/**
 * 批量删除商品
 */
export function deleteCommodityByIds(ids: number[]) {
  return http.delete<boolean>('/dianjia/commodity/deleteCommodityByIds', { params: { IDs: ids } })
}

