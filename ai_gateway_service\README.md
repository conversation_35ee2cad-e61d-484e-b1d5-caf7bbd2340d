# AI Gateway Service

统一的AI网关微服务，为DianJia项目提供智能解析、数据分析等AI功能。

## 功能特性

- **智能报价解析**: 将非结构化文本转换为结构化报价数据
- **可扩展架构**: 支持未来添加更多AI功能
- **高性能异步**: 基于FastAPI的异步处理
- **类型安全**: 使用Pydantic进行数据校验

## 技术栈

- **Python 3.10+**: 开发语言
- **FastAPI**: Web框架
- **LangChain**: AI工具链
- **OpenAI GPT-4**: 大语言模型
- **Pydantic**: 数据校验
- **UV**: 包管理器

## 快速开始

### 1. 环境准备

```bash
# 克隆项目（如果独立部署）
cd ai_gateway_service

# 使用uv安装依赖
uv sync

# 复制环境变量配置
cp .env.example .env
# 编辑 .env 文件，填入你的 OpenAI API Key
```

### 2. 配置环境变量

编辑 `.env` 文件：

```bash
OPENAI_API_KEY=your_actual_openai_api_key
OPENAI_MODEL=gpt-4-turbo
DEBUG=true
```

### 3. 启动服务

```bash
# 开发模式
uv run python main.py

# 或者使用uvicorn直接启动
uv run uvicorn main:app --host 0.0.0.0 --port 8001 --reload
```

### 4. 访问API文档

启动后访问：
- API文档: http://localhost:8001/docs
- 健康检查: http://localhost:8001/health

## API 接口

### 智能报价解析

**端点**: `POST /api/v1/quotations/parse`

**请求体**:
```json
{
  "text": "上海地区仓库自提，中天螺纹钢，规格HRB400E 20mm，500吨，价格比RB2501盘面高120，过磅含税，3天内有效。"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "title": "中天螺纹钢基差报价",
    "commodity_name": "螺纹钢",
    "delivery_location": "上海地区仓库",
    "brand": "中天",
    "specifications": "HRB400E 20mm",
    "description": "过磅含税",
    "price_type": "Basis",
    "price": 120,
    "instrument_symbol": "RB2501",
    "quantity": 500,
    "unit": "吨",
    "expires_in_days": 3
  },
  "message": "解析成功"
}
```

## 项目结构

```
ai_gateway_service/
├── api/v1/
│   ├── router.py           # 主路由
│   └── endpoints/
│       └── quotations.py   # 报价相关端点
├── core/
│   ├── config.py          # 配置管理
│   └── logging.py         # 日志配置
├── services/
│   └── quotation_parser.py # 报价解析服务
├── schemas/
│   └── quotation.py       # 数据模型
├── prompts/
│   └── quotation_prompts.py # Prompt模板
├── .env.example           # 环境变量示例
├── main.py               # 应用入口
└── pyproject.toml        # 项目配置
```

## 开发指南

### 代码风格

项目使用以下工具保证代码质量：

```bash
# 代码格式化
uv run black .

# 导入排序
uv run isort .

# 代码检查
uv run flake8 .

# 类型检查
uv run mypy .
```

### 测试

```bash
# 运行测试
uv run pytest

# 运行测试并查看覆盖率
uv run pytest --cov=.
```

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t ai-gateway-service .

# 运行容器
docker run -d \
  --name ai-gateway \
  -p 8001:8001 \
  --env-file .env \
  ai-gateway-service
```

### 生产环境

```bash
# 使用生产级WSGI服务器
uv run gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker --bind 0.0.0.0:8001
```

## 扩展指南

### 添加新的AI功能

1. 在 `schemas/` 中定义新的数据模型
2. 在 `prompts/` 中创建新的Prompt模板
3. 在 `services/` 中实现业务逻辑
4. 在 `api/v1/endpoints/` 中创建新的端点文件
5. 在 `api/v1/router.py` 中注册新的路由

### 示例：添加数据分析功能

```python
# schemas/data_analysis.py
class DataAnalysisRequest(BaseModel):
    data: dict
    analysis_type: str

# services/data_analyzer.py
async def analyze_data(data: dict, analysis_type: str):
    # 实现数据分析逻辑
    pass

# api/v1/endpoints/data_analysis.py
@router.post("/analyze")
async def analyze_data(request: DataAnalysisRequest):
    # 处理数据分析请求
    pass
```

## 故障排除

### 常见问题

1. **OpenAI API Key 错误**: 检查 `.env` 文件中的 `OPENAI_API_KEY` 是否正确
2. **模型调用失败**: 检查网络连接和API配额
3. **解析格式错误**: 检查Prompt模板和Pydantic模型定义

### 日志查看

```bash
# 实时查看日志
tail -f logs/app.log

# 调试模式启动
DEBUG=true uv run python main.py
```