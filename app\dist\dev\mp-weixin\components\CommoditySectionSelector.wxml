<wd-popup wx:if="{{t}}" class="data-v-14aff35e" u-s="{{['d']}}" u-i="14aff35e-0" bind:__l="__l" bindupdateModelValue="{{s}}" u-p="{{t}}"><view class="selector-container data-v-14aff35e"><view class="header data-v-14aff35e"><text class="title data-v-14aff35e">选择关注品种</text><wd-button wx:if="{{c}}" class="data-v-14aff35e" u-s="{{['d']}}" bindclick="{{b}}" u-i="14aff35e-1,14aff35e-0" bind:__l="__l" u-p="{{c}}"><wd-icon wx:if="{{a}}" class="data-v-14aff35e" u-i="14aff35e-2,14aff35e-1" bind:__l="__l" u-p="{{a}}"/></wd-button></view><view class="fixed-top data-v-14aff35e"><view class="search-section data-v-14aff35e"><wd-input wx:if="{{g}}" class="data-v-14aff35e" u-s="{{['prefix']}}" bindclear="{{e}}" u-i="14aff35e-3,14aff35e-0" bind:__l="__l" bindupdateModelValue="{{f}}" u-p="{{g}}"><wd-icon class="data-v-14aff35e" u-i="14aff35e-4,14aff35e-3" bind:__l="__l" u-p="{{d}}" slot="prefix"/></wd-input></view><view wx:if="{{h}}" class="selected-count data-v-14aff35e"> 已选择 {{i}} 个品种 </view></view><view wx:if="{{j}}" class="content data-v-14aff35e"><scroll-view class="scroll-content data-v-14aff35e" scroll-y><view wx:if="{{k}}" class="empty-state data-v-14aff35e"><text class="empty-text data-v-14aff35e">{{l}}</text></view><view wx:else class="section-list data-v-14aff35e"><view wx:for="{{m}}" wx:for-item="commodities" wx:key="m" class="section-group data-v-14aff35e"><view class="section-header data-v-14aff35e"><view class="section-title-content data-v-14aff35e" bindtap="{{commodities.g}}"><wd-checkbox wx:if="{{commodities.b}}" class="data-v-14aff35e" u-i="{{commodities.a}}" bind:__l="__l" u-p="{{commodities.b}}"/><text class="section-title data-v-14aff35e">{{commodities.c}}</text><text class="section-count data-v-14aff35e">{{commodities.d}} <text wx:if="{{commodities.e}}" class="selected-count data-v-14aff35e"> （{{commodities.f}}选中） </text></text></view><view class="section-toggle data-v-14aff35e" bindtap="{{commodities.j}}"><wd-icon wx:if="{{commodities.i}}" class="data-v-14aff35e" u-i="{{commodities.h}}" bind:__l="__l" u-p="{{commodities.i}}"/></view></view><view wx:if="{{commodities.k}}" class="commodity-list data-v-14aff35e"><view wx:for="{{commodities.l}}" wx:for-item="commodity" wx:key="e" class="commodity-item data-v-14aff35e" bindtap="{{commodity.f}}"><wd-checkbox wx:if="{{commodity.b}}" class="data-v-14aff35e" u-i="{{commodity.a}}" bind:__l="__l" u-p="{{commodity.b}}"/><view class="commodity-info data-v-14aff35e"><text class="commodity-name data-v-14aff35e">{{commodity.c}}</text><text class="commodity-code data-v-14aff35e">{{commodity.d}}</text></view></view></view></view></view></scroll-view></view><view wx:else class="loading-state data-v-14aff35e"><wd-loading wx:if="{{n}}" class="data-v-14aff35e" u-i="14aff35e-8,14aff35e-0" bind:__l="__l" u-p="{{n}}"/><text class="loading-text data-v-14aff35e">加载中...</text></view><view class="footer data-v-14aff35e"><wd-button wx:if="{{p}}" class="data-v-14aff35e" u-s="{{['d']}}" bindclick="{{o}}" u-i="14aff35e-9,14aff35e-0" bind:__l="__l" u-p="{{p}}"> 取消 </wd-button><wd-button wx:if="{{r}}" class="data-v-14aff35e" u-s="{{['d']}}" bindclick="{{q}}" u-i="14aff35e-10,14aff35e-0" bind:__l="__l" u-p="{{r}}"> 确认选择 </wd-button></view></view></wd-popup>