# 第一阶段实施计划

> **版本**: 7.0.0  
> **负责人**: 开发团队  
> **状态**: 实施中  
> **最后更新**: 2025-08-07

---

## 1. 实施目标

基于现有报价系统核心功能，第一阶段重点实现商品分类管理、个性化筛选和市场功能优化，为用户提供更加精准和高效的报价浏览体验。

### 1.1 核心目标
- **商品分类管理**: 为商品增加版块字段，简化分类体系
- **个性化筛选**: 实现用户关注品种的个性化展示
- **市场功能优化**: 增强筛选功能，支持价格类型和报价类型筛选
- **排序优化**: 默认按时间从新到旧排序，提升信息时效性

### 1.2 实施范围
- 后端：扩展商品模型，增加版块字段；扩展用户模型，增加extra字段存储关注信息
- 前端：优化市场页面，增加品种选择和筛选功能
- 数据库：简化表结构，提供高效的查询接口

---

## 2. 用户进入页面流程设计

### 2.1 用户进入公开报价页面的完整流程

#### 2.1.1 首次进入流程（用户没有关注的品种）
1. **用户打开公开报价页面**
   - 页面加载时检查用户是否有关注的品种
   - 如果用户没有关注任何品种，触发品种选择弹窗

2. **弹出品种选择弹窗**
   - 弹窗按section分组显示所有可用的品种
   - 每个section下显示对应的commodity列表
   - 支持多选功能，用户可以选择多个品种
   - 弹窗最少需要选择一个品种才能保存

3. **用户选择并保存**
   - 用户在弹窗中选择感兴趣的品种
   - 点击保存按钮，将选择结果保存到用户模型的extra字段中
   - 弹窗关闭，主页面自动刷新

4. **主页面刷新显示关注内容**
   - 页面重新加载用户关注的品种相关报价
   - 默认按时间从新到旧排序显示
   - 显示用户已选择的品种标签

#### 2.1.2 非首次进入流程（用户已有关注品种）
1. **用户打开公开报价页面**
   - 页面加载时检查用户是否有关注的品种
   - 如果用户已有关注品种，直接进入主页面

2. **直接获取用户关注的品种报价**
   - 系统根据用户模型的extra字段中存储的关注品种列表，查询相关报价
   - 只显示用户关注品种的报价内容
   - 默认按时间从新到旧排序显示

3. **显示已关注品种标签**
   - 在页面顶部显示用户已关注的品种标签
   - 提供筛选和编辑功能

#### 2.1.3 主动筛选流程
1. **用户点击品种筛选按钮**
   - 用户可以主动点击品种筛选按钮
   - 弹出品种选择弹窗，显示当前用户已选择的品种

2. **弹出品种选择弹窗**
   - 弹窗按section分组显示所有可用品种
   - 已选择的品种显示为选中状态
   - 支持多选功能，可以增减选择的品种
   - 弹窗最少需要选择一个品种

3. **用户修改选择并保存**
   - 用户可以修改已选择的品种
   - 点击保存按钮，更新用户模型的extra字段
   - 弹窗关闭，主页面自动刷新

4. **主页面更新显示**
   - 页面根据用户新的选择更新报价列表
   - 显示更新后的品种标签

### 2.2 关键交互设计要点

#### 2.2.1 弹窗触发机制
- **首次进入强制弹窗**: 新用户首次进入必须选择至少一个品种
- **主动筛选弹窗**: 用户可以随时点击筛选按钮修改选择
- **最少选择限制**: 弹窗中用户最少需要选择一个品种才能保存

#### 2.2.2 数据展示逻辑
- **按section分组**: 品种按section分组显示，便于用户查找
- **多选支持**: 用户可以同时选择多个不同section的品种
- **状态保持**: 弹窗中显示用户当前的选择状态

#### 2.2.3 用户体验优化
- **即时反馈**: 选择品种后提供即时视觉反馈
- **保存确认**: 保存成功后提供明确的成功提示
- **页面刷新**: 保存后自动刷新页面，显示最新内容

---

## 3. 技术实施方案

### 3.1 后端实施计划

#### 3.1.1 扩展 Commodity 模型

**目标**: 在现有商品模型基础上增加版块字段，采用灵活的字符串格式

**需求**: 
- 为commodities表添加section字段，支持字符串格式的版块分类
- 无需预定义版块内容，保持数据表的灵活性
- 直接使用gorm定义，无需复杂的表关联

#### 3.1.2 扩展 User 模型

**目标**: 在现有用户模型基础上增加extra字段，采用JSON格式存储用户关注信息

**需求**:
- 为sys_users表添加extra字段，支持JSON格式的扩展数据
- 在extra字段中存储用户关注的品种信息
- 直接使用gorm的JSON类型支持，无需创建额外的关注表

#### 3.1.3 简化数据表结构

**目标**: 不需要预定义版块内容，commodity表格保持灵活性，直接使用gorm定义

**需求**:
- 更新commodities表结构，添加section字段
- 更新sys_users表结构，添加extra字段
- 为section字段创建索引，优化查询性能
- 删除不再需要的分类表，简化数据库结构

#### 3.1.4 扩展商品查询接口

**目标**: 支持按版块、价格类型、报价类型筛选，默认按时间排序

**需求**:
- 实现商品列表查询接口，支持section筛选
- 支持按名称、产品ID、交易所ID等多条件筛选
- 默认按创建时间倒序排序

#### 3.1.5 扩展报价查询接口

**目标**: 支持按版块、价格类型、报价类型筛选，默认按时间排序

**需求**:
- 实现报价列表查询接口，支持通过section筛选
- 支持价格类型、报价类型等筛选条件
- 默认按创建时间倒序排序

#### 3.1.6 扩展用户查询接口

**目标**: 支持用户关注品种的查询和更新

**需求**:
- 实现用户信息查询接口，支持解析extra字段中的关注品种信息
- 实现用户信息更新接口，支持更新extra字段中的关注品种信息
- 提供JSON字段的序列化和反序列化支持

### 3.2 前端实施计划

#### 3.2.1 创建品种选择弹窗

**目标**: 实现按section分组的品种选择弹窗，支持多选功能

**需求**:
- 创建品种选择弹窗组件，支持按section分组显示
- 实现多选功能，用户可以选择多个品种
- 支持搜索功能，便于用户快速查找品种
- 实现最少选择一个品种的验证逻辑

#### 3.2.2 优化市场页面

**目标**: 增加品种筛选功能，支持用户关注品种的个性化展示

**需求**:
- 修改市场页面，增加用户关注品种的显示
- 实现首次进入自动弹窗选择逻辑
- 实现主动筛选功能，支持用户修改选择
- 显示用户已选择的品种标签

#### 3.2.3 新增筛选功能

**目标**: 支持价格类型（基差、一口价、商议）和报价类型（求购、出售）筛选

**需求**:
- 实现价格类型筛选功能
- 实现报价类型筛选功能
- 支持多种筛选条件的组合使用
- 提供筛选结果的实时更新

### 3.3 数据库实施计划

#### 3.3.1 更新商品表

**目标**: 为现有商品表增加版块字段，采用灵活的字符串格式

**需求**:
- 为commodities表添加section字段
- 为section字段创建索引，优化查询性能
- 创建复合索引，进一步提升查询效率

#### 3.3.2 更新用户表

**目标**: 为现有用户表增加extra字段，采用JSON格式存储用户关注信息

**需求**:
- 为sys_users表添加extra字段，使用JSON类型
- 在extra字段中存储用户关注的品种信息
- 提供JSON字段的查询和更新支持

---

## 4. 实施步骤

### 4.1 第一周：后端开发和数据库更新

#### Day 1-2: 数据库表更新
- [ ] 更新 commodities 表增加 section 字段
- [ ] 创建相关索引
- [ ] 更新 sys_users 表增加 extra 字段

#### Day 3-4: 后端模型和服务开发
- [ ] 更新 Commodity 模型结构
- [ ] 更新 User 模型结构，添加extra字段
- [ ] 实现商品查询服务
- [ ] 实现用户关注服务（基于extra字段）

#### Day 5: 接口开发和测试
- [ ] 实现商品查询接口
- [ ] 实现用户关注接口（基于extra字段）
- [ ] 扩展报价查询接口支持版块筛选
- [ ] 编写单元测试

### 4.2 第二周：前端开发和页面优化

#### Day 1-2: 品种选择弹窗开发
- [ ] 创建品种选择弹窗组件
- [ ] 实现按section分组显示
- [ ] 实现多选功能和验证逻辑
- [ ] 集成后端接口

#### Day 3-4: 市场页面优化
- [ ] 修改市场页面增加品种选择栏
- [ ] 实现首次进入自动弹窗逻辑
- [ ] 实现主动筛选功能
- [ ] 集成价格类型和报价类型筛选

#### Day 5: 用户关注功能
- [ ] 实现用户关注品种的本地存储
- [ ] 实现关注品种的服务端同步（基于extra字段）
- [ ] 优化页面跳转逻辑
- [ ] 完善用户体验

### 4.3 第三周：集成测试和优化

#### Day 1-2: 功能集成测试
- [ ] 端到端功能测试
- [ ] 数据一致性测试
- [ ] 性能测试
- [ ] 用户体验测试

#### Day 3-4: 问题修复和优化
- [ ] 修复测试发现的问题
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 代码质量优化

#### Day 5: 文档和部署
- [ ] 更新技术文档
- [ ] 编写用户手册
- [ ] 准备部署
- [ ] 发布上线

---

## 5. 技术要点

### 5.1 后端技术要点

#### 5.1.1 数据模型设计
- **灵活结构**: commodity表格采用字符串格式的section字段，无需预定义内容
- **JSON存储**: 用户关注信息存储在user模型的extra字段中，采用JSON格式
- **简单关联**: 直接使用gorm定义，无需复杂的表关联
- **扩展性强**: extra字段支持存储其他扩展信息

#### 5.1.2 接口设计
- **RESTful风格**: 遵循REST API设计原则
- **统一响应**: 使用统一的响应格式
- **错误处理**: 完善的错误码和错误信息
- **性能优化**: 支持分页查询和索引优化

#### 5.1.3 数据库优化
- **索引策略**: 为section字段创建索引，支持高效查询
- **JSON查询**: 支持JSON字段的查询和更新操作
- **简化结构**: 删除不必要的关注表，减少复杂度
- **直接查询**: 避免多表JOIN，提升查询性能

### 5.2 前端技术要点

#### 5.2.1 组件设计
- **模块化**: 将功能拆分为独立组件
- **可复用**: 组件支持复用和配置
- **响应式**: 适配不同屏幕尺寸
- **性能优化**: 使用虚拟滚动等优化技术

#### 5.2.2 状态管理
- **本地存储**: 使用localStorage存储用户偏好
- **状态同步**: 本地状态与服务端同步
- **缓存策略**: 合理使用缓存提升性能
- **数据一致性**: 确保前后端数据一致

#### 5.2.3 用户体验
- **流畅交互**: 优化页面切换和加载体验
- **即时反馈**: 提供操作反馈和状态提示
- **个性化**: 基于用户偏好提供个性化内容
- **无障碍**: 考虑无障碍访问需求

---

## 6. 成功标准

### 6.1 技术标准

#### 6.1.1 功能完整性
- [ ] 所有计划功能100%实现
- [ ] 功能测试覆盖率 ≥ 90%
- [ ] 接口响应时间 ≤ 500ms
- [ ] 页面加载时间 ≤ 2s

#### 6.1.2 代码质量
- [ ] 代码审查通过率 100%
- [ ] 单元测试覆盖率 ≥ 80%
- [ ] 静态代码分析无严重问题
- [ ] 文档完整性 ≥ 90%

### 6.2 业务标准

#### 6.2.1 用户体验
- [ ] 用户满意度 ≥ 4.5/5.0
- [ ] 功能使用率 ≥ 80%
- [ ] 用户留存率 ≥ 90%
- [ ] 操作成功率 ≥ 95%

#### 6.2.2 业务指标
- [ ] 报价浏览量提升 ≥ 30%
- [ ] 用户关注率 ≥ 60%
- [ ] 筛选功能使用率 ≥ 70%
- [ ] 页面跳出率 ≤ 20%

---

## 7. 风险评估和应对

### 7.1 技术风险

#### 7.1.1 数据一致性风险
**风险**: 字符串格式的section字段可能导致数据不一致
**应对**: 
- 建立数据验证机制
- 提供数据清理工具
- 实现模糊查询支持

#### 7.1.2 JSON字段操作风险
**风险**: JSON字段的查询和更新可能存在性能问题
**应对**:
- 使用数据库原生的JSON支持
- 为JSON字段创建适当的索引
- 实现缓存机制，减少直接查询

#### 7.1.3 性能风险
**风险**: 新增筛选功能可能影响查询性能
**应对**:
- 优化数据库索引
- 实现查询缓存
- 使用分页和懒加载
- 监控性能指标

#### 7.1.4 兼容性风险
**风险**: 新功能可能影响现有功能
**应对**:
- 保持向后兼容
- 充分的回归测试
- 灰度发布策略
- 快速回滚机制

### 7.2 业务风险

#### 7.2.1 用户接受度风险
**风险**: 用户可能不适应新的筛选方式
**应对**:
- 提供用户引导和帮助
- 保持原有功能可用
- 收集用户反馈并快速迭代
- A/B测试验证效果

#### 7.2.2 数据质量风险
**风险**: 版块数据质量可能影响筛选效果
**应对**:
- 建立数据质量检查机制
- 提供数据管理功能
- 定期清理和维护数据
- 用户反馈数据问题

---

## 8. 后续规划

### 8.1 第二阶段计划
- **智能推荐**: 基于用户行为实现个性化推荐
- **通知系统**: 实现报价更新和关注通知
- **统计分析**: 完善浏览统计和趋势分析
- **社交功能**: 增加用户互动和分享功能

### 8.2 第三阶段计划
- **高级筛选**: 支持更复杂的筛选条件
- **数据可视化**: 提供丰富的图表展示
- **移动端优化**: 针对移动端体验优化
- **API开放**: 提供第三方集成接口

---

## 9. 总结

第一阶段实施计划聚焦于商品版块管理和市场功能优化，通过简化数据结构、增强筛选功能和优化用户体验，为报价系统打下坚实的基础。实施过程中将注重技术质量和用户体验，确保系统稳定性和可用性。

通过本阶段的实施，预期将显著提升用户的报价浏览体验，提高信息匹配效率，为后续高级功能的开发奠定良好基础。

**核心优势**:
- **结构简化**: 无需预定义版块内容，commodity表格保持灵活性
- **存储优化**: 用户关注信息存储在user模型的extra字段中，避免创建额外的关注表
- **查询高效**: 直接字符串匹配和JSON查询，避免多表JOIN操作
- **维护成本低**: 不需要复杂的分类管理后台和关注表管理
- **扩展性好**: 版块内容可动态添加，extra字段支持存储其他扩展信息

**用户体验亮点**:
- **首次引导**: 新用户首次进入时自动引导选择关注品种
- **个性化展示**: 根据用户关注品种展示个性化内容
- **灵活筛选**: 支持用户随时修改关注品种，提供灵活的筛选功能
- **最少选择**: 确保用户最少选择一个品种，保证内容的针对性
