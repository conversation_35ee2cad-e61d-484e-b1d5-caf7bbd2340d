"""
AI Gateway Service startup script
Provides convenient commands for development and production
"""

import os
import sys
import subprocess
from pathlib import Path


def check_env_file():
    """Check if .env file exists and has required variables"""
    env_path = Path(".env")
    if not env_path.exists():
        print("错误: 未找到 .env 文件")
        print("请复制 .env.example 到 .env 并填入你的配置")
        return False
    
    # Check for required environment variables
    with open(env_path, 'r', encoding='utf-8') as f:
        content = f.read()
        
    if "sk-test-key-replace-with-real-key" in content:
        print("警告: 请在 .env 文件中设置真实的 OPENAI_API_KEY")
        return False
        
    return True


def run_dev():
    """Run development server"""
    print("启动开发服务器...")
    if not check_env_file():
        return
    
    try:
        subprocess.run(["uv", "run", "python", "main.py"], check=True)
    except subprocess.CalledProcessError:
        print("启动失败，请检查配置")
    except KeyboardInterrupt:
        print("服务已停止")


def run_prod():
    """Run production server"""
    print("启动生产服务器...")
    if not check_env_file():
        return
    
    try:
        subprocess.run([
            "uv", "run", "gunicorn", "main:app",
            "-w", "4",
            "-k", "uvicorn.workers.UvicornWorker",
            "--bind", "0.0.0.0:8001"
        ], check=True)
    except subprocess.CalledProcessError:
        print("启动失败，请检查配置")
    except KeyboardInterrupt:
        print("服务已停止")


def run_test():
    """Run test suite"""
    print("运行测试...")
    try:
        subprocess.run(["uv", "run", "python", "test_service.py"], check=True)
    except subprocess.CalledProcessError:
        print("测试失败")


def install_deps():
    """Install dependencies"""
    print("安装依赖...")
    try:
        subprocess.run(["uv", "sync"], check=True)
        print("依赖安装完成")
    except subprocess.CalledProcessError:
        print("依赖安装失败")


def format_code():
    """Format code using black and isort"""
    print("格式化代码...")
    try:
        subprocess.run(["uv", "run", "black", "."], check=True)
        subprocess.run(["uv", "run", "isort", "."], check=True)
        print("代码格式化完成")
    except subprocess.CalledProcessError:
        print("代码格式化失败")


def lint_code():
    """Run code linting"""
    print("运行代码检查...")
    try:
        subprocess.run(["uv", "run", "flake8", "."], check=True)
        subprocess.run(["uv", "run", "mypy", "."], check=True)
        print("代码检查通过")
    except subprocess.CalledProcessError:
        print("代码检查发现问题")


def show_help():
    """Show help information"""
    print("AI Gateway Service 管理脚本")
    print("=" * 40)
    print("使用方法: python start.py <command>")
    print("")
    print("可用命令:")
    print("  dev      - 启动开发服务器")
    print("  prod     - 启动生产服务器")
    print("  test     - 运行测试")
    print("  install  - 安装依赖")
    print("  format   - 格式化代码")
    print("  lint     - 代码检查")
    print("  help     - 显示帮助信息")


def main():
    """Main entry point"""
    if len(sys.argv) < 2:
        show_help()
        return
    
    command = sys.argv[1].lower()
    
    commands = {
        'dev': run_dev,
        'prod': run_prod,
        'test': run_test,
        'install': install_deps,
        'format': format_code,
        'lint': lint_code,
        'help': show_help,
    }
    
    if command in commands:
        commands[command]()
    else:
        print(f"未知命令: {command}")
        show_help()


if __name__ == "__main__":
    main()