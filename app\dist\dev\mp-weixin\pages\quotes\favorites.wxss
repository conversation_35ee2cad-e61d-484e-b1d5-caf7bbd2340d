/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.page-container.data-v-2f4046e0 {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}
.header-section.data-v-2f4046e0 {
  position: sticky;
  top: 0;
  z-index: 100;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  padding: 20rpx;
  padding-top: calc(20rpx + env(safe-area-inset-top));
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.header-card.data-v-2f4046e0 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
.header-title.data-v-2f4046e0 {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}
.header-subtitle.data-v-2f4046e0 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.data-v-2f4046e0 .header-icon {
  color: rgba(255, 255, 255, 0.9) !important;
}
.scroll-container.data-v-2f4046e0 {
  flex: 1;
  padding: 20rpx;
  box-sizing: border-box;
}
.quotation-list.data-v-2f4046e0 {
  width: 100%;
  box-sizing: border-box;
}
.quotation-list.data-v-2f4046e0 .quotation-card {
  background: rgba(255, 255, 255, 0.95) !important;
  -webkit-backdrop-filter: blur(10rpx) !important;
          backdrop-filter: blur(10rpx) !important;
  border-radius: 20rpx !important;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12) !important;
  transition: all 0.3s ease !important;
}
.quotation-list.data-v-2f4046e0 .quotation-card:hover {
  transform: translateY(-4rpx) !important;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
}
.empty-state.data-v-2f4046e0 {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  margin: 36rpx;
}
.empty-state.data-v-2f4046e0 .empty-icon {
  color: #E4E7ED !important;
  margin-bottom: 32rpx;
}
.empty-state .empty-title.data-v-2f4046e0 {
  font-size: 32rpx;
  color: #909399;
  font-weight: 500;
  margin-bottom: 16rpx;
}
.empty-state .empty-subtitle.data-v-2f4046e0 {
  font-size: 24rpx;
  color: #C0C4CC;
  margin-bottom: 48rpx;
}
.empty-state.data-v-2f4046e0 .empty-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border-radius: 44rpx !important;
  font-weight: 600 !important;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1) !important;
  transition: all 0.3s ease !important;
}
.empty-state.data-v-2f4046e0 .empty-button:hover {
  transform: translateY(-2rpx) !important;
  box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3) !important;
}
.loading-more.data-v-2f4046e0 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}
.loading-more.data-v-2f4046e0 .loading-spinner {
  color: #667eea !important;
}
.loading-more .loading-text.data-v-2f4046e0 {
  margin-left: 16rpx;
  font-size: 26rpx;
  color: #667eea;
  font-weight: 500;
}
.no-more.data-v-2f4046e0 {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  margin: 20rpx;
}
.no-more text.data-v-2f4046e0 {
  position: relative;
  font-size: 24rpx;
  color: #909399;
  font-weight: 400;
  padding: 0 40rpx;
}
.no-more text.data-v-2f4046e0::before, .no-more text.data-v-2f4046e0::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 60rpx;
  height: 2rpx;
  background-color: #e4e7ed;
}
.no-more text.data-v-2f4046e0::before {
  left: -80rpx;
}
.no-more text.data-v-2f4046e0::after {
  right: -80rpx;
}