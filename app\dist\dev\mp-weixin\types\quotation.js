"use strict";
const QUOTATION_PRICE_TYPE_CONFIG = {
  Fixed: {
    label: "一口价",
    description: "固定价格报价",
    icon: "price-tag"
  },
  Basis: {
    label: "基差报价",
    description: "相对于期货合约的基差报价",
    icon: "trending-up"
  },
  Negotiable: {
    label: "商议",
    description: "价格面议，具体价格可沟通协商",
    icon: "chat"
  }
};
const QUOTATION_TRADE_TYPE_CONFIG = {
  sell: {
    label: "出售",
    color: "#1890ff",
    bgColor: "linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)",
    borderColor: "#91d5ff"
  },
  buy: {
    label: "求购",
    color: "#52c41a",
    bgColor: "linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)",
    borderColor: "#b7eb8f"
  }
};
exports.QUOTATION_PRICE_TYPE_CONFIG = QUOTATION_PRICE_TYPE_CONFIG;
exports.QUOTATION_TRADE_TYPE_CONFIG = QUOTATION_TRADE_TYPE_CONFIG;
