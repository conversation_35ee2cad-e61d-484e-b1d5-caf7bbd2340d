import type { IUser } from './user'
import type { IInstrumentSelectItem } from './instrument'

// 报价状态枚举
export type QuotationStatus = 'Draft' | 'Active'

// 报价价格类型枚举
export type QuotationPriceType = 'Fixed' | 'Basis' | 'Negotiable' // Fixed: 一口价, Basis: 基差报价, Negotiable: 商议

// 报价基础接口 - 与后端Go结构体对齐
export interface IQuotation {
  // 基础字段（GVA_MODEL）
  id: number
  createdAt: string
  updatedAt: string
  deletedAt?: string
  
  // 基本信息
  userID: number
  user?: IUser
  title: string
  isBuyRequest: boolean // 是否为求购请求(true:求购,false:出售)
  
  // 商品信息
  commodityName: string
  deliveryLocation: string
  brand?: string
  specifications?: string
  description?: string
  
  // 价格信息
  priceType: QuotationPriceType
  price: number
  instrumentRefID?: number
  instrumentRef?: IInstrumentSelectItem // 期货合约信息（基差报价用）
  
  // 生命周期管理
  expiresAt: string // ISO时间字符串
  status: QuotationStatus
}

// 报价响应（含计算字段）
export interface IQuotationResponse extends IQuotation {
  // 计算字段
  isExpired: boolean       // 是否已过期
  remainingHours: number   // 剩余小时数
}

// 创建报价请求
export interface ICreateQuotationRequest {
  title: string
  commodityName: string
  deliveryLocation: string
  brand?: string
  specifications?: string
  description?: string
  isBuyRequest?: boolean // 是否为求购请求，默认false(出售)
  priceType: QuotationPriceType
  price?: number // 商议类型时可选
  instrumentRefID?: number
  instrumentRef?: IInstrumentSelectItem // 前端辅助字段，不发送给后端
  expiresAt: string // ISO时间字符串
  status?: QuotationStatus // 创建时只能是Draft或直接Active
}

// 更新报价请求
export interface IUpdateQuotationRequest extends Omit<ICreateQuotationRequest, 'status'> {
  id: number
}

// 发布报价请求
export interface IPublishQuotationRequest {
  id: number
  expiresAt: string // 发布时设置过期时间
}

// 切换报价状态请求
export interface IToggleQuotationStatusRequest {
  id: number
}

// 删除报价请求
export interface IDeleteQuotationRequest {
  id: number
}

// 公开报价列表请求（公开市场用）
export interface IQuotationListRequest {
  page?: number
  pageSize?: number
  keyword?: string        // 关键词搜索（标题、企业名称等）
  priceType?: string      // 价格类型筛选
  isBuyRequest?: boolean  // 报价类型筛选（求购/出售）
  userID?: number         // 按用户ID筛选（用于用户报价主页）
  favoriteCommodities?: string[] // 用户关注的商品列表（用于个性化筛选）
}

// 我的报价列表请求
export interface IMyQuotationListRequest {
  page?: number
  pageSize?: number
  filter?: 'valid' | 'invalid' // valid: 有效报价(Active), invalid: 无效报价(Draft)
  status?: QuotationStatus      // 具体状态筛选
}

// 报价列表响应
export interface IQuotationListResponse {
  list: IQuotationResponse[]
  total: number
  page: number
  pageSize: number
}

// 报价详情请求参数
export interface IQuotationDetailParams {
  quotationId: number
}

// 报价筛选选项
export interface IQuotationFilterOptions {
  // 商品名称选项
  commodityNames: Array<{
    name: string
    count: number // 该商品名称下的报价数量
  }>
  
  // 价格类型选项
  priceTypes: Array<{
    type: QuotationPriceType
    label: string
    count: number
  }>
}

// 报价统计信息
export interface IQuotationStats {
  totalCount: number        // 总报价数
  activeCount: number       // 激活中报价数
  draftCount: number        // 草稿报价数
}

// 报价有效期选项
export interface IQuotationExpiryOption {
  label: string           // 显示文本，如 "当日有效"
  value: string          // 选项值，如 "today"
  hours: number          // 小时数，如 24
  description?: string   // 描述，如 "到今日23:59过期"
}

// 常用的报价有效期选项
export const QUOTATION_EXPIRY_OPTIONS: IQuotationExpiryOption[] = [
  {
    label: '当日有效',
    value: 'today',
    hours: 24,
    description: '到今日23:59过期'
  },
  {
    label: '3天内有效',
    value: '3days',
    hours: 72,
    description: '72小时后过期'
  },
  {
    label: '1周内有效',
    value: '1week',
    hours: 168,
    description: '7天后过期'
  },
  {
    label: '2周内有效',
    value: '2weeks',
    hours: 336,
    description: '14天后过期'
  },
  {
    label: '1个月内有效',
    value: '1month',
    hours: 720,
    description: '30天后过期'
  },
  {
    label: '自定义',
    value: 'custom',
    hours: 0,
    description: '请选择具体的过期时间'
  }
]

// 报价状态显示配置
export const QUOTATION_STATUS_CONFIG: Record<QuotationStatus, {
  label: string
  color: string
  bgColor: string
  description: string
}> = {
  Draft: {
    label: '草稿',
    color: '#909399',
    bgColor: '#f4f4f5',
    description: '报价草稿，未公开发布'
  },
  Active: {
    label: '激活中',
    color: '#67C23A',
    bgColor: '#f0f9ff',
    description: '报价已公开，可被其他用户查看'
  }
}

// 价格类型显示配置
export const QUOTATION_PRICE_TYPE_CONFIG: Record<QuotationPriceType, {
  label: string
  description: string
  icon?: string
}> = {
  Fixed: {
    label: '一口价',
    description: '固定价格报价',
    icon: 'price-tag'
  },
  Basis: {
    label: '基差报价',
    description: '相对于期货合约的基差报价',
    icon: 'trending-up'
  },
  Negotiable: {
    label: '商议',
    description: '价格面议，具体价格可沟通协商',
    icon: 'chat'
  }
}

// 交易类型显示配置
export const QUOTATION_TRADE_TYPE_CONFIG = {
  sell: {
    label: '出售',
    color: '#1890ff',
    bgColor: 'linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%)',
    borderColor: '#91d5ff'
  },
  buy: {
    label: '求购', 
    color: '#52c41a',
    bgColor: 'linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%)',
    borderColor: '#b7eb8f'
  }
}

// 分页响应类型
export interface IPageResult<T = any> {
  list: T[]
  total: number
  page: number
  pageSize: number
}