<route lang="jsonc" type="home">
{
  "layout": "tabbar",
  "style": {
    "navigationBarTitleText": "工作台"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import { useUserStore } from '@/store/user'
import { navigateToPage } from '@/utils'

defineOptions({
  name: 'WorkspaceIndex',
})

const userStore = useUserStore()

// 更新文案和图标名称
const sellerMenu = ref([
  {
    title: '合同管理',
    icon: 'file-word',
    path: '/pages/contract/setter-list',
    color: 'text-blue-500',
  },
  {
    title: '交易审核',
    icon: 'check-circle',
    path: '/pages/trade/setter-management',
    color: 'text-green-500',
  },
  {
    title: '报表', // Changed
    icon: 'chart-pie',
    path: '/pages/reports/setter-view',
    color: 'text-orange-500',
  },
])

const pricerMenu = ref([
  {
    title: '合同中心',
    icon: 'cart',
    path: '/pages/contract/pricer-list',
    color: 'text-purple-500',
  },
  {
    title: '发起点价',
    icon: 'add-circle',
    path: '/pages/trade/execute',
    color: 'text-red-500',
  },
  {
    title: '我的交易',
    icon: 'list',
    path: '/pages/trade/pricer-management',
    color: 'text-indigo-500',
  },
  {
    title: '报表', // Changed
    icon: 'chart-pie',
    path: '/pages/reports/pricer-view',
    color: 'text-amber-500',
  },
])

const commonMenu = ref([
  {
    title: '行情看板',
    icon: 'chart-bar',
    path: '/pages/dashboard/index',
    color: 'text-cyan-500',
  },
  {
    title: '消息通知',
    icon: 'notification',
    path: '/pages/notifications/index',
    color: 'text-amber-500',
  },
  {
    title: '账户中心',
    icon: 'setting',
    path: '/pages/profile/index',
    color: 'text-gray-500',
  },
])

function handleNavigate(path: string) {
  if (!path) return
  navigateToPage({ url: path })
}
</script>

<template>
  <view class="app-container">
    <view class="app-content">
      <!-- 用户欢迎语 -->
      <view class="user-welcome animate-fade-in">
        <h1 class="app-text-title">您好, {{ userStore.userInfo.nickName }}</h1>
        <p class="app-text-caption mt-1">欢迎使用大宗商品交易风险管理平台</p>
      </view>

      <!-- 报价工作台 -->
      <view class="app-card overflow-hidden animate-slide-up mb-4">
        <view class="app-card-header">
          <h2 class="app-text-subtitle">报价工作台</h2>
        </view>
        <view class="grid grid-cols-3 divide-y divide-x divide-gray-100">
          <view
            class="workspace-menu-item"
            @click="handleNavigate('/pages/quotes/marketplace')"
          >
            <wd-icon name="shop" custom-class="text-success" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">报价市场</text>
          </view>
          <view
            class="workspace-menu-item"
            @click="handleNavigate('/pages/quotes/my-list')"
          >
            <wd-icon name="read" custom-class="text-primary-600" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">我的报价</text>
          </view>
          <view
            class="workspace-menu-item"
            @click="handleNavigate('/pages/quotes/favorites')"
          >
            <wd-icon name="star" custom-class="text-warning" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">我的收藏</text>
          </view>
        </view>
      </view>

      <!-- 被点价方工作台 -->
      <view class="app-card overflow-hidden animate-slide-up mb-4" style="animation-delay: 0.1s">
        <view class="app-card-header">
          <h2 class="app-text-subtitle">被点价方工作台</h2>
        </view>
        <view class="grid grid-cols-4 divide-y divide-x divide-gray-100">
          <view
            v-for="item in sellerMenu"
            :key="item.title"
            class="workspace-menu-item"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 点价方工作台 -->
      <view class="app-card overflow-hidden animate-slide-up mb-4" style="animation-delay: 0.2s">
        <view class="app-card-header">
          <h2 class="app-text-subtitle">点价方工作台</h2>
        </view>
        <view class="grid grid-cols-4 divide-y divide-x divide-gray-100">
          <view
            v-for="item in pricerMenu"
            :key="item.title"
            class="workspace-menu-item"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 通用工具区 -->
      <view class="app-card overflow-hidden animate-slide-up" style="animation-delay: 0.3s">
        <view class="app-card-header">
          <h2 class="app-text-subtitle">通用工具</h2>
        </view>
        <view class="grid grid-cols-3 divide-y divide-x divide-gray-100">
          <view
            v-for="item in commonMenu"
            :key="item.title"
            class="workspace-menu-item"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="32rpx"></wd-icon>
            <text class="workspace-menu-text">{{ item.title }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 工作台特定样式

.user-welcome {
  margin-bottom: var(--app-spacing-lg);
  text-align: center;
  padding: var(--app-spacing-base) 0;
}

.workspace-menu-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-base);
  min-height: 120rpx;
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  /* #ifndef MP-WEIXIN */
  cursor: pointer;
  /* #endif */

  &:hover {
    background: var(--app-color-gray-50);
  }

  &:active {
    background: var(--app-color-gray-100);
    transform: scale(0.98);
  }
}

.workspace-menu-text {
  margin-top: var(--app-spacing-xs);
  font-size: var(--app-font-size-xs);
  color: var(--app-text-secondary);
  text-align: center;
  line-height: var(--app-line-height-tight);
}

// 所有平台都使用小屏样式，简化配置
.user-welcome {
  margin-bottom: var(--app-spacing-md);
  padding: var(--app-spacing-sm) 0;
}

.workspace-menu-item {
  min-height: 100rpx;
  padding: var(--app-spacing-sm);
}
</style>