// https://www.npmjs.com/package/@uni-helper/unocss-preset-uni
import { presetUni } from '@uni-helper/unocss-preset-uni'
import {
  defineConfig,
  presetAttributify,
  presetIcons,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'

export default defineConfig({
  presets: [
    presetUni({
      attributify: {
        // prefix: 'fg-', // 如果加前缀，则需要在代码里面使用 `fg-` 前缀，如：<div fg-border="1px solid #000"></div>
        prefixedOnly: true,
      },
    }),
    presetIcons({
      scale: 1.2,
      warn: true,
      extraProperties: {
        'display': 'inline-block',
        'vertical-align': 'middle',
      },
    }),
    // 支持css class属性化
    presetAttributify(),
  ],
  transformers: [
    // 启用指令功能：主要用于支持 @apply、@screen 和 theme() 等 CSS 指令
    transformerDirectives(),
    // 启用 () 分组功能
    // 支持css class组合，eg: `<div class="hover:(bg-gray-400 font-medium) font-(light mono)">测试 unocss</div>`
    transformerVariantGroup(),
  ],
  shortcuts: [
    // 布局相关
    {
      'center': 'flex justify-center items-center',
      'flex-col-center': 'flex flex-col justify-center items-center',
      'flex-between': 'flex justify-between items-center',
      'flex-around': 'flex justify-around items-center',
    },
    
    // 页面容器相关
    {
      'app-container': 'min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 p-safe',
      'page-wrapper': 'min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 px-4 py-2',
      'content-container': 'w-full max-w-screen-lg mx-auto',
    },
    
    // 卡片相关
    {
      'app-card': 'bg-white rounded-xl shadow-sm border border-gray-100',
      'app-card-hover': 'bg-white rounded-xl shadow-sm border border-gray-100 transition-all hover:shadow-md',
      'glass-card': 'bg-white/80 backdrop-blur-md rounded-xl shadow-sm border border-white/20',
    },
    
    // 按钮相关
    {
      'btn-primary': 'bg-gradient-to-r from-primary-500 to-primary-600 text-white font-medium rounded-lg px-4 py-2 transition-all hover:shadow-lg active:scale-95',
      'btn-secondary': 'bg-gray-100 text-gray-700 font-medium rounded-lg px-4 py-2 transition-all hover:bg-gray-200 active:scale-95',
      'btn-ghost': 'text-primary-600 font-medium rounded-lg px-4 py-2 transition-all hover:bg-primary-50 active:scale-95',
    },
    
    // 输入框相关
    {
      'input-base': 'w-full px-3 py-2 border border-gray-200 rounded-lg text-sm transition-all focus:border-primary-500 focus:ring-2 focus:ring-primary-100',
      'input-error': 'border-red-500 focus:border-red-500 focus:ring-red-100',
    },
    
    // 文本相关
    {
      'text-title': 'text-lg font-semibold text-gray-900',
      'text-subtitle': 'text-base font-medium text-gray-800',
      'text-body': 'text-sm text-gray-600',
      'text-caption': 'text-xs text-gray-500',
      'text-link': 'text-primary-600 hover:text-primary-700 transition-colors',
    },
  ],
  safelist: [
    // 确保常用类不被清除
    'bg-gradient-to-br',
    'from-slate-50',
    'to-blue-50',
    'backdrop-blur-md',
  ],
  rules: [
    [
      'p-safe',
      {
        padding:
          'env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left)',
      },
    ],
    ['pt-safe', { 'padding-top': 'env(safe-area-inset-top)' }],
    ['pb-safe', { 'padding-bottom': 'env(safe-area-inset-bottom)' }],
    ['pl-safe', { 'padding-left': 'env(safe-area-inset-left)' }],
    ['pr-safe', { 'padding-right': 'env(safe-area-inset-right)' }],
    
    // 自定义渐变规则
    [/^bg-gradient-primary-(.*)$/, ([, direction]) => {
      const directions: Record<string, string> = {
        'r': 'to right',
        'l': 'to left',
        'b': 'to bottom',
        't': 'to top',
        'br': 'to bottom right',
        'bl': 'to bottom left',
        'tr': 'to top right',
        'tl': 'to top left',
      }
      
      if (directions[direction]) {
        return {
          background: `linear-gradient(${directions[direction]}, var(--app-color-primary), var(--app-color-primary-dark))`
        }
      }
    }],
  ],
  theme: {
    colors: {
      // 主色系 - 基于品牌蓝色
      primary: {
        50: 'var(--app-color-primary-50, #eff6ff)',
        100: 'var(--app-color-primary-100, #dbeafe)',
        200: 'var(--app-color-primary-200, #bfdbfe)',
        300: 'var(--app-color-primary-300, #93c5fd)',
        400: 'var(--app-color-primary-400, #60a5fa)',
        500: 'var(--app-color-primary-500, #3b82f6)',
        600: 'var(--app-color-primary-600, #2563eb)',
        700: 'var(--app-color-primary-700, #1d4ed8)',
        800: 'var(--app-color-primary-800, #1e40af)',
        900: 'var(--app-color-primary-900, #1e3a8a)',
        DEFAULT: 'var(--app-color-primary, #3b82f6)',
      },
      
      // 辅助色系
      secondary: {
        50: 'var(--app-color-secondary-50, #f8fafc)',
        100: 'var(--app-color-secondary-100, #f1f5f9)',
        200: 'var(--app-color-secondary-200, #e2e8f0)',
        300: 'var(--app-color-secondary-300, #cbd5e1)',
        400: 'var(--app-color-secondary-400, #94a3b8)',
        500: 'var(--app-color-secondary-500, #64748b)',
        600: 'var(--app-color-secondary-600, #475569)',
        700: 'var(--app-color-secondary-700, #334155)',
        800: 'var(--app-color-secondary-800, #1e293b)',
        900: 'var(--app-color-secondary-900, #0f172a)',
        DEFAULT: 'var(--app-color-secondary, #64748b)',
      },
      
      // 功能色系
      success: {
        50: '#ecfdf5',
        100: '#d1fae5',
        200: '#a7f3d0',
        300: '#6ee7b7',
        400: '#34d399',
        500: '#10b981',
        600: '#059669',
        700: '#047857',
        800: '#065f46',
        900: '#064e3b',
        DEFAULT: '#10b981',
      },
      
      warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f',
        DEFAULT: '#f59e0b',
      },
      
      error: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d',
        DEFAULT: '#ef4444',
      },
      
      info: {
        50: '#eff6ff',
        100: '#dbeafe',
        200: '#bfdbfe',
        300: '#93c5fd',
        400: '#60a5fa',
        500: '#3b82f6',
        600: '#2563eb',
        700: '#1d4ed8',
        800: '#1e40af',
        900: '#1e3a8a',
        DEFAULT: '#3b82f6',
      },
    },
    
    fontSize: {
      // 扩展字体大小系统，使用 rpx 单位
      '4xs': ['16rpx', '22rpx'],
      '3xs': ['18rpx', '26rpx'],
      '2xs': ['20rpx', '28rpx'],
      'xs': ['24rpx', '32rpx'],
      'sm': ['28rpx', '36rpx'],
      'base': ['32rpx', '40rpx'],
      'lg': ['36rpx', '44rpx'],
      'xl': ['40rpx', '48rpx'],
      '2xl': ['48rpx', '56rpx'],
      '3xl': ['60rpx', '68rpx'],
      '4xl': ['72rpx', '80rpx'],
      '5xl': ['96rpx', '104rpx'],
    },
    
    spacing: {
      // 基于 8rpx 网格的间距系统
      '0.5': '4rpx',
      '1': '8rpx',
      '1.5': '12rpx',
      '2': '16rpx',
      '2.5': '20rpx',
      '3': '24rpx',
      '3.5': '28rpx',
      '4': '32rpx',
      '5': '40rpx',
      '6': '48rpx',
      '7': '56rpx',
      '8': '64rpx',
      '9': '72rpx',
      '10': '80rpx',
      '12': '96rpx',
      '16': '128rpx',
      '20': '160rpx',
      '24': '192rpx',
      '32': '256rpx',
    },
    
    borderRadius: {
      'none': '0',
      'sm': '8rpx',
      'DEFAULT': '12rpx',
      'md': '12rpx',
      'lg': '16rpx',
      'xl': '24rpx',
      '2xl': '32rpx',
      '3xl': '48rpx',
      'full': '9999rpx',
    },
    
    boxShadow: {
      'sm': '0 2rpx 8rpx rgba(0, 0, 0, 0.06)',
      'DEFAULT': '0 4rpx 16rpx rgba(0, 0, 0, 0.08)',
      'md': '0 8rpx 32rpx rgba(0, 0, 0, 0.12)',
      'lg': '0 16rpx 64rpx rgba(0, 0, 0, 0.16)',
      'xl': '0 32rpx 128rpx rgba(0, 0, 0, 0.2)',
      'none': 'none',
    },
    
    animation: {
      'fade-in': 'fadeIn 0.3s ease-in-out',
      'fade-out': 'fadeOut 0.3s ease-in-out',
      'slide-up': 'slideUp 0.3s ease-out',
      'slide-down': 'slideDown 0.3s ease-out',
      'scale': 'scale 0.2s ease-out',
    },
  },
})
