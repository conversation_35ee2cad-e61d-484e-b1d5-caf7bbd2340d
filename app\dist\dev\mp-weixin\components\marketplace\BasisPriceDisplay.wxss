/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.basis-display.data-v-80bbf42c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  min-width: 0;
  overflow: hidden;
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  position: relative;
}
.basis-display.flash-bg.data-v-80bbf42c {
  animation: flashBackground-80bbf42c 0.6s ease;
}
.basis-display .basis-content.data-v-80bbf42c,
.basis-display .final-content.data-v-80bbf42c {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  width: 100%;
  min-height: 80rpx;
  position: relative;
}
.basis-display .contract-name.data-v-80bbf42c {
  font-size: 26rpx;
  color: #595959;
  font-weight: 700;
  letter-spacing: 2rpx;
  text-transform: uppercase;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  text-align: center;
}
.basis-display .basis-value.data-v-80bbf42c {
  font-weight: 900;
  line-height: 1;
  text-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.15);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  text-align: center;
  transition: font-size 0.3s ease;
}
.basis-display .basis-value.data-v-80bbf42c:first-letter {
  font-size: 36rpx;
}
.basis-display .basis-value[data-positive=true].data-v-80bbf42c {
  color: #ff4d4f;
  background: linear-gradient(135deg, #ff4d4f 0%, #ff7875 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.basis-display .basis-value[data-negative=true].data-v-80bbf42c {
  color: #52c41a;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}
.basis-display .loading-text.data-v-80bbf42c {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.2;
}
.basis-display .error-text.data-v-80bbf42c {
  font-size: 22rpx;
  color: #ff4d4f;
  text-align: center;
  line-height: 1.2;
  opacity: 0.8;
}
.basis-display .final-price-label.data-v-80bbf42c {
  font-size: 22rpx;
  color: #666;
  text-align: center;
  margin-bottom: 4rpx;
  font-weight: 500;
}
.basis-display .final-price-value.data-v-80bbf42c {
  font-weight: 900;
  line-height: 1;
  text-align: center;
  color: #1890ff;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2rpx 4rpx rgba(24, 144, 255, 0.15);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  transition: font-size 0.3s ease;
}
.adaptive-price.data-v-80bbf42c {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.adaptive-price.data-v-80bbf42c:hover {
  position: relative;
  z-index: 100;
}
.adaptive-price.data-v-80bbf42c:hover::after {
  content: attr(title);
  position: absolute;
  top: -40rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  white-space: nowrap;
  z-index: 101;
  pointer-events: none;
  opacity: 0;
  animation: fadeIn-80bbf42c 0.3s ease forwards;
}
.price-switch-enter-active.data-v-80bbf42c,
.price-switch-leave-active.data-v-80bbf42c {
  transition: all 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.price-switch-enter-from.data-v-80bbf42c {
  opacity: 0.8;
  transform: translateX(120%) scale(0.95);
}
.price-switch-enter-to.data-v-80bbf42c {
  opacity: 1;
  transform: translateX(0) scale(1);
}
.price-switch-leave-from.data-v-80bbf42c {
  opacity: 1;
  transform: translateX(0) scale(1);
}
.price-switch-leave-to.data-v-80bbf42c {
  opacity: 0.8;
  transform: translateX(-120%) scale(0.95);
}
.price-switch-enter-active.data-v-80bbf42c {
  z-index: 2;
}
.price-switch-leave-active.data-v-80bbf42c {
  z-index: 1;
}
@keyframes flashBackground-80bbf42c {
0% {
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
}
15% {
    background-color: rgba(102, 126, 234, 0.1);
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
30% {
    background-color: rgba(102, 126, 234, 0.2);
    box-shadow: 0 0 0 8rpx rgba(102, 126, 234, 0.15);
}
45% {
    background-color: rgba(102, 126, 234, 0.25);
    box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.2);
}
60% {
    background-color: rgba(102, 126, 234, 0.15);
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
80% {
    background-color: rgba(102, 126, 234, 0.05);
    box-shadow: 0 0 0 2rpx rgba(102, 126, 234, 0.05);
}
100% {
    background-color: transparent;
    box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
}
}
@keyframes fadeIn-80bbf42c {
from {
    opacity: 0;
    transform: translateX(-50%) translateY(-5rpx);
}
to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
}
}
@media (max-width: 750rpx) {
.adaptive-price.basis-value.data-v-80bbf42c {
    font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
}
.adaptive-price.final-price-value.data-v-80bbf42c {
    font-size: clamp(22rpx, 4.5vw, 36rpx) !important;
}
}