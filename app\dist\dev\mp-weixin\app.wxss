/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
swiper,
scroll-view {
  flex: 1;
  height: 100%;
  overflow: hidden;
}
image {
  width: 100%;
  height: 100%;
  vertical-align: middle;
}
/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/**
 * 全局样式文件 - 统一的设计系统
 * 提供 CSS 变量、全局样式、标准化组件样式
 */
:root,
page {
  --app-color-primary-50: #eff6ff;
  --app-color-primary-100: #dbeafe;
  --app-color-primary-200: #bfdbfe;
  --app-color-primary-300: #93c5fd;
  --app-color-primary-400: #60a5fa;
  --app-color-primary-500: #3b82f6;
  --app-color-primary-600: #2563eb;
  --app-color-primary-700: #1d4ed8;
  --app-color-primary-800: #1e40af;
  --app-color-primary-900: #1e3a8a;
  --app-color-primary: #3b82f6;
  --app-color-primary-dark: #2563eb;
  --app-color-secondary-50: #f8fafc;
  --app-color-secondary-100: #f1f5f9;
  --app-color-secondary-200: #e2e8f0;
  --app-color-secondary-300: #cbd5e1;
  --app-color-secondary-400: #94a3b8;
  --app-color-secondary-500: #64748b;
  --app-color-secondary-600: #475569;
  --app-color-secondary-700: #334155;
  --app-color-secondary-800: #1e293b;
  --app-color-secondary-900: #0f172a;
  --app-color-secondary: #64748b;
  --app-color-success: #10b981;
  --app-color-success-light: #d1fae5;
  --app-color-warning: #f59e0b;
  --app-color-warning-light: #fef3c7;
  --app-color-error: #ef4444;
  --app-color-error-light: #fee2e2;
  --app-color-info: #3b82f6;
  --app-color-info-light: #dbeafe;
  --app-color-gray-50: #f9fafb;
  --app-color-gray-100: #f3f4f6;
  --app-color-gray-200: #e5e7eb;
  --app-color-gray-300: #d1d5db;
  --app-color-gray-400: #9ca3af;
  --app-color-gray-500: #6b7280;
  --app-color-gray-600: #374151;
  --app-color-gray-700: #1f2937;
  --app-color-gray-800: #111827;
  --app-color-gray-900: #030712;
  --app-text-primary: var(--app-color-gray-900);
  --app-text-secondary: var(--app-color-gray-600);
  --app-text-tertiary: var(--app-color-gray-500);
  --app-text-placeholder: var(--app-color-gray-400);
  --app-text-disabled: var(--app-color-gray-300);
  --app-text-inverse: #ffffff;
  --app-bg-primary: #ffffff;
  --app-bg-secondary: var(--app-color-gray-50);
  --app-bg-tertiary: var(--app-color-gray-100);
  --app-bg-overlay: rgba(0, 0, 0, 0.6);
  --app-bg-page: linear-gradient(135deg, var(--app-color-gray-50) 0%, #e0f2fe 100%);
  --app-border-primary: var(--app-color-gray-200);
  --app-border-secondary: var(--app-color-gray-100);
  --app-border-focus: var(--app-color-primary);
  --app-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  --app-shadow-base: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  --app-shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  --app-shadow-lg: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);
  --app-shadow-xl: 0 32rpx 128rpx rgba(0, 0, 0, 0.2);
  --app-radius-sm: 8rpx;
  --app-radius-base: 12rpx;
  --app-radius-md: 16rpx;
  --app-radius-lg: 20rpx;
  --app-radius-xl: 24rpx;
  --app-radius-2xl: 32rpx;
  --app-radius-full: 9999rpx;
  --app-spacing-xs: 8rpx;
  --app-spacing-sm: 16rpx;
  --app-spacing-base: 24rpx;
  --app-spacing-md: 32rpx;
  --app-spacing-lg: 48rpx;
  --app-spacing-xl: 64rpx;
  --app-font-size-xs: 24rpx;
  --app-font-size-sm: 28rpx;
  --app-font-size-base: 32rpx;
  --app-font-size-lg: 36rpx;
  --app-font-size-xl: 40rpx;
  --app-font-size-2xl: 48rpx;
  --app-font-size-3xl: 60rpx;
  --app-line-height-tight: 1.25;
  --app-line-height-normal: 1.5;
  --app-line-height-relaxed: 1.75;
  --app-font-weight-normal: 400;
  --app-font-weight-medium: 500;
  --app-font-weight-semibold: 600;
  --app-font-weight-bold: 700;
  --app-z-dropdown: 1000;
  --app-z-sticky: 1020;
  --app-z-fixed: 1030;
  --app-z-modal-backdrop: 1040;
  --app-z-modal: 1050;
  --app-z-popover: 1060;
  --app-z-tooltip: 1070;
  --app-duration-fast: 0.15s;
  --app-duration-base: 0.3s;
  --app-duration-slow: 0.5s;
  --app-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --app-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --app-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --wot-color-theme: var(--app-color-primary);
  --wot-color-success: var(--app-color-success);
  --wot-color-warning: var(--app-color-warning);
  --wot-color-danger: var(--app-color-error);
  --wot-color-info: var(--app-color-info);
}
/* 微信小程序兼容的样式重置 */
view, text, button, input, textarea, image, scroll-view, swiper, swiper-item,
navigator, audio, video, canvas, map, open-data, web-view, ad, official-account,
rich-text, picker, picker-view, picker-view-column, slider, switch, camera,
live-player, live-pusher, cover-view, cover-image, match-media, page-meta {
  box-sizing: border-box;
}
page {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
  font-size: var(--app-font-size-base);
  line-height: var(--app-line-height-normal);
  color: var(--app-text-primary);
  background: var(--app-bg-page);
  /* 平台兼容的字体平滑 */
}
.gradient-bg-primary {
  background: var(--app-bg-page);
}
.page-container {
  min-height: 100vh;
  background: var(--app-bg-page);
  padding: var(--app-spacing-xs);
}
.page-container.modern {
  padding: var(--app-spacing-xs);
}
.app-container {
  min-height: 100vh;
  background: var(--app-bg-page);
  /* 微信小程序样式 - 保持简洁 */
  padding: env(safe-area-inset-top) var(--app-spacing-xs) env(safe-area-inset-bottom);
  /* 非微信小程序样式 - 添加外框效果 */
}
.app-content {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: var(--app-spacing-base);
  /* 微信小程序中减少内边距 */
  padding: var(--app-spacing-sm);
}
.common-card {
  margin: 0 var(--app-spacing-sm) var(--app-spacing-sm);
  background: var(--app-bg-primary);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  border: 1rpx solid var(--app-border-secondary);
  overflow: hidden;
  /* 条件编译处理毛玻璃效果 */
  background: var(--app-bg-primary); /* 小程序中使用纯色背景 */
}
.common-card:last-child {
  margin-bottom: 0;
}
.app-card {
  background: var(--app-bg-primary);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  border: 1rpx solid var(--app-border-secondary);
  overflow: hidden;
  transition: all var(--app-duration-base) var(--app-ease-out);
}
.app-card:hover {
  box-shadow: var(--app-shadow-md);
  transform: translateY(-2rpx);
}
.app-card-header {
  padding: var(--app-spacing-base);
  border-bottom: 1rpx solid var(--app-border-secondary);
  background: var(--app-bg-secondary);
}
.app-card-body {
  padding: var(--app-spacing-base);
}
.app-card-footer {
  padding: var(--app-spacing-base);
  border-top: 1rpx solid var(--app-border-secondary);
  background: var(--app-bg-secondary);
}
.glass-card {
  background: rgba(255, 255, 255, 0.8);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
  background: rgba(255, 255, 255, 0.95);
}
.app-form-item {
  margin-bottom: var(--app-spacing-base);
}
.app-form-item:last-child {
  margin-bottom: 0;
}
.app-form-label {
  display: block;
  font-size: var(--app-font-size-sm);
  font-weight: var(--app-font-weight-medium);
  color: var(--app-text-primary);
  margin-bottom: var(--app-spacing-xs);
  line-height: var(--app-line-height-tight);
}
.app-form-control {
  position: relative;
}
.app-form-input {
  width: 100%;
  padding: var(--app-spacing-base) var(--app-spacing-sm);
  font-size: var(--app-font-size-base);
  color: var(--app-text-primary);
  background: var(--app-bg-primary);
  border: 2rpx solid var(--app-border-primary);
  border-radius: var(--app-radius-base);
  transition: all var(--app-duration-base) var(--app-ease-out);
}
.app-form-input::-moz-placeholder {
  color: var(--app-text-placeholder);
}
.app-form-input::placeholder {
  color: var(--app-text-placeholder);
}
.app-form-input:focus {
  border-color: var(--app-border-focus);
  box-shadow: 0 0 0 4rpx rgba(59, 130, 246, 0.1);
  outline: none;
}
.app-form-input.error {
  border-color: var(--app-color-error);
}
.app-form-input.error:focus {
  box-shadow: 0 0 0 4rpx rgba(239, 68, 68, 0.1);
}
.app-form-input:disabled {
  background: var(--app-bg-tertiary);
  color: var(--app-text-disabled);
}
.app-form-error {
  margin-top: var(--app-spacing-xs);
  font-size: var(--app-font-size-xs);
  color: var(--app-color-error);
  line-height: var(--app-line-height-tight);
}
.app-form-help {
  margin-top: var(--app-spacing-xs);
  font-size: var(--app-font-size-xs);
  color: var(--app-text-tertiary);
  line-height: var(--app-line-height-tight);
}
.app-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--app-spacing-sm) var(--app-spacing-base);
  font-size: var(--app-font-size-base);
  font-weight: var(--app-font-weight-medium);
  line-height: 1;
  border: 2rpx solid transparent;
  border-radius: var(--app-radius-base);
  transition: all var(--app-duration-base) var(--app-ease-out);
  white-space: nowrap;
}
.app-btn-small {
  padding: var(--app-spacing-xs) var(--app-spacing-sm);
  font-size: var(--app-font-size-sm);
}
.app-btn-large {
  padding: var(--app-spacing-base) var(--app-spacing-lg);
  font-size: var(--app-font-size-lg);
}
.app-btn-primary {
  background: linear-gradient(135deg, var(--app-color-primary), var(--app-color-primary-dark));
  color: var(--app-text-inverse);
}
.app-btn-primary:hover:not(:disabled) {
  box-shadow: var(--app-shadow-md);
  transform: translateY(-2rpx);
}
.app-btn-primary:active:not(:disabled) {
  transform: translateY(0);
}
.app-btn-secondary {
  background: var(--app-bg-secondary);
  color: var(--app-text-primary);
  border-color: var(--app-border-primary);
}
.app-btn-secondary:hover:not(:disabled) {
  background: var(--app-bg-tertiary);
  border-color: var(--app-color-primary);
}
.app-btn-ghost {
  background: transparent;
  color: var(--app-color-primary);
}
.app-btn-ghost:hover:not(:disabled) {
  background: var(--app-color-primary-50);
}
.app-btn-text {
  background: transparent;
  color: var(--app-color-primary);
  padding: var(--app-spacing-xs);
}
.app-btn-text:hover:not(:disabled) {
  background: var(--app-color-primary-50);
}
.app-btn:disabled {
  opacity: 0.5;
  transform: none !important;
}
.app-btn-block {
  width: 100%;
}
.app-text-title {
  font-size: var(--app-font-size-xl);
  font-weight: var(--app-font-weight-semibold);
  color: var(--app-text-primary);
  line-height: var(--app-line-height-tight);
}
.app-text-subtitle {
  font-size: var(--app-font-size-lg);
  font-weight: var(--app-font-weight-medium);
  color: var(--app-text-primary);
  line-height: var(--app-line-height-normal);
}
.app-text-body {
  font-size: var(--app-font-size-base);
  color: var(--app-text-secondary);
  line-height: var(--app-line-height-normal);
}
.app-text-caption {
  font-size: var(--app-font-size-sm);
  color: var(--app-text-tertiary);
  line-height: var(--app-line-height-normal);
}
.app-text-link {
  color: var(--app-color-primary);
  text-decoration: none;
  transition: color var(--app-duration-base) var(--app-ease-out);
}
.app-text-link:hover {
  color: var(--app-color-primary-700);
}
.app-text-link:active {
  color: var(--app-color-primary-800);
}
.m-0 {
  margin: 0 !important;
}
.m-1 {
  margin: var(--app-spacing-xs) !important;
}
.m-2 {
  margin: var(--app-spacing-sm) !important;
}
.m-3 {
  margin: var(--app-spacing-base) !important;
}
.m-4 {
  margin: var(--app-spacing-md) !important;
}
.p-0 {
  padding: 0 !important;
}
.p-1 {
  padding: var(--app-spacing-xs) !important;
}
.p-2 {
  padding: var(--app-spacing-sm) !important;
}
.p-3 {
  padding: var(--app-spacing-base) !important;
}
.p-4 {
  padding: var(--app-spacing-md) !important;
}
.hidden {
  display: none !important;
}
.visible {
  display: block !important;
}
.text-left {
  text-align: left !important;
}
.text-center {
  text-align: center !important;
}
.text-right {
  text-align: right !important;
}
.rounded-none {
  border-radius: 0 !important;
}
.rounded-sm {
  border-radius: var(--app-radius-sm) !important;
}
.rounded {
  border-radius: var(--app-radius-base) !important;
}
.rounded-lg {
  border-radius: var(--app-radius-lg) !important;
}
.rounded-full {
  border-radius: var(--app-radius-full) !important;
}
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
    transform: translateY(0);
  }
  to {
    opacity: 0;
    transform: translateY(-10rpx);
  }
}
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes scale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}
.animate-fade-in {
  animation: fadeIn var(--app-duration-base) var(--app-ease-out);
}
.animate-fade-out {
  animation: fadeOut var(--app-duration-base) var(--app-ease-out);
}
.animate-slide-up {
  animation: slideUp var(--app-duration-base) var(--app-ease-out);
}
.animate-slide-down {
  animation: slideDown var(--app-duration-base) var(--app-ease-out);
}
.animate-scale {
  animation: scale var(--app-duration-base) var(--app-ease-out);
}
.test {
  margin-top:32rpx;margin-left:32rpx;
  padding-top: 4px;
  color: red;
}
 page,::before,::after{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}::backdrop{--un-rotate:0;--un-rotate-x:0;--un-rotate-y:0;--un-rotate-z:0;--un-scale-x:1;--un-scale-y:1;--un-scale-z:1;--un-skew-x:0;--un-skew-y:0;--un-translate-x:0;--un-translate-y:0;--un-translate-z:0;--un-pan-x: ;--un-pan-y: ;--un-pinch-zoom: ;--un-scroll-snap-strictness:proximity;--un-ordinal: ;--un-slashed-zero: ;--un-numeric-figure: ;--un-numeric-spacing: ;--un-numeric-fraction: ;--un-border-spacing-x:0;--un-border-spacing-y:0;--un-ring-offset-shadow:0 0 rgb(0 0 0 / 0);--un-ring-shadow:0 0 rgb(0 0 0 / 0);--un-shadow-inset: ;--un-shadow:0 0 rgb(0 0 0 / 0);--un-ring-inset: ;--un-ring-offset-width:0px;--un-ring-offset-color:#fff;--un-ring-width:0px;--un-ring-color:rgb(147 197 253 / 0.5);--un-blur: ;--un-brightness: ;--un-contrast: ;--un-drop-shadow: ;--un-grayscale: ;--un-hue-rotate: ;--un-invert: ;--un-saturate: ;--un-sepia: ;--un-backdrop-blur: ;--un-backdrop-brightness: ;--un-backdrop-contrast: ;--un-backdrop-grayscale: ;--un-backdrop-hue-rotate: ;--un-backdrop-invert: ;--un-backdrop-opacity: ;--un-backdrop-saturate: ;--un-backdrop-sepia: ;}.i-carbon-code{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='inline-block' vertical-align='middle' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' d='m31 16l-7 7l-1.41-1.41L28.17 16l-5.58-5.59L24 9zM1 16l7-7l1.41 1.41L3.83 16l5.58 5.59L8 23zm11.42 9.484L17.64 6l1.932.517L14.352 26z'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:inline-block;vertical-align:middle;width:1.2em;height:1.2em;}.i-carbon-logo-wechat{--un-icon:url("data:image/svg+xml;utf8,%3Csvg viewBox='0 0 32 32' display='inline-block' vertical-align='middle' width='1.2em' height='1.2em' xmlns='http://www.w3.org/2000/svg' %3E%3Cpath fill='currentColor' fill-rule='evenodd' d='M27.086 24.78A6.62 6.62 0 0 0 30 19.465c0-3.88-3.776-7.027-8.434-7.027s-8.434 3.147-8.434 7.027s3.777 7.028 8.434 7.028a10 10 0 0 0 2.754-.385l.247-.037a.9.9 0 0 1 .448.13l1.847 1.066l.162.053a.28.28 0 0 0 .281-.282l-.045-.205l-.38-1.417l-.03-.18a.56.56 0 0 1 .236-.458M12.12 4.68C6.53 4.68 2 8.455 2 13.114a7.94 7.94 0 0 0 3.497 6.374a.67.67 0 0 1 .283.55l-.035.215l-.456 1.701l-.055.246a.34.34 0 0 0 .337.338l.196-.063l2.216-1.28a1.06 1.06 0 0 1 .536-.155l.298.044a12 12 0 0 0 3.304.464l.555-.014a6.5 6.5 0 0 1-.34-2.067c0-4.247 4.133-7.691 9.23-7.691l.55.014c-.762-4.029-4.947-7.11-9.995-7.11m6.633 13.663a1.125 1.125 0 1 1 1.125-1.125a1.124 1.124 0 0 1-1.125 1.125m5.624 0a1.125 1.125 0 1 1 1.123-1.125a1.125 1.125 0 0 1-1.123 1.125m-15.631-6.58a1.35 1.35 0 1 1 1.35-1.348a1.35 1.35 0 0 1-1.35 1.349m6.747 0a1.35 1.35 0 1 1 1.35-1.348a1.35 1.35 0 0 1-1.35 1.349'/%3E%3C/svg%3E");-webkit-mask:var(--un-icon) no-repeat;mask:var(--un-icon) no-repeat;-webkit-mask-size:100% 100%;mask-size:100% 100%;background-color:currentColor;color:inherit;display:inline-block;vertical-align:middle;width:1.2em;height:1.2em;}.app-container{min-height:100vh;--un-gradient-from-position:0%;--un-gradient-from:rgba(248, 250, 252, var(--un-from-opacity, 1)) var(--un-gradient-from-position);--un-gradient-to-position:100%;--un-gradient-to:rgba(248, 250, 252, 0) var(--un-gradient-to-position);--un-gradient-stops:var(--un-gradient-from), var(--un-gradient-to);--un-gradient-to:rgba(239, 246, 255, var(--un-to-opacity, 1)) var(--un-gradient-to-position);--un-gradient-shape:to bottom right in oklch;--un-gradient:var(--un-gradient-shape), var(--un-gradient-stops);background-image:linear-gradient(var(--un-gradient));padding:env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);}.input-base{width:100%;border-width:1px;--un-border-opacity:1;border-color:rgba(229, 231, 235, var(--un-border-opacity));border-radius:16rpx;padding-left:24rpx;padding-right:24rpx;padding-top:16rpx;padding-bottom:16rpx;font-size:28rpx;line-height:36rpx;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.center{display:flex;align-items:center;justify-content:center;}.flex-between{display:flex;align-items:center;justify-content:space-between;}.flex-col-center{display:flex;flex-direction:column;align-items:center;justify-content:center;}.btn-ghost:active{--un-scale-x:0.95;--un-scale-y:0.95;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.btn-primary:active{--un-scale-x:0.95;--un-scale-y:0.95;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.btn-secondary:active{--un-scale-x:0.95;--un-scale-y:0.95;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.app-card{border-width:1px;--un-border-opacity:1;border-color:rgba(243, 244, 246, var(--un-border-opacity));border-radius:24rpx;--un-bg-opacity:1;background-color:rgba(255, 255, 255, var(--un-bg-opacity)) /* #fff */;--un-shadow:0 2rpx 8rpx var(--un-shadow-color, rgb(0,, 0,, 0,, 0.06));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.glass-card{border-width:1px;border-color:rgba(255, 255, 255, 0.2);border-radius:24rpx;background-color:rgba(255, 255, 255, 0.8) /* #fff */;--un-shadow:0 2rpx 8rpx var(--un-shadow-color, rgb(0,, 0,, 0,, 0.06));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);--un-backdrop-blur:blur(12px);-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}.input-base:focus{border-color:var(--app-color-primary-500, #3b82f6);--un-ring-width:2px;--un-ring-offset-shadow:var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color);--un-ring-shadow:var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color);box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);--un-ring-color:var(--app-color-primary-100, #dbeafe) /* var(--app-color-primary-100, #dbeafe) */;}.btn-ghost{border-radius:16rpx;padding-left:32rpx;padding-right:32rpx;padding-top:16rpx;padding-bottom:16rpx;color:var(--app-color-primary-600, #2563eb) /* var(--app-color-primary-600, #2563eb) */;font-weight:500;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.btn-primary{border-radius:16rpx;--un-gradient-from-position:0%;--un-gradient-from:var(--app-color-primary-500, #3b82f6) var(--un-gradient-from-position);--un-gradient-to-position:100%;--un-gradient-to:rgba(255, 255, 255, 0) var(--un-gradient-to-position);--un-gradient-stops:var(--un-gradient-from), var(--un-gradient-to);--un-gradient-to:var(--app-color-primary-600, #2563eb) var(--un-gradient-to-position);--un-gradient-shape:to right in oklch;--un-gradient:var(--un-gradient-shape), var(--un-gradient-stops);background-image:linear-gradient(var(--un-gradient));padding-left:32rpx;padding-right:32rpx;padding-top:16rpx;padding-bottom:16rpx;--un-text-opacity:1;color:rgba(255, 255, 255, var(--un-text-opacity)) /* #fff */;font-weight:500;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.btn-secondary{border-radius:16rpx;--un-bg-opacity:1;background-color:rgba(243, 244, 246, var(--un-bg-opacity)) /* #f3f4f6 */;padding-left:32rpx;padding-right:32rpx;padding-top:16rpx;padding-bottom:16rpx;--un-text-opacity:1;color:rgba(55, 65, 81, var(--un-text-opacity)) /* #374151 */;font-weight:500;transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.btn-ghost:hover{background-color:var(--app-color-primary-50, #eff6ff) /* var(--app-color-primary-50, #eff6ff) */;}.btn-secondary:hover{--un-bg-opacity:1;background-color:rgba(229, 231, 235, var(--un-bg-opacity)) /* #e5e7eb */;}.text-body{font-size:28rpx;line-height:36rpx;--un-text-opacity:1;color:rgba(75, 85, 99, var(--un-text-opacity)) /* #4b5563 */;}.text-caption{font-size:24rpx;line-height:32rpx;--un-text-opacity:1;color:rgba(107, 114, 128, var(--un-text-opacity)) /* #6b7280 */;}.text-title{font-size:36rpx;line-height:44rpx;--un-text-opacity:1;color:rgba(17, 24, 39, var(--un-text-opacity)) /* #111827 */;font-weight:600;}.text-link{color:var(--app-color-primary-600, #2563eb) /* var(--app-color-primary-600, #2563eb) */;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.text-link:hover{color:var(--app-color-primary-700, #1d4ed8) /* var(--app-color-primary-700, #1d4ed8) */;}.btn-primary:hover{--un-shadow:0 16rpx 64rpx var(--un-shadow-color, rgb(0,, 0,, 0,, 0.16));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}[display_a_none=""]{display:none="";}[opacity_a_1=""]{opacity:1="";}[update_a_modelValue=""]{update:modelValue="";}.visible{visibility:visible;}.absolute{position:absolute;}.fixed{position:fixed;}.relative{position:relative;}.sticky{position:sticky;}.static{position:static;}.bottom-0{bottom:0;}.left-0{left:0;}.right-0{right:0;}.right-1{right:8rpx;}.top-1{top:8rpx;}.grid{display:grid;}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr));}.grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr));}.grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr));}.m-8{margin:64rpx;}.mx-auto{margin-left:auto;margin-right:auto;}.my-6{margin-top:48rpx;margin-bottom:48rpx;}.mb-1{margin-bottom:8rpx;}.mb-2{margin-bottom:16rpx;}.mb-3{margin-bottom:24rpx;}.mb-4{margin-bottom:32rpx;}.mb-6{margin-bottom:48rpx;}.mb-8{margin-bottom:64rpx;}.ml-1{margin-left:8rpx;}.ml-2{margin-left:16rpx;}.ml-3{margin-left:24rpx;}.mr-1{margin-right:8rpx;}.mr-2{margin-right:16rpx;}.mr-3{margin-right:24rpx;}.mr-8{margin-right:64rpx;}.ms{margin-inline-start:32rpx;}.mt-1{margin-top:8rpx;}.mt-2{margin-top:16rpx;}.mt-3{margin-top:24rpx;}.mt-4{margin-top:32rpx;}.inline{display:inline;}.block{display:block;}.inline-block{display:inline-block;}.list-item{display:list-item;}.hidden{display:none;}.h-16{height:128rpx;}.h-2,.h2{height:16rpx;}.h-4,.h4{height:32rpx;}.h-40rpx,.h5{height:40rpx;}.h-8{height:64rpx;}.h-full{height:100%;}.h1{height:8rpx;}.h3{height:24rpx;}.h6{height:48rpx;}.min-h-_a_80vh_a_{min-height:80vh;}.min-w-_a_30_a__a_{min-width:30%;}.w-12{width:96rpx;}.w-16{width:128rpx;}.w-2{width:16rpx;}.w-4{width:32rpx;}.w-40rpx{width:40rpx;}.w-8{width:64rpx;}.w-full{width:100%;}.wxs{width:640rpx;}.flex{display:flex;}.flex-1{flex:1 1 0%;}.flex-shrink{flex-shrink:1;}.flex-col{flex-direction:column;}.flex-wrap{flex-wrap:wrap;}.table{display:table;}.table-cell{display:table-cell;}.table-row{display:table-row;}.table-row-group{display:table-row-group;}.border-collapse{border-collapse:collapse;}.hover_a_scale-105:hover{--un-scale-x:1.05;--un-scale-y:1.05;transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}.transform{transform:translateX(var(--un-translate-x)) translateY(var(--un-translate-y)) translateZ(var(--un-translate-z)) rotate(var(--un-rotate)) rotateX(var(--un-rotate-x)) rotateY(var(--un-rotate-y)) rotateZ(var(--un-rotate-z)) skewX(var(--un-skew-x)) skewY(var(--un-skew-y)) scaleX(var(--un-scale-x)) scaleY(var(--un-scale-y)) scaleZ(var(--un-scale-z));}@keyframes fade-in{from{opacity:0}to{opacity:1}}.animate-fade-in{animation:fade-in 1s linear 1;}@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}.animate-spin{animation:spin 1s linear infinite;}.cursor-pointer{cursor:pointer;}.resize{resize:both;}.items-start{align-items:flex-start;}.items-center{align-items:center;}.items-stretch{align-items:stretch;}.justify-end{justify-content:flex-end;}.justify-center{justify-content:center;}.justify-between{justify-content:space-between;}.gap-1{gap:8rpx;}.gap-2{gap:16rpx;}.gap-3{gap:24rpx;}.gap-4{gap:32rpx;}.space-x-1>view+view,.space-x-1>view+button,.space-x-1>view+text,.space-x-1>view+image,.space-x-1>button+view,.space-x-1>button+button,.space-x-1>button+text,.space-x-1>button+image,.space-x-1>text+view,.space-x-1>text+button,.space-x-1>text+text,.space-x-1>text+image,.space-x-1>image+view,.space-x-1>image+button,.space-x-1>image+text,.space-x-1>image+image{--un-space-x-reverse:0;margin-left:calc(8rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(8rpx * var(--un-space-x-reverse));}.space-x-2>view+view,.space-x-2>view+button,.space-x-2>view+text,.space-x-2>view+image,.space-x-2>button+view,.space-x-2>button+button,.space-x-2>button+text,.space-x-2>button+image,.space-x-2>text+view,.space-x-2>text+button,.space-x-2>text+text,.space-x-2>text+image,.space-x-2>image+view,.space-x-2>image+button,.space-x-2>image+text,.space-x-2>image+image{--un-space-x-reverse:0;margin-left:calc(16rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(16rpx * var(--un-space-x-reverse));}.space-x-3>view+view,.space-x-3>view+button,.space-x-3>view+text,.space-x-3>view+image,.space-x-3>button+view,.space-x-3>button+button,.space-x-3>button+text,.space-x-3>button+image,.space-x-3>text+view,.space-x-3>text+button,.space-x-3>text+text,.space-x-3>text+image,.space-x-3>image+view,.space-x-3>image+button,.space-x-3>image+text,.space-x-3>image+image{--un-space-x-reverse:0;margin-left:calc(24rpx * calc(1 - var(--un-space-x-reverse)));margin-right:calc(24rpx * var(--un-space-x-reverse));}.space-y-1>view+view,.space-y-1>view+button,.space-y-1>view+text,.space-y-1>view+image,.space-y-1>button+view,.space-y-1>button+button,.space-y-1>button+text,.space-y-1>button+image,.space-y-1>text+view,.space-y-1>text+button,.space-y-1>text+text,.space-y-1>text+image,.space-y-1>image+view,.space-y-1>image+button,.space-y-1>image+text,.space-y-1>image+image{--un-space-y-reverse:0;margin-top:calc(8rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(8rpx * var(--un-space-y-reverse));}.space-y-2>view+view,.space-y-2>view+button,.space-y-2>view+text,.space-y-2>view+image,.space-y-2>button+view,.space-y-2>button+button,.space-y-2>button+text,.space-y-2>button+image,.space-y-2>text+view,.space-y-2>text+button,.space-y-2>text+text,.space-y-2>text+image,.space-y-2>image+view,.space-y-2>image+button,.space-y-2>image+text,.space-y-2>image+image{--un-space-y-reverse:0;margin-top:calc(16rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(16rpx * var(--un-space-y-reverse));}.space-y-3>view+view,.space-y-3>view+button,.space-y-3>view+text,.space-y-3>view+image,.space-y-3>button+view,.space-y-3>button+button,.space-y-3>button+text,.space-y-3>button+image,.space-y-3>text+view,.space-y-3>text+button,.space-y-3>text+text,.space-y-3>text+image,.space-y-3>image+view,.space-y-3>image+button,.space-y-3>image+text,.space-y-3>image+image{--un-space-y-reverse:0;margin-top:calc(24rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(24rpx * var(--un-space-y-reverse));}.space-y-4>view+view,.space-y-4>view+button,.space-y-4>view+text,.space-y-4>view+image,.space-y-4>button+view,.space-y-4>button+button,.space-y-4>button+text,.space-y-4>button+image,.space-y-4>text+view,.space-y-4>text+button,.space-y-4>text+text,.space-y-4>text+image,.space-y-4>image+view,.space-y-4>image+button,.space-y-4>image+text,.space-y-4>image+image{--un-space-y-reverse:0;margin-top:calc(32rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(32rpx * var(--un-space-y-reverse));}.space-y-6>view+view,.space-y-6>view+button,.space-y-6>view+text,.space-y-6>view+image,.space-y-6>button+view,.space-y-6>button+button,.space-y-6>button+text,.space-y-6>button+image,.space-y-6>text+view,.space-y-6>text+button,.space-y-6>text+text,.space-y-6>text+image,.space-y-6>image+view,.space-y-6>image+button,.space-y-6>image+text,.space-y-6>image+image{--un-space-y-reverse:0;margin-top:calc(48rpx * calc(1 - var(--un-space-y-reverse)));margin-bottom:calc(48rpx * var(--un-space-y-reverse));}.divide-x>view+view,.divide-x>view+button,.divide-x>view+text,.divide-x>view+image,.divide-x>button+view,.divide-x>button+button,.divide-x>button+text,.divide-x>button+image,.divide-x>text+view,.divide-x>text+button,.divide-x>text+text,.divide-x>text+image,.divide-x>image+view,.divide-x>image+button,.divide-x>image+text,.divide-x>image+image{--un-divide-x-reverse:0;border-left-width:calc(1px * calc(1 - var(--un-divide-x-reverse)));border-right-width:calc(1px * var(--un-divide-x-reverse));}.divide-y>view+view,.divide-y>view+button,.divide-y>view+text,.divide-y>view+image,.divide-y>button+view,.divide-y>button+button,.divide-y>button+text,.divide-y>button+image,.divide-y>text+view,.divide-y>text+button,.divide-y>text+text,.divide-y>text+image,.divide-y>image+view,.divide-y>image+button,.divide-y>image+text,.divide-y>image+image{--un-divide-y-reverse:0;border-top-width:calc(1px * calc(1 - var(--un-divide-y-reverse)));border-bottom-width:calc(1px * var(--un-divide-y-reverse));}.divide-gray-100>view+view,.divide-gray-100>view+button,.divide-gray-100>view+text,.divide-gray-100>view+image,.divide-gray-100>button+view,.divide-gray-100>button+button,.divide-gray-100>button+text,.divide-gray-100>button+image,.divide-gray-100>text+view,.divide-gray-100>text+button,.divide-gray-100>text+text,.divide-gray-100>text+image,.divide-gray-100>image+view,.divide-gray-100>image+button,.divide-gray-100>image+text,.divide-gray-100>image+image{--un-divide-opacity:1;border-color:rgba(243, 244, 246, var(--un-divide-opacity)) /* #f3f4f6 */;}.overflow-hidden{overflow:hidden;}.break-all{word-break:break-all;}.b,.border{border-width:1px;}.border-2{border-width:2px;}.border-t{border-top-width:1px;}.border-blue-500{--un-border-opacity:1;border-color:rgba(59, 130, 246, var(--un-border-opacity));}.border-gray-100{--un-border-opacity:1;border-color:rgba(243, 244, 246, var(--un-border-opacity));}.border-t-transparent{border-top-color:transparent;}.rounded-full{border-radius:9999rpx;}.rounded-lg{border-radius:16rpx;}.bg-blue-50{--un-bg-opacity:1;background-color:rgba(239, 246, 255, var(--un-bg-opacity)) /* #eff6ff */;}.bg-blue-500{--un-bg-opacity:1;background-color:rgba(59, 130, 246, var(--un-bg-opacity)) /* #3b82f6 */;}.bg-error,.bg-error-500,.bg-red-500{--un-bg-opacity:1;background-color:rgba(239, 68, 68, var(--un-bg-opacity)) /* #ef4444 */;}.bg-gray-50{--un-bg-opacity:1;background-color:rgba(249, 250, 251, var(--un-bg-opacity)) /* #f9fafb */;}.bg-green-50{--un-bg-opacity:1;background-color:rgba(240, 253, 244, var(--un-bg-opacity)) /* #f0fdf4 */;}.bg-green-500{--un-bg-opacity:1;background-color:rgba(34, 197, 94, var(--un-bg-opacity)) /* #22c55e */;}.bg-orange-50{--un-bg-opacity:1;background-color:rgba(255, 247, 237, var(--un-bg-opacity)) /* #fff7ed */;}.bg-orange-500{--un-bg-opacity:1;background-color:rgba(249, 115, 22, var(--un-bg-opacity)) /* #f97316 */;}.bg-primary-50{background-color:var(--app-color-primary-50, #eff6ff) /* var(--app-color-primary-50, #eff6ff) */;}.bg-primary-500{background-color:var(--app-color-primary-500, #3b82f6) /* var(--app-color-primary-500, #3b82f6) */;}.bg-purple-50{--un-bg-opacity:1;background-color:rgba(250, 245, 255, var(--un-bg-opacity)) /* #faf5ff */;}.bg-purple-500{--un-bg-opacity:1;background-color:rgba(168, 85, 247, var(--un-bg-opacity)) /* #a855f7 */;}.bg-red-50{--un-bg-opacity:1;background-color:rgba(254, 242, 242, var(--un-bg-opacity)) /* #fef2f2 */;}.bg-success,.bg-success-500{--un-bg-opacity:1;background-color:rgba(16, 185, 129, var(--un-bg-opacity)) /* #10b981 */;}.bg-warning{--un-bg-opacity:1;background-color:rgba(245, 158, 11, var(--un-bg-opacity)) /* #f59e0b */;}.bg-white{--un-bg-opacity:1;background-color:rgba(255, 255, 255, var(--un-bg-opacity)) /* #fff */;}.bg-white_a_95{background-color:rgba(255, 255, 255, 0.95) /* #fff */;}.from-primary-500{--un-gradient-from-position:0%;--un-gradient-from:var(--app-color-primary-500, #3b82f6) var(--un-gradient-from-position);--un-gradient-to-position:100%;--un-gradient-to:rgba(255, 255, 255, 0) var(--un-gradient-to-position);--un-gradient-stops:var(--un-gradient-from), var(--un-gradient-to);}.from-purple-500{--un-gradient-from-position:0%;--un-gradient-from:rgba(168, 85, 247, var(--un-from-opacity, 1)) var(--un-gradient-from-position);--un-gradient-to-position:100%;--un-gradient-to:rgba(168, 85, 247, 0) var(--un-gradient-to-position);--un-gradient-stops:var(--un-gradient-from), var(--un-gradient-to);}.from-slate-50{--un-gradient-from-position:0%;--un-gradient-from:rgba(248, 250, 252, var(--un-from-opacity, 1)) var(--un-gradient-from-position);--un-gradient-to-position:100%;--un-gradient-to:rgba(248, 250, 252, 0) var(--un-gradient-to-position);--un-gradient-stops:var(--un-gradient-from), var(--un-gradient-to);}.to-blue-50{--un-gradient-to-position:100%;--un-gradient-to:rgba(239, 246, 255, var(--un-to-opacity, 1)) var(--un-gradient-to-position);}.to-pink-500{--un-gradient-to-position:100%;--un-gradient-to:rgba(236, 72, 153, var(--un-to-opacity, 1)) var(--un-gradient-to-position);}.to-primary-600{--un-gradient-to-position:100%;--un-gradient-to:var(--app-color-primary-600, #2563eb) var(--un-gradient-to-position);}.bg-gradient-to-br{--un-gradient-shape:to bottom right in oklch;--un-gradient:var(--un-gradient-shape), var(--un-gradient-stops);background-image:linear-gradient(var(--un-gradient));}.bg-gradient-to-r{--un-gradient-shape:to right in oklch;--un-gradient:var(--un-gradient-shape), var(--un-gradient-stops);background-image:linear-gradient(var(--un-gradient));}.p-3{padding:24rpx;}.p-4{padding:32rpx;}.p1{padding:8rpx;}.px{padding-left:32rpx;padding-right:32rpx;}.px-2{padding-left:16rpx;padding-right:16rpx;}.px-3{padding-left:24rpx;padding-right:24rpx;}.py-1{padding-top:8rpx;padding-bottom:8rpx;}.py-12{padding-top:96rpx;padding-bottom:96rpx;}.py-20{padding-top:160rpx;padding-bottom:160rpx;}.py-4{padding-top:32rpx;padding-bottom:32rpx;}.py-6{padding-top:48rpx;padding-bottom:48rpx;}.py-8{padding-top:64rpx;padding-bottom:64rpx;}.pb-_a_180rpx_a_{padding-bottom:180rpx;}.pr-4{padding-right:32rpx;}.pr-8{padding-right:64rpx;}.text-center{text-align:center;}.text-right{text-align:right;}.text-2xl{font-size:48rpx;line-height:56rpx;}.text-4xl{font-size:72rpx;line-height:80rpx;}.text-base{font-size:32rpx;line-height:40rpx;}.text-lg{font-size:36rpx;line-height:44rpx;}.text-sm{font-size:28rpx;line-height:36rpx;}.text-xl{font-size:40rpx;line-height:48rpx;}.text-xs{font-size:24rpx;line-height:32rpx;}.text-amber-500,.text-warning{--un-text-opacity:1;color:rgba(245, 158, 11, var(--un-text-opacity)) /* #f59e0b */;}.text-blue-500{--un-text-opacity:1;color:rgba(59, 130, 246, var(--un-text-opacity)) /* #3b82f6 */;}.text-blue-600{--un-text-opacity:1;color:rgba(37, 99, 235, var(--un-text-opacity)) /* #2563eb */;}.text-cyan-500{--un-text-opacity:1;color:rgba(6, 182, 212, var(--un-text-opacity)) /* #06b6d4 */;}.text-error,.text-red-500{--un-text-opacity:1;color:rgba(239, 68, 68, var(--un-text-opacity)) /* #ef4444 */;}.text-gray-300{--un-text-opacity:1;color:rgba(209, 213, 219, var(--un-text-opacity)) /* #d1d5db */;}.text-gray-400{--un-text-opacity:1;color:rgba(156, 163, 175, var(--un-text-opacity)) /* #9ca3af */;}.text-gray-500{--un-text-opacity:1;color:rgba(107, 114, 128, var(--un-text-opacity)) /* #6b7280 */;}.text-gray-600{--un-text-opacity:1;color:rgba(75, 85, 99, var(--un-text-opacity)) /* #4b5563 */;}.text-gray-700{--un-text-opacity:1;color:rgba(55, 65, 81, var(--un-text-opacity)) /* #374151 */;}.text-gray-800{--un-text-opacity:1;color:rgba(31, 41, 55, var(--un-text-opacity)) /* #1f2937 */;}.text-green-500{--un-text-opacity:1;color:rgba(34, 197, 94, var(--un-text-opacity)) /* #22c55e */;}.text-green-600{--un-text-opacity:1;color:rgba(22, 163, 74, var(--un-text-opacity)) /* #16a34a */;}.text-indigo-500{--un-text-opacity:1;color:rgba(99, 102, 241, var(--un-text-opacity)) /* #6366f1 */;}.text-orange-500{--un-text-opacity:1;color:rgba(249, 115, 22, var(--un-text-opacity)) /* #f97316 */;}.text-primary{color:var(--app-color-primary, #3b82f6) /* var(--app-color-primary, #3b82f6) */;}.text-primary-600{color:var(--app-color-primary-600, #2563eb) /* var(--app-color-primary-600, #2563eb) */;}.text-purple-500{--un-text-opacity:1;color:rgba(168, 85, 247, var(--un-text-opacity)) /* #a855f7 */;}.text-red-600{--un-text-opacity:1;color:rgba(220, 38, 38, var(--un-text-opacity)) /* #dc2626 */;}.text-success{--un-text-opacity:1;color:rgba(16, 185, 129, var(--un-text-opacity)) /* #10b981 */;}.text-white{--un-text-opacity:1;color:rgba(255, 255, 255, var(--un-text-opacity)) /* #fff */;}.font-bold{font-weight:700;}.font-medium{font-weight:500;}.font-semibold{font-weight:600;}.leading-relaxed{line-height:1.625;}.leading-tight{line-height:1.25;}.uppercase{text-transform:uppercase;}.italic{font-style:italic;}.line-through{text-decoration-line:line-through;}.underline{text-decoration-line:underline;}.antialiased{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;}.tab{-moz-tab-size:4;-o-tab-size:4;tab-size:4;}.text-shadow{--un-text-shadow:0 0 1px var(--un-text-shadow-color, rgba(0, 0, 0, 0.2)),0 0 1px var(--un-text-shadow-color, rgba(1, 0, 5, 0.1));text-shadow:var(--un-text-shadow);}.opacity-20{opacity:0.2;}.shadow-sm{--un-shadow:0 2rpx 8rpx var(--un-shadow-color, rgb(0,, 0,, 0,, 0.06));box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.outline{outline-style:solid;}.ring{--un-ring-width:3px;--un-ring-offset-shadow:var(--un-ring-inset) 0 0 0 var(--un-ring-offset-width) var(--un-ring-offset-color);--un-ring-shadow:var(--un-ring-inset) 0 0 0 calc(var(--un-ring-width) + var(--un-ring-offset-width)) var(--un-ring-color);box-shadow:var(--un-ring-offset-shadow), var(--un-ring-shadow), var(--un-shadow);}.backdrop-blur-md{--un-backdrop-blur:blur(12px);-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}.blur{--un-blur:blur(8px);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.grayscale{--un-grayscale:grayscale(1);filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.filter{filter:var(--un-blur) var(--un-brightness) var(--un-contrast) var(--un-drop-shadow) var(--un-grayscale) var(--un-hue-rotate) var(--un-invert) var(--un-saturate) var(--un-sepia);}.backdrop-filter{-webkit-backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);backdrop-filter:var(--un-backdrop-blur) var(--un-backdrop-brightness) var(--un-backdrop-contrast) var(--un-backdrop-grayscale) var(--un-backdrop-hue-rotate) var(--un-backdrop-invert) var(--un-backdrop-opacity) var(--un-backdrop-saturate) var(--un-backdrop-sepia);}.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,-webkit-backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter,-webkit-backdrop-filter;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.transition-all{transition-property:all;transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);transition-duration:150ms;}.ease,.ease-in-out{transition-timing-function:cubic-bezier(0.4, 0, 0.2, 1);}.ease-out{transition-timing-function:cubic-bezier(0, 0, 0.2, 1);}.pb-safe{padding-bottom:env(safe-area-inset-bottom);} 
page{--status-bar-height:25px;--top-window-height:0px;--window-top:0px;--window-bottom:0px;--window-left:0px;--window-right:0px;--window-magin:0px}[data-c-h="true"]{display: none !important;}