<layout-tabbar-uni class="data-v-b051b919" u-s="{{['d']}}" u-i="b051b919-0" bind:__l="__l"><view class="app-container data-v-b051b919"><view class="app-content data-v-b051b919"><view class="glass-card mb-4 p-4 animate-slide-up data-v-b051b919"><view class="flex-between gap-3 data-v-b051b919"><view class="flex-1 data-v-b051b919"><wd-input wx:if="{{e}}" class="data-v-b051b919" u-s="{{['prefix']}}" bindconfirm="{{b}}" bindclear="{{c}}" u-i="b051b919-1,b051b919-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><wd-icon class="data-v-b051b919" u-i="b051b919-2,b051b919-1" bind:__l="__l" u-p="{{a}}" slot="prefix"/></wd-input></view><view class="flex gap-2 data-v-b051b919"><view class="btn-ghost data-v-b051b919" bindtap="{{h}}"><wd-icon wx:if="{{f}}" class="data-v-b051b919" u-i="b051b919-3,b051b919-0" bind:__l="__l" u-p="{{f}}"/><text class="text-caption data-v-b051b919">筛选</text><view wx:if="{{g}}" class="w-2 h-2 bg-error-500 rounded-full absolute top-1 right-1 data-v-b051b919"></view></view><view class="btn-ghost data-v-b051b919" bindtap="{{m}}"><wd-icon wx:if="{{i}}" class="data-v-b051b919" u-i="b051b919-4,b051b919-0" bind:__l="__l" u-p="{{i}}"/><text class="text-caption data-v-b051b919">品种</text><text wx:if="{{j}}" class="text-caption text-primary-600 data-v-b051b919">({{k}})</text><view wx:if="{{l}}" class="w-2 h-2 bg-success-500 rounded-full absolute top-1 right-1 data-v-b051b919"></view></view></view></view><view wx:if="{{n}}" class="flex flex-wrap gap-2 mt-3 data-v-b051b919"><view wx:if="{{o}}" class="app-card px-3 py-1 bg-primary-500 text-white text-caption rounded-full flex items-center gap-1 data-v-b051b919">{{p}} <wd-icon wx:if="{{r}}" class="data-v-b051b919" bindclick="{{q}}" u-i="b051b919-5,b051b919-0" bind:__l="__l" u-p="{{r}}"/></view><view wx:if="{{s}}" class="app-card px-3 py-1 bg-primary-500 text-white text-caption rounded-full flex items-center gap-1 data-v-b051b919">{{t}} <wd-icon wx:if="{{w}}" class="data-v-b051b919" bindclick="{{v}}" u-i="b051b919-6,b051b919-0" bind:__l="__l" u-p="{{w}}"/></view></view></view><scroll-view class="flex-1 data-v-b051b919" scroll-y refresher-enabled refresher-triggered="{{H}}" bindrefresherrefresh="{{I}}" bindscrolltolower="{{J}}"><view class="space-y-4 data-v-b051b919"><view wx:if="{{x}}" class="data-v-b051b919"><quotation-card wx:for="{{y}}" wx:for-item="quotation" wx:key="a" class="data-v-b051b919" bindclick="{{quotation.b}}" bindpublisherClick="{{quotation.c}}" u-i="{{quotation.d}}" bind:__l="__l" u-p="{{quotation.e}}"/></view><view wx:elif="{{z}}" class="app-card flex flex-col items-center justify-center py-12 text-center data-v-b051b919"><text class="app-text-caption text-gray-500 data-v-b051b919">{{A}}</text><wd-button wx:if="{{B}}" class="data-v-b051b919" u-s="{{['d']}}" bindclick="{{C}}" u-i="b051b919-8,b051b919-0" bind:__l="__l" u-p="{{D}}"> 清空筛选条件 </wd-button></view><view wx:if="{{E}}" class="app-card flex items-center justify-center py-8 data-v-b051b919"><wd-loading wx:if="{{F}}" class="data-v-b051b919" u-i="b051b919-9,b051b919-0" bind:__l="__l" u-p="{{F}}"/><text class="app-text-caption text-primary-600 ml-2 data-v-b051b919">加载中...</text></view><view wx:if="{{G}}" class="app-card flex items-center justify-center py-8 data-v-b051b919"><text class="app-text-caption text-gray-500 data-v-b051b919">没有更多报价了</text></view></view></scroll-view><wd-action-sheet wx:if="{{M}}" class="data-v-b051b919" bindselect="{{K}}" u-i="b051b919-10,b051b919-0" bind:__l="__l" bindupdateModelValue="{{L}}" u-p="{{M}}"/><wd-popup wx:if="{{S}}" class="data-v-b051b919" u-s="{{['d']}}" u-i="b051b919-11,b051b919-0" bind:__l="__l" bindupdateModelValue="{{R}}" u-p="{{S}}"><view class="app-card h-full flex flex-col data-v-b051b919"><view class="app-card-header flex-between data-v-b051b919"><text class="app-text-subtitle data-v-b051b919">筛选条件</text><wd-button wx:if="{{O}}" class="data-v-b051b919" u-s="{{['d']}}" bindclick="{{N}}" u-i="b051b919-12,b051b919-11" bind:__l="__l" u-p="{{O}}">重置</wd-button></view><view class="app-card-body flex-1 space-y-6 data-v-b051b919"><view class="data-v-b051b919"><text class="app-text-body font-medium mb-3 block data-v-b051b919">价格类型</text><view class="flex flex-wrap gap-2 data-v-b051b919"><wd-tag wx:for="{{P}}" wx:for-item="option" wx:key="b" class="data-v-b051b919" u-s="{{['d']}}" bindclick="{{option.c}}" u-i="{{option.d}}" bind:__l="__l" u-p="{{option.e}}">{{option.a}}</wd-tag></view></view><view class="data-v-b051b919"><text class="app-text-body font-medium mb-3 block data-v-b051b919">交易类型</text><view class="flex flex-wrap gap-2 data-v-b051b919"><wd-tag wx:for="{{Q}}" wx:for-item="option" wx:key="b" class="data-v-b051b919" u-s="{{['d']}}" bindclick="{{option.c}}" u-i="{{option.d}}" bind:__l="__l" u-p="{{option.e}}">{{option.a}}</wd-tag></view></view></view></view></wd-popup><commodity-section-selector wx:if="{{W}}" class="data-v-b051b919" bindconfirm="{{T}}" bindcancel="{{U}}" u-i="b051b919-15,b051b919-0" bind:__l="__l" bindupdateVisible="{{V}}" u-p="{{W}}"/></view></view></layout-tabbar-uni>