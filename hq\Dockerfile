# hq 量化交易行情引擎 - 生产环境 Dockerfile
# 基于 Python 3.13 + UV + TA-Lib 0.6.4
FROM python:3.13-slim

# 设置工作目录
WORKDIR /app

# 配置清华apt源
RUN sed -i 's|http://deb.debian.org|https://mirrors.tuna.tsinghua.edu.cn|g' /etc/apt/sources.list.d/debian.sources

# 安装系统依赖和编译工具
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        locales \
        build-essential \
        wget \
        netcat-openbsd \
        gcc \
        g++ \
        make \
        pkg-config \
        python3-dev \
        && rm -rf /var/lib/apt/lists/*

# 配置中文环境
RUN sed -i '/zh_CN.UTF-8/s/^# //g' /etc/locale.gen && \
    sed -i '/zh_CN.GB18030/s/^# //g' /etc/locale.gen && \
    locale-gen

# 设置环境变量
ENV LANG=zh_CN.GB18030 \
    LC_ALL=zh_CN.GB18030 \
    PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    LD_LIBRARY_PATH=/usr/local/lib:$LD_LIBRARY_PATH \
    PKG_CONFIG_PATH=/usr/local/lib/pkgconfig:$PKG_CONFIG_PATH

# 安装 UV 包管理器（仅用于构建时安装依赖）
RUN pip install --no-cache-dir uv

# 安装 TA-Lib C 库 (0.6.4版本)
RUN cd /tmp && \
    wget https://github.com/TA-Lib/ta-lib/releases/download/v0.6.4/ta-lib-0.6.4-src.tar.gz && \
    tar -xzf ta-lib-0.6.4-src.tar.gz && \
    cd ta-lib-0.6.4 && \
    ./configure --prefix=/usr/local && \
    make -j1 && \
    make install && \
    ldconfig && \
    cd / && \
    rm -rf /tmp/ta-lib*

# 复制依赖配置文件
COPY requirements.txt ./

# 安装基础依赖到系统环境
RUN uv pip install --no-cache-dir numpy --system

# 从 requirements.txt 安装所有依赖到系统环境（避免 uv 运行时检查）
RUN uv pip install --system -r requirements.txt

# 复制应用代码
COPY . /app

# 清理临时文件
RUN find /app -name "*.pyc" -delete && \
    find /app -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

# 暴露端口
EXPOSE 40899

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import talib; print('TA-Lib version:', talib.__version__)" || exit 1

# 启动应用
CMD ["python", "main.py", "docker_config.ini", "--schedule"]