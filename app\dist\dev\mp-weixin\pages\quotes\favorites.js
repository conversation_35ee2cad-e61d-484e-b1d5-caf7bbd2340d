"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const utils_index = require("../../utils/index.js");
const store_user = require("../../store/user.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_loading2 = common_vendor.resolveComponent("wd-loading");
  const _easycom_wd_action_sheet2 = common_vendor.resolveComponent("wd-action-sheet");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_icon2 + _easycom_wd_button2 + _easycom_wd_loading2 + _easycom_wd_action_sheet2 + _component_layout_default_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_loading = () => "../../node-modules/wot-design-uni/components/wd-loading/wd-loading.js";
const _easycom_wd_action_sheet = () => "../../node-modules/wot-design-uni/components/wd-action-sheet/wd-action-sheet.js";
if (!Math) {
  (_easycom_wd_icon + QuotationCard + _easycom_wd_button + _easycom_wd_loading + _easycom_wd_action_sheet)();
}
const QuotationCard = () => "../../components/marketplace/QuotationCard.js";
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, {
  name: "QuotationFavorites"
}), {
  __name: "favorites",
  setup(__props) {
    store_user.useUserStore();
    const isLoading = common_vendor.ref(false);
    const isRefreshing = common_vendor.ref(false);
    const hasMore = common_vendor.ref(true);
    const showActionSheet = common_vendor.ref(false);
    const selectedQuotation = common_vendor.ref(null);
    const favoriteList = common_vendor.ref([]);
    const currentPage = common_vendor.ref(1);
    const pageSize = common_vendor.ref(10);
    const total = common_vendor.ref(0);
    function loadFavoriteList(refresh = false) {
      return __async(this, null, function* () {
        if (refresh) {
          currentPage.value = 1;
          favoriteList.value = [];
          isRefreshing.value = true;
        } else {
          isLoading.value = true;
        }
        try {
          const params = {
            page: currentPage.value,
            pageSize: pageSize.value
          };
          const mockData = {
            list: [],
            total: 0
          };
          if (refresh) {
            favoriteList.value = mockData.list;
          } else {
            favoriteList.value.push(...mockData.list);
          }
          total.value = mockData.total;
          hasMore.value = favoriteList.value.length < mockData.total;
        } catch (error) {
          console.error("加载收藏列表失败:", error);
          common_vendor.index.showToast({
            title: "加载失败",
            icon: "error"
          });
        } finally {
          isLoading.value = false;
          isRefreshing.value = false;
        }
      });
    }
    function loadMore() {
      return __async(this, null, function* () {
        if (!hasMore.value || isLoading.value)
          return;
        currentPage.value++;
        yield loadFavoriteList();
      });
    }
    function onRefresh() {
      return __async(this, null, function* () {
        yield loadFavoriteList(true);
      });
    }
    const actionSheetOptions = [
      { name: "查看详情", value: "detail" },
      { name: "取消收藏", value: "unfavorite" },
      { name: "发起点价", value: "contact" }
    ];
    function showQuotationActions(quotation) {
      selectedQuotation.value = quotation;
      showActionSheet.value = true;
    }
    function handleActionSelect({ item }) {
      showActionSheet.value = false;
      if (!selectedQuotation.value)
        return;
      switch (item.value) {
        case "detail":
          viewDetail(selectedQuotation.value);
          break;
        case "unfavorite":
          handleUnfavorite(selectedQuotation.value);
          break;
        case "contact":
          contactPublisher(selectedQuotation.value);
          break;
      }
      selectedQuotation.value = null;
    }
    function viewDetail(quotation) {
      utils_index.navigateToPage({
        url: `/pages/quotes/detail?id=${quotation.id}&from=favorites`
      });
    }
    function handleUnfavorite(quotation) {
      return __async(this, null, function* () {
        try {
          const index = favoriteList.value.findIndex((item) => item.id === quotation.id);
          if (index !== -1) {
            favoriteList.value.splice(index, 1);
            total.value--;
          }
          common_vendor.index.showToast({
            title: "已取消收藏",
            icon: "success"
          });
        } catch (error) {
          console.error("取消收藏失败:", error);
          common_vendor.index.showToast({
            title: "取消收藏失败",
            icon: "error"
          });
        }
      });
    }
    function contactPublisher(_quotation) {
      common_vendor.index.showToast({
        title: "功能开发中",
        icon: "none"
      });
    }
    function handlePublisherClick(userID) {
      utils_index.navigateToPage({
        url: `/pages/quotes/public-list?id=${userID}`
      });
    }
    function handleNavigate(path) {
      utils_index.navigateToPage({ url: path });
    }
    common_vendor.onMounted(() => __async(this, null, function* () {
      yield loadFavoriteList();
    }));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(total.value),
        b: common_vendor.p({
          name: "star",
          size: "48rpx",
          ["custom-class"]: "header-icon"
        }),
        c: favoriteList.value.length > 0
      }, favoriteList.value.length > 0 ? {
        d: common_vendor.f(favoriteList.value, (quotation, k0, i0) => {
          return {
            a: quotation.id,
            b: common_vendor.o(showQuotationActions, quotation.id),
            c: common_vendor.o(handlePublisherClick, quotation.id),
            d: "2f4046e0-2-" + i0 + ",2f4046e0-0",
            e: common_vendor.p({
              quotation
            })
          };
        })
      } : !isLoading.value ? {
        f: common_vendor.p({
          name: "star",
          size: "120rpx",
          ["custom-class"]: "empty-icon"
        }),
        g: common_vendor.o(($event) => handleNavigate("/pages/quotes/marketplace")),
        h: common_vendor.p({
          type: "primary",
          ["custom-class"]: "empty-button"
        })
      } : {}, {
        e: !isLoading.value,
        i: isLoading.value && favoriteList.value.length > 0
      }, isLoading.value && favoriteList.value.length > 0 ? {
        j: common_vendor.p({
          size: "24rpx",
          ["custom-class"]: "loading-spinner"
        })
      } : {}, {
        k: !hasMore.value && favoriteList.value.length > 0
      }, !hasMore.value && favoriteList.value.length > 0 ? {} : {}, {
        l: isRefreshing.value,
        m: common_vendor.o(onRefresh),
        n: common_vendor.o(loadMore),
        o: common_vendor.o(handleActionSelect),
        p: common_vendor.o(($event) => showActionSheet.value = $event),
        q: common_vendor.p({
          actions: actionSheetOptions,
          ["cancel-text"]: "取消",
          ["custom-class"]: "action-sheet",
          modelValue: showActionSheet.value
        })
      });
    };
  }
}));
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2f4046e0"]]);
wx.createPage(MiniProgramPage);
