"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
const http_http = require("../http/http.js");
function getSelectableList(params = {}) {
  const queryParams = __spreadValues({
    page: 1,
    pageSize: 50
  }, params);
  return http_http.http.get("/user/getSelectableList", queryParams);
}
function getSelectableProfile(id) {
  return http_http.http.get(`/user/getSelectableProfile/${id}`);
}
function getUserPublicProfile(id) {
  return http_http.http.get(`/users/${id}/profile`);
}
function getUserFavoriteCommodities() {
  return http_http.http.get("/user/getFavoriteCommodities");
}
function setUserFavoriteCommodities(commodities) {
  return http_http.http.post("/user/setFavoriteCommodities", commodities);
}
exports.getSelectableList = getSelectableList;
exports.getSelectableProfile = getSelectableProfile;
exports.getUserFavoriteCommodities = getUserFavoriteCommodities;
exports.getUserPublicProfile = getUserPublicProfile;
exports.setUserFavoriteCommodities = setUserFavoriteCommodities;
