import typer
import os
from typing_extensions import Annotated

from . import synchronizer

app = typer.Typer(name="commodity", help="Commands for managing commodity data.")


@app.command("init")
def init_commodities(
    csv_file: Annotated[str, typer.Option(help="CSV文件路径")] = "features/commodity_update/futures_exchange_codes.csv",
    dry_run: Annotated[bool, typer.Option(help="预览模式，不实际写入数据库")] = False,
):
    """
    从CSV文件初始化商品种类数据
    如果商品名称已存在则覆盖，不存在则新建
    """
    typer.echo("开始初始化商品种类数据...")
    
    # 构建完整的CSV文件路径
    if not os.path.isabs(csv_file):
        # 相对路径，基于scripts目录
        # __file__ 是当前文件路径，需要回到scripts根目录
        scripts_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        csv_file_path = os.path.join(scripts_dir, csv_file)
    else:
        csv_file_path = csv_file
    
    # 检查文件是否存在
    if not os.path.exists(csv_file_path):
        typer.echo(typer.style(f"错误: CSV文件不存在: {csv_file_path}", fg=typer.colors.RED))
        raise typer.Exit(code=1)
    
    try:
        # 读取CSV数据
        commodities_data = synchronizer.read_csv_data(csv_file_path)
        typer.echo(f"成功读取 {len(commodities_data)} 条商品数据")
        
        # 验证数据
        errors = synchronizer.validate_csv_data(commodities_data)
        if errors:
            typer.echo(typer.style("数据验证失败:", fg=typer.colors.RED))
            for error in errors:
                typer.echo(typer.style(f"  - {error}", fg=typer.colors.RED))
            raise typer.Exit(code=1)
        
        typer.echo(typer.style("数据验证通过", fg=typer.colors.GREEN))
        
        if dry_run:
            typer.echo(typer.style("\n[预览模式] 显示前5条数据:", fg=typer.colors.YELLOW))
            for i, commodity in enumerate(commodities_data[:5], 1):
                typer.echo(f"  {i}. {commodity['name']} ({commodity['product_id']}) - {commodity['exchange_id']} - {commodity['section']}")
            
            if len(commodities_data) > 5:
                typer.echo(f"  ... 还有 {len(commodities_data) - 5} 条数据")
            
            typer.echo(typer.style("\n[预览模式] 未对数据库进行任何更改", fg=typer.colors.YELLOW))
            return
        
        # 获取当前数据库中的商品数量
        current_count = synchronizer.get_current_commodities_count()
        typer.echo(f"当前数据库中有 {current_count} 条商品记录")
        
        # 同步到数据库
        added_count, updated_count, conflicts = synchronizer.sync_commodities_to_database(commodities_data)

        # 显示冲突信息
        if conflicts:
            typer.echo(typer.style("\n⚠️  发现数据冲突:", fg=typer.colors.YELLOW))
            for conflict in conflicts:
                typer.echo(typer.style(f"  - {conflict}", fg=typer.colors.YELLOW))

        # 显示同步结果
        typer.echo(typer.style("\n同步完成!", bold=True, fg=typer.colors.GREEN))
        typer.echo(f"- 新增商品: {typer.style(str(added_count), fg=typer.colors.GREEN)}")
        typer.echo(f"- 更新商品: {typer.style(str(updated_count), fg=typer.colors.CYAN)}")
        if conflicts:
            typer.echo(f"- 跳过冲突: {typer.style(str(len(conflicts)), fg=typer.colors.YELLOW)}")
        typer.echo(f"- 总计处理: {typer.style(str(len(commodities_data)), fg=typer.colors.BLUE)}")

        # 获取更新后的数据库商品数量
        final_count = synchronizer.get_current_commodities_count()
        typer.echo(f"- 数据库中现有商品总数: {typer.style(str(final_count), fg=typer.colors.BLUE)}")
        
    except FileNotFoundError as e:
        typer.echo(typer.style(f"文件错误: {e}", fg=typer.colors.RED))
        raise typer.Exit(code=1)
    except Exception as e:
        typer.echo(typer.style(f"同步过程中发生错误: {e}", fg=typer.colors.RED))
        raise typer.Exit(code=1)


@app.command("status")
def show_status():
    """
    显示当前商品数据库状态
    """
    try:
        count = synchronizer.get_current_commodities_count()
        typer.echo(f"数据库中当前有 {typer.style(str(count), fg=typer.colors.BLUE)} 条商品记录")
    except Exception as e:
        typer.echo(typer.style(f"获取状态时发生错误: {e}", fg=typer.colors.RED))
        raise typer.Exit(code=1)
