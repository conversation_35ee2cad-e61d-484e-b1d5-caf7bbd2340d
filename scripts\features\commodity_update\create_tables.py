"""
创建商品表的脚本
运行此脚本来创建commodities表
"""

from modules.database import engine, Base
from .models import Commodity

def create_commodity_table():
    """创建商品表"""
    try:
        # 创建所有表（如果不存在）
        Base.metadata.create_all(bind=engine)
        print("成功创建商品表 (commodities)")
    except Exception as e:
        print(f"创建表时发生错误: {e}")
        raise

if __name__ == "__main__":
    create_commodity_table()
