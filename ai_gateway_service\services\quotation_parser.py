"""
Quotation parsing service using LangChain and OpenAI
"""

import logging
from typing import Optional
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain.prompts import PromptTemplate
from langchain.output_parsers import PydanticOutputParser
from langchain.schema import Output<PERSON>arserException

from schemas.quotation import ParsedQuotationData, ParsedQuotationResponse, ContractInferenceRequest
from prompts.quotation_prompts import PARSE_QUOTATION_PROMPT
from core.config import get_settings
from services.contract_inference import get_contract_inference_service

logger = logging.getLogger(__name__)


class QuotationParsingService:
    """Service for parsing unstructured quotation text into structured data"""
    
    def __init__(self):
        self.settings = get_settings()
        self.parser = PydanticOutputParser(pydantic_object=ParsedQuotationData)
        self.prompt = PromptTemplate(
            template=PARSE_QUOTATION_PROMPT,
            input_variables=["user_text"],
            partial_variables={"format_instructions": self.parser.get_format_instructions()}
        )
        
        # Initialize OpenAI model
        self.model = ChatOpenAI(
            temperature=0,
            openai_api_base=self.settings.openai_api_base,
            model=self.settings.openai_model,
            api_key=self.settings.openai_api_key,
            max_retries=2
        )
        
        # Create the processing chain
        self.chain = self.prompt | self.model | self.parser
    
    async def parse_quotation_text(self, user_text: str) -> ParsedQuotationResponse:
        """
        Parse unstructured quotation text into structured data with contract inference
        
        Args:
            user_text: The unstructured text input from user
            
        Returns:
            ParsedQuotationResponse: Contains success status and parsed data
        """
        try:
            logger.info(f"Parsing quotation text: {user_text[:100]}...")
            
            # Step 1: Parse basic quotation data using LangChain
            response = await self.chain.ainvoke({"user_text": user_text})
            
            logger.info(f"Successfully parsed basic quotation data: {response}")
            
            # Step 2: Infer contract information if priceType is Basis
            if response.priceType == "Basis":
                logger.info("Basis pricing detected, inferring contract...")
                
                contract_service = get_contract_inference_service()
                inference_request = ContractInferenceRequest(
                    text=user_text,
                    commodity_name=response.commodityName
                )
                
                inference_result = await contract_service.infer_contract(inference_request)
                
                if inference_result.success and inference_result.confidence and inference_result.confidence > 0.5:
                    # Use inferred contract if confidence is high enough
                    response.instrumentRefID = inference_result.instrumentRefID
                    response.instrumentRef = inference_result.instrumentRef
                    logger.info(f"Contract inference successful: {inference_result.instrumentRef.instrument_id} (confidence: {inference_result.confidence})")
                else:
                    logger.warning(f"Contract inference failed or low confidence: {inference_result.message}")
            
            return ParsedQuotationResponse(
                success=True,
                data=response,
                message="解析成功"
            )
            
        except OutputParserException as e:
            logger.error(f"Failed to parse LLM output: {e}")
            return ParsedQuotationResponse(
                success=False,
                data=None,
                message="AI解析格式错误，请检查输入文本"
            )
            
        except Exception as e:
            logger.error(f"Unexpected error during quotation parsing: {e}")
            return ParsedQuotationResponse(
                success=False,
                data=None,
                message="解析服务暂时不可用，请稍后重试"
            )
    
    def validate_quotation_data(self, data: ParsedQuotationData) -> bool:
        """
        Validate parsed quotation data for basic business rules
        
        Args:
            data: Parsed quotation data to validate
            
        Returns:
            bool: True if data passes validation
        """
        # Basic validation rules
        if data.priceType == "Basis" and not data.instrumentRefID:
            logger.warning("Basis pricing requires instrumentRefID")
            return False
            
        if data.price < 0 and data.priceType == "Fixed":
            logger.warning("Fixed price should not be negative")
            return False
            
        return True


# Global service instance (lazy initialization)
_quotation_service = None

def get_quotation_service() -> QuotationParsingService:
    """Get or create the global quotation service instance"""
    global _quotation_service
    if _quotation_service is None:
        _quotation_service = QuotationParsingService()
    return _quotation_service