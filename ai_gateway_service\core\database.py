"""
Database connection management for AI Gateway service
"""

import logging
from typing import Optional, List, Dict, Any
import aiomysql
from core.config import get_settings

logger = logging.getLogger(__name__)


class DatabaseConnection:
    """MySQL database connection manager using aiomysql"""
    
    def __init__(self):
        self.pool = None
        self.settings = get_settings()
        self._connected = False
    
    async def connect(self):
        """Connect to MySQL database"""
        try:
            self.pool = await aiomysql.create_pool(
                host=self.settings.db_host,
                port=self.settings.db_port,
                user=self.settings.db_user,
                password=self.settings.db_password,
                db=self.settings.db_name,
                charset=self.settings.db_charset,
                autocommit=True,
                maxsize=10,
                minsize=1
            )
            self._connected = True
            logger.info(f"Database connection established to {self.settings.db_host}:{self.settings.db_port}/{self.settings.db_name}")
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    async def disconnect(self):
        """Disconnect from database"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            logger.info("Database connection pool closed")
        self._connected = False
    
    def is_connected(self) -> bool:
        """Check if database is connected"""
        return self._connected and self.pool is not None
    
    async def execute_query(self, query: str, params: Optional[tuple] = None) -> List[Dict[str, Any]]:
        """Execute database query and return results"""
        if not self.is_connected():
            await self.connect()
        
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    logger.debug(f"Executing query: {query}")
                    await cursor.execute(query, params or ())
                    result = await cursor.fetchall()
                    logger.debug(f"Query returned {len(result)} rows")
                    return result
        except Exception as e:
            logger.error(f"Database query failed: {e}")
            raise
    
    async def execute_single_query(self, query: str, params: Optional[tuple] = None) -> Optional[Dict[str, Any]]:
        """Execute query and return single result"""
        results = await self.execute_query(query, params)
        return results[0] if results else None


# Global database connection instance
_db_connection = None


async def get_database_connection() -> DatabaseConnection:
    """Get or create the global database connection instance"""
    global _db_connection
    if _db_connection is None:
        _db_connection = DatabaseConnection()
        await _db_connection.connect()
    return _db_connection


async def close_database_connection():
    """Close the global database connection"""
    global _db_connection
    if _db_connection:
        await _db_connection.disconnect()
        _db_connection = None