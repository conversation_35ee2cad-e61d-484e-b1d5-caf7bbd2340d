/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* 全局样式 - 为 wot-design-uni 组件的 custom-class 提供样式 */
 .loading-spinner {
  --loading-color: var(--app-color-primary);
}
.fixed-type {
  background: var(--app-color-primary-600);
}
.basis-type {
  background: var(--app-color-warning);
}
.negotiable-type {
  background: var(--app-color-info);
}
.unknown-type {
  background: var(--app-color-secondary-600);
}
.animate-slide-up[style="animation-delay: 0.1s"] {
  animation-delay: 0.1s;
}
.animate-slide-up[style="animation-delay: 0.2s"] {
  animation-delay: 0.2s;
}
.animate-slide-up[style="animation-delay: 0.3s"] {
  animation-delay: 0.3s;
}
@media (max-width: 750rpx) {
.space-y-4 {
    margin-top: var(--app-spacing-base);
}
.flex-1 {
    min-width: calc(50% - 12rpx);
}
}
 .wd-button {
  border-radius: var(--app-radius-lg);
  font-weight: var(--app-font-weight-medium);
  transition: all var(--app-duration-base) var(--app-ease-out);
}
 .wd-button:hover {
  transform: translateY(-2rpx);
}
 .wd-button:active {
  transform: translateY(0);
}
.fixed {
  backdrop-filter: blur(20rpx);
  -webkit-backdrop-filter: blur(20rpx);
  border-top: 1rpx solid var(--app-border-secondary);
}
.btn-edit {
  background: linear-gradient(135deg, var(--app-color-primary-500), var(--app-color-primary-600)) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-edit:hover {
  background: linear-gradient(135deg, var(--app-color-primary-600), var(--app-color-primary-700)) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.4) !important;
}
.btn-edit:active {
  transform: translateY(0) !important;
}
.btn-publish {
  background: linear-gradient(135deg, var(--app-color-success), #059669) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-publish:hover {
  background: linear-gradient(135deg, #059669, #047857) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(16, 185, 129, 0.4) !important;
}
.btn-publish:active {
  transform: translateY(0) !important;
}
.btn-unpublish {
  background: linear-gradient(135deg, var(--app-color-warning), #d97706) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-unpublish:hover {
  background: linear-gradient(135deg, #d97706, #b45309) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(245, 158, 11, 0.4) !important;
}
.btn-unpublish:active {
  transform: translateY(0) !important;
}
.btn-delete {
  background: linear-gradient(135deg, var(--app-color-error), #dc2626) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-delete:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(239, 68, 68, 0.4) !important;
}
.btn-delete:active {
  transform: translateY(0) !important;
}
.btn-share {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-share:hover {
  background: linear-gradient(135deg, #7c3aed, #6d28d9) !important;
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.4) !important;
}
.btn-share:active {
  transform: translateY(0) !important;
}
.btn-contact {
  background: linear-gradient(135deg, var(--app-color-primary-500), var(--app-color-primary-600)) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-contact:hover {
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.4) !important;
}
.btn-contact:active {
  transform: translateY(0) !important;
}
.btn-share-friend {
  background: linear-gradient(135deg, #8b5cf6, #ec4899) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
  font-weight: var(--app-font-weight-medium) !important;
  transition: all var(--app-duration-base) var(--app-ease-out) !important;
}
.btn-share-friend:hover {
  transform: translateY(-2rpx) !important;
  box-shadow: 0 8rpx 25rpx rgba(139, 92, 246, 0.4) !important;
}
.btn-share-friend:active {
  transform: translateY(0) !important;
}
.btn-edit,
.btn-publish,
.btn-unpublish,
.btn-delete,
.btn-share,
.btn-contact,
.btn-share-friend {
  position: relative;
  overflow: hidden;
}
.btn-edit::after,
.btn-publish::after,
.btn-unpublish::after,
.btn-delete::after,
.btn-share::after,
.btn-contact::after,
.btn-share-friend::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: inherit;
  opacity: 0;
  transition: opacity 0.3s ease;
}
.btn-edit:active::after,
.btn-publish:active::after,
.btn-unpublish:active::after,
.btn-delete:active::after,
.btn-share:active::after,
.btn-contact:active::after,
.btn-share-friend:active::after {
  opacity: 1;
}