import { http } from '@/http/http'
import type { IUserSelectable, IUserPublicProfile } from '@/types'

/**
 * 用户列表查询参数
 */
export interface IUserListParams {
  search?: string
  page?: number
  pageSize?: number
}

/**
 * 分页结果接口
 */
export interface IPageResult<T> {
  list: T[]
  total: number
  page: number
  pageSize: number
}

/**
 * 获取可选择的用户列表
 * @param params 查询参数：搜索关键词、页码、每页数量
 * @returns Promise 包含分页用户列表
 */
export function getSelectableList(params: IUserListParams = {}) {
  const queryParams = {
    page: 1,
    pageSize: 50,
    ...params
  }
  return http.get<IPageResult<IUserSelectable>>('/user/getSelectableList', queryParams)
}

/**
 * 获取指定用户的可选择信息
 * @param id 用户ID
 * @returns Promise 包含用户信息
 */
export function getSelectableProfile(id: number) {
  return http.get<IUserSelectable>(`/user/getSelectableProfile/${id}`)
}

/**
 * 获取用户公开档案
 * @param id 用户ID
 * @returns Promise 包含用户公开档案信息
 */
export function getUserPublicProfile(id: number) {
  return http.get<IUserPublicProfile>(`/users/${id}/profile`)
}

/**
 * 获取用户关注的品种列表
 * @returns Promise 包含用户关注的品种ProductID列表
 */
export function getUserFavoriteCommodities() {
  return http.get<string[]>('/user/getFavoriteCommodities')
}

/**
 * 设置用户关注的品种列表
 * @param commodities 关注的品种ProductID列表
 * @returns Promise 设置结果
 */
export function setUserFavoriteCommodities(commodities: string[]) {
  return http.post<boolean>('/user/setFavoriteCommodities', commodities)
}