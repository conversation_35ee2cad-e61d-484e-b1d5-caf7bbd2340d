# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
.env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs/
*.log

# Cache
cache/
.cache/

# Backup
backup/

# Git
.git/
.gitignore

# Documentation
*.md
!README.md

# Test files
test/
tests/
*_test.py
test_*.py

# Development files
install.bat
install.sh

# Local config (keep docker_config.ini)
config.ini
