from pydantic import BaseModel, Field
from typing import Optional, Literal


class QuotationParseRequest(BaseModel):
    """Request model for quotation text parsing"""
    text: str = Field(..., description="非结构化的报价文本内容")


class InstrumentSelectItem(BaseModel):
    """Instrument select item model matching frontend IInstrumentSelectItem"""
    id: int = Field(..., description="合约ID")
    instrument_id: str = Field(..., description="合约代码")
    instrument_name: str = Field(..., description="合约名称")
    product_name: str = Field(..., description="产品名称")
    exchange_id: str = Field(..., description="交易所ID")


class ParsedQuotationData(BaseModel):
    """Parsed quotation data model exactly matching frontend ICreateQuotationRequest"""
    title: str = Field(..., description="报价标题")
    commodityName: str = Field(..., description="商品名称")
    deliveryLocation: str = Field(..., description="交货地点")
    brand: Optional[str] = Field(None, description="品牌")
    specifications: Optional[str] = Field(None, description="规格说明")
    description: Optional[str] = Field(None, description="补充说明")
    priceType: Literal["Fixed", "Basis"] = Field(..., description="报价方式：Fixed(一口价) 或 Basis(基差报价)")
    price: float = Field(..., description="价格或基差值")
    instrumentRefID: Optional[int] = Field(None, description="推断的期货合约ID")
    instrumentRef: Optional[InstrumentSelectItem] = Field(None, description="推断的期货合约信息")
    expiresAt: Optional[str] = Field(None, description="过期时间ISO字符串")
    status: Optional[Literal["Draft", "Active"]] = Field("Draft", description="报价状态")


class ParsedQuotationResponse(BaseModel):
    """Response wrapper for parsed quotation data"""
    success: bool = Field(..., description="解析是否成功")
    data: Optional[ParsedQuotationData] = Field(None, description="解析结果数据")
    message: Optional[str] = Field(None, description="错误或提示信息")


class ContractInferenceRequest(BaseModel):
    """Request model for contract inference"""
    text: str = Field(..., description="需要推断合约的文本内容")
    commodity_name: Optional[str] = Field(None, description="商品名称（可选，用于辅助推断）")


class ContractInferenceResponse(BaseModel):
    """Response model for contract inference"""
    success: bool = Field(..., description="推断是否成功")
    instrumentRefID: Optional[int] = Field(None, description="推断的合约ID")
    instrumentRef: Optional[InstrumentSelectItem] = Field(None, description="推断的合约信息")
    confidence: Optional[float] = Field(None, description="推断置信度 (0-1)")
    reasoning: Optional[str] = Field(None, description="推断理由")
    message: Optional[str] = Field(None, description="错误或提示信息")