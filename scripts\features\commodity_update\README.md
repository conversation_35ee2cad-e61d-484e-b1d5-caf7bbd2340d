# 商品种类初始化功能

这个模块提供了从CSV文件初始化商品种类数据的功能。

## 功能特性

- 从CSV文件读取商品数据
- 自动去重（如果CSV中有重复的商品名称，保留最后一个）
- 支持数据覆盖（如果数据库中已存在相同名称的商品，则更新；否则新建）
- 数据验证（检查必填字段和重复的品种ID）
- 预览模式（可以在不修改数据库的情况下查看将要处理的数据）

## 数据库结构

商品表 (commodities) 包含以下字段：
- `id`: 主键，自增
- `name`: 商品名称（唯一）
- `product_id`: 品种ID（唯一）
- `exchange_id`: 所属交易所ID
- `section`: 商品版块
- `created_at`: 创建时间
- `updated_at`: 更新时间
- `deleted_at`: 删除时间（软删除）

## CSV文件格式

CSV文件应包含以下列：
- `product_id`: 品种ID
- `exchange`: 交易所ID
- `name`: 商品名称
- `section`: 商品版块

示例：
```csv
product_id,exchange,name,section
PX,CZCE,鲜花,农产品
pp,DCE,聚丙烯,化工
au,SHFE,黄金,贵金属
```

## 使用方法

### 1. 创建数据库表

首次使用前需要创建数据库表：

```bash
uv run python -m features.commodity_update.create_tables
```

### 2. 查看当前状态

```bash
uv run python main.py commodity status
```

### 3. 预览模式（推荐先运行）

在实际导入前，建议先使用预览模式查看数据：

```bash
uv run python main.py commodity init --dry-run
```

### 4. 执行初始化

```bash
uv run python main.py commodity init
```

### 5. 使用自定义CSV文件

```bash
uv run python main.py commodity init --csv-file /path/to/your/file.csv
```

## 命令选项

### `commodity init`

- `--csv-file`: 指定CSV文件路径（默认：features/commodity_update/futures_exchange_codes.csv）
- `--dry-run`: 预览模式，不实际修改数据库

### `commodity status`

显示当前数据库中的商品数量

## 注意事项

1. **数据覆盖**: 如果数据库中已存在相同名称的商品，新数据会覆盖旧数据
2. **去重逻辑**: 如果CSV中有重复的商品名称，会保留最后出现的记录
3. **必填字段**: name、product_id、exchange_id 都是必填字段
4. **唯一性**: 商品名称(name)和品种ID(product_id)在数据库中必须唯一

## 错误处理

- 如果CSV文件不存在，会显示错误信息
- 如果数据验证失败，会显示具体的错误行和原因
- 如果数据库连接失败，会显示相应的错误信息

## 示例输出

```
开始初始化商品种类数据...
成功读取 79 条商品数据
数据验证通过
当前数据库中有 1 条商品记录

同步完成!
- 新增商品: 78
- 更新商品: 1
- 总计处理: 79
- 数据库中现有商品总数: 79
```
