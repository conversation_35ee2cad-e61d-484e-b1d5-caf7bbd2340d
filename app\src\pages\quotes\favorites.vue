<route lang="jsonc" type="page">
{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "我的收藏",
    "navigationStyle": "default"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { getFavoriteQuotationList } from '@/api/quotation'
import QuotationCard from '@/components/marketplace/QuotationCard.vue'
import type {
  IQuotationResponse,
  IQuotationListRequest
} from '@/types/quotation'
import { navigateToPage } from '@/utils'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'QuotationFavorites'
})

// ==================== Store 实例 ====================
const userStore = useUserStore()

// ==================== 响应式数据声明 ====================

// 页面状态管理
const isLoading = ref<boolean>(false)
const isRefreshing = ref<boolean>(false)
const hasMore = ref<boolean>(true)

// ActionSheet交互状态
const showActionSheet = ref<boolean>(false)
const selectedQuotation = ref<IQuotationResponse | null>(null)

// 数据存储
const favoriteList = ref<IQuotationResponse[]>([])
const currentPage = ref<number>(1)
const pageSize = ref<number>(10)
const total = ref<number>(0)

// ==================== 数据获取相关函数 ====================

/**
 * 加载收藏报价列表
 * @param refresh 是否刷新（重置分页）
 */
async function loadFavoriteList(refresh: boolean = false): Promise<void> {
  if (refresh) {
    currentPage.value = 1
    favoriteList.value = []
    isRefreshing.value = true
  } else {
    isLoading.value = true
  }

  try {
    const params: IQuotationListRequest = {
      page: currentPage.value,
      pageSize: pageSize.value
    }

    // TODO: 这里需要实际的收藏报价API
    // const res = await getFavoriteQuotationList(params)
    // const { list, total: totalCount } = res.data

    // 临时模拟数据，实际开发时需要替换为真实API调用
    const mockData = {
      list: [],
      total: 0
    }

    if (refresh) {
      favoriteList.value = mockData.list
    } else {
      favoriteList.value.push(...mockData.list)
    }

    total.value = mockData.total
    hasMore.value = favoriteList.value.length < mockData.total

  } catch (error) {
    console.error('加载收藏列表失败:', error)
    uni.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
    isRefreshing.value = false
  }
}

/**
 * 加载更多数据
 */
async function loadMore(): Promise<void> {
  if (!hasMore.value || isLoading.value) return

  currentPage.value++
  await loadFavoriteList()
}

/**
 * 下拉刷新
 */
async function onRefresh(): Promise<void> {
  await loadFavoriteList(true)
}

// ==================== 交互处理函数 ====================

// ActionSheet操作选项配置
const actionSheetOptions = [
  { name: '查看详情', value: 'detail' },
  { name: '取消收藏', value: 'unfavorite' },
  { name: '发起点价', value: 'contact' }
]

/**
 * 显示报价操作菜单
 * @param quotation 选中的报价
 */
function showQuotationActions(quotation: IQuotationResponse): void {
  selectedQuotation.value = quotation
  showActionSheet.value = true
}

/**
 * 处理ActionSheet选择
 * @param item 选中的操作项
 */
function handleActionSelect({ item }: { item: { name: string; value: string } }): void {
  showActionSheet.value = false

  if (!selectedQuotation.value) return

  switch (item.value) {
    case 'detail':
      viewDetail(selectedQuotation.value)
      break
    case 'unfavorite':
      handleUnfavorite(selectedQuotation.value)
      break
    case 'contact':
      contactPublisher(selectedQuotation.value)
      break
  }

  selectedQuotation.value = null
}

/**
 * 查看报价详情
 * @param quotation 报价对象
 */
function viewDetail(quotation: IQuotationResponse): void {
  navigateToPage({
    url: `/pages/quotes/detail?id=${quotation.id}&from=favorites`
  })
}

/**
 * 取消收藏
 * @param quotation 报价对象
 */
async function handleUnfavorite(quotation: IQuotationResponse): Promise<void> {
  try {
    // TODO: 实际的取消收藏API调用
    // await unfavoriteQuotation(quotation.id)
    
    // 从列表中移除
    const index = favoriteList.value.findIndex(item => item.id === quotation.id)
    if (index !== -1) {
      favoriteList.value.splice(index, 1)
      total.value--
    }
    
    uni.showToast({
      title: '已取消收藏',
      icon: 'success'
    })
  } catch (error) {
    console.error('取消收藏失败:', error)
    uni.showToast({
      title: '取消收藏失败',
      icon: 'error'
    })
  }
}

/**
 * 联系发布者/发起点价
 * @param _quotation 报价对象（暂未使用）
 */
function contactPublisher(_quotation: IQuotationResponse): void {
  // TODO: 实现联系功能，可能是发起点价请求
  uni.showToast({
    title: '功能开发中',
    icon: 'none'
  })
}

/**
 * 处理发布者点击事件，跳转到用户报价主页
 * @param userID 用户ID
 */
function handlePublisherClick(userID: number): void {
  navigateToPage({
    url: `/pages/quotes/public-list?id=${userID}`
  })
}

function handleNavigate(path: string) {
  if (!path) return
  navigateToPage({ url: path })
}

// ==================== 页面生命周期函数 ====================

/**
 * 页面挂载时初始化数据
 */
onMounted(async () => {
  await loadFavoriteList()
})
</script>

<template>
  <view class="page-container gradient-bg-primary">
    <!-- 头部说明 -->
    <view class="header-section">
      <view class="header-card">
        <view class="flex items-center justify-between">
          <view>
            <text class="header-title">我的收藏</text>
            <text class="header-subtitle">共 {{ total }} 条收藏报价</text>
          </view>
          <wd-icon name="star" size="48rpx" custom-class="header-icon" />
        </view>
      </view>
    </view>

    <!-- 列表内容 -->
    <scroll-view 
      class="scroll-container"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="onRefresh"
      @scrolltolower="loadMore"
    >
      <view class="list-container">
        <!-- 收藏报价列表 -->
        <view v-if="favoriteList.length > 0" class="quotation-list">
          <QuotationCard
            v-for="quotation in favoriteList"
            :key="quotation.id"
            :quotation="quotation"
            @click="showQuotationActions"
            @publisherClick="handlePublisherClick"
          />
        </view>
        
        <!-- 空状态 -->
        <view v-else-if="!isLoading" class="empty-state">
          <wd-icon name="star" size="120rpx" custom-class="empty-icon" />
          <text class="empty-title">暂无收藏报价</text>
          <text class="empty-subtitle">去报价市场看看有什么好报价吧</text>
          <wd-button
            type="primary"
            custom-class="empty-button"
            @click="handleNavigate('/pages/quotes/marketplace')"
          >
            前往报价市场
          </wd-button>
        </view>

        <!-- 加载更多 -->
        <view v-if="isLoading && favoriteList.length > 0" class="loading-more">
          <wd-loading size="24rpx" custom-class="loading-spinner" />
          <text class="loading-text">加载中...</text>
        </view>

        <!-- 没有更多 -->
        <view v-if="!hasMore && favoriteList.length > 0" class="no-more">
          <text>没有更多收藏了</text>
        </view>
      </view>
    </scroll-view>

    <!-- 操作菜单 -->
    <wd-action-sheet
      v-model="showActionSheet"
      :actions="actionSheetOptions"
      cancel-text="取消"
      custom-class="action-sheet"
      @select="handleActionSelect"
    />
  </view>
</template>

<style lang="scss" scoped>
// 设计系统变量
$primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
$primary-color: #667eea;
$primary-dark: #764ba2;
$bg-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
$bg-card: rgba(255, 255, 255, 0.95);
$radius-sm: 8rpx;
$radius-md: 12rpx;
$radius-lg: 20rpx;
$radius-xl: 44rpx;
$shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$shadow-md: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
$spacing-sm: 20rpx;
$spacing-md: 36rpx;
$spacing-lg: 48rpx;

.page-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  box-sizing: border-box;
}

.header-section {
  position: sticky;
  top: 0;
  z-index: 100;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  padding: $spacing-sm;
  padding-top: calc($spacing-sm + env(safe-area-inset-top));
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.header-card {
  background: $primary-gradient;
  padding: $spacing-sm;
  border-radius: $radius-lg;
  box-shadow: $shadow-md;
}

.header-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: white;
  margin-bottom: 4rpx;
}

.header-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}

:deep(.header-icon) {
  color: rgba(255, 255, 255, 0.9) !important;
}

.scroll-container {
  flex: 1;
  padding: $spacing-sm;
  box-sizing: border-box;
}

.quotation-list {
  width: 100%;
  box-sizing: border-box;

  :deep(.quotation-card) {
    background: $bg-card !important;
    backdrop-filter: blur(10rpx) !important;
    border-radius: $radius-lg !important;
    box-shadow: $shadow-md !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-4rpx) !important;
      box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15) !important;
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  text-align: center;
  margin: $spacing-md;

  :deep(.empty-icon) {
    color: #E4E7ED !important;
    margin-bottom: 32rpx;
  }

  .empty-title {
    font-size: 32rpx;
    color: #909399;
    font-weight: 500;
    margin-bottom: 16rpx;
  }

  .empty-subtitle {
    font-size: 24rpx;
    color: #C0C4CC;
    margin-bottom: 48rpx;
  }

  :deep(.empty-button) {
    background: $primary-gradient !important;
    border-radius: $radius-xl !important;
    font-weight: 600 !important;
    box-shadow: $shadow-sm !important;
    transition: all 0.3s ease !important;

    &:hover {
      transform: translateY(-2rpx) !important;
      box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.3) !important;
    }
  }
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background: $bg-card;
  backdrop-filter: blur(10rpx);
  border-radius: $radius-lg;
  margin: $spacing-sm;
  box-shadow: $shadow-sm;

  :deep(.loading-spinner) {
    color: $primary-color !important;
  }

  .loading-text {
    margin-left: 16rpx;
    font-size: 26rpx;
    color: $primary-color;
    font-weight: 500;
  }
}

.no-more {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  margin: $spacing-sm;

  text {
    position: relative;
    font-size: 24rpx;
    color: #909399;
    font-weight: 400;
    padding: 0 40rpx;

    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 60rpx;
      height: 2rpx;
      background-color: #e4e7ed;
    }

    &::before {
      left: -80rpx;
    }

    &::after {
      right: -80rpx;
    }
  }
}
</style>