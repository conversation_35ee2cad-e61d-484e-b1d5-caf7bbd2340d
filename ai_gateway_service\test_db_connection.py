"""
Test database connection and contract inference functionality
"""

import asyncio
import json
from core.database import get_database_connection, close_database_connection
from services.product_inference import get_product_inference_service
from services.contract_inference import get_contract_inference_service
from schemas.quotation import ContractInferenceRequest


async def test_database_connection():
    """Test MySQL database connection"""
    print("测试数据库连接...")
    
    try:
        db = await get_database_connection()
        
        # Test basic connection
        result = await db.execute_query("SELECT 1 as test")
        print(f"数据库连接成功: {result}")
        
        # Test instruments table access
        count_query = "SELECT COUNT(*) as total FROM instruments"
        count_result = await db.execute_single_query(count_query)
        print(f"合约表记录数: {count_result['total']}")
        
        # Test sample data retrieval
        sample_query = """
        SELECT id, instrument_id, instrument_name, product_id, product_name, exchange_id
        FROM instruments 
        WHERE inst_life_phase IN ('0', '1')
        LIMIT 5
        """
        sample_result = await db.execute_query(sample_query)
        print(f"样本合约数据:")
        for contract in sample_result:
            print(f"  {contract['instrument_id']}: {contract['instrument_name']} ({contract['product_name']})")
        
        return True
        
    except Exception as e:
        print(f"数据库连接失败: {e}")
        return False


async def test_product_inference():
    """Test product inference functionality"""
    print("\n测试产品推断功能...")
    
    try:
        service = get_product_inference_service()
        
        # Test cases
        test_cases = [
            ("螺纹钢基差报价，比RB2505高120", "螺纹钢"),
            ("沪铜现货价格18000元/吨", "铜"),
            ("豆粕期货升水50", "豆粕"),
            ("铝锭一口价19000", "铝")
        ]
        
        for text, expected_commodity in test_cases:
            print(f"\n测试文本: {text}")
            result = await service.infer_product(text, expected_commodity)
            
            if result.get("success"):
                print(f"  产品推断成功: {result['product_id']} - {result['product_name']}")
                print(f"  置信度: {result['confidence']}")
                print(f"  推断理由: {result['reasoning']}")
            else:
                print(f"  产品推断失败: {result.get('message')}")
        
        return True
        
    except Exception as e:
        print(f"产品推断测试失败: {e}")
        return False


async def test_contract_inference():
    """Test two-stage contract inference functionality"""
    print("\n测试两阶段合约推断功能...")
    
    try:
        service = get_contract_inference_service()
        
        # Test cases
        test_cases = [
            ContractInferenceRequest(
                text="螺纹钢基差报价，比RB2505高120，500吨，上海地区",
                commodity_name="螺纹钢"
            ),
            ContractInferenceRequest(
                text="沪铜现货，5月交割，升水200",
                commodity_name="铜"
            )
        ]
        
        for request in test_cases:
            print(f"\n测试文本: {request.text}")
            result = await service.infer_contract(request)
            
            if result.success:
                print(f"  合约推断成功: {result.instrumentRef.instrument_id}")
                print(f"  合约名称: {result.instrumentRef.instrument_name}")
                print(f"  综合置信度: {result.confidence}")
                print(f"  推断理由: {result.reasoning}")
            else:
                print(f"  合约推断失败: {result.message}")
        
        return True
        
    except Exception as e:
        print(f"合约推断测试失败: {e}")
        return False


async def main():
    """Run all database and inference tests"""
    print("AI Gateway 数据库和推断功能测试")
    print("=" * 60)
    
    # Test database connection
    db_success = await test_database_connection()
    
    if db_success:
        # Test product inference
        await test_product_inference()
        
        # Test contract inference
        await test_contract_inference()
    else:
        print("数据库连接失败，跳过推断功能测试")
    
    # Clean up
    await close_database_connection()
    
    print("\n测试完成!")
    print("如果看到错误，请检查:")
    print("1. MySQL数据库是否运行在 localhost:3306")
    print("2. 数据库用户名密码是否正确 (root/123456)")
    print("3. 是否存在 dianjia 数据库和 instruments 表")
    print("4. .env 文件中的 OPENAI_API_KEY 是否设置")


if __name__ == "__main__":
    asyncio.run(main())