/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.login-container.data-v-45258083 {
  min-height: 100vh;
  padding: 100rpx 48rpx 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}
.login-container .login-form.data-v-45258083 {
  width: 100%;
  max-width: 600rpx;
  background: rgba(255, 255, 255, 0.75);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12), inset 0 1rpx 0 rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  -webkit-backdrop-filter: blur(10rpx);
          backdrop-filter: blur(10rpx);
}
.login-container .login-form .logo-section.data-v-45258083 {
  text-align: center;
  margin-bottom: 60rpx;
  position: relative;
  z-index: 2;
}
.login-container .login-form .logo-section .logo.data-v-45258083 {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  filter: drop-shadow(0 4rpx 12rpx rgba(0, 0, 0, 0.15));
}
.login-container .login-form .logo-section .app-name.data-v-45258083 {
  display: block;
  font-size: 44rpx;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 0 1rpx 2rpx rgba(255, 255, 255, 0.3);
  position: relative;
  letter-spacing: 2rpx;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.login-container .login-form .login-tabs.data-v-45258083 {
  display: flex;
  margin-bottom: 40rpx;
  border-radius: 12rpx;
  background: #f5f7fa;
  padding: 6rpx;
}
.login-container .login-form .login-tabs .tab-item.data-v-45258083 {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  color: #909399;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  cursor: pointer;
}
.login-container .login-form .login-tabs .tab-item.active.data-v-45258083 {
  background: #fff;
  color: #667eea;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.15);
}
.login-container .login-form .login-tabs .tab-item.data-v-45258083:hover:not(.active) {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}
.login-container .login-form .form-content.data-v-45258083 {
  animation: fadeIn-45258083 0.3s ease-in-out;
}
.login-container .login-form .form-item.data-v-45258083 {
  margin-bottom: 36rpx;
}
.login-container .login-form .form-item .form-label.data-v-45258083 {
  display: block;
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 12rpx;
  line-height: 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.login-container .login-form .form-item .form-control.data-v-45258083 {
  display: flex;
  align-items: center;
  gap: 20rpx;
}
.login-container .login-form .form-item .form-control .form-input.data-v-45258083 {
  flex: 1;
  height: 88rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  color: #2c3e50;
  border: 2rpx solid #e4e7ed;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.95);
  box-sizing: border-box;
  transition: all 0.3s ease;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.login-container .login-form .form-item .form-control .form-input.data-v-45258083:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
  background: #fff;
}
.login-container .login-form .form-item .form-control .placeholder.data-v-45258083 {
  color: #c0c4cc;
}
.login-container .login-form .form-item .form-control .code-btn.data-v-45258083 {
  flex-shrink: 0;
  min-width: 140rpx;
  height: 88rpx;
  color: #667eea;
  font-size: 26rpx;
  border-radius: 12rpx;
  border: 2rpx solid #e4e7ed;
  background: #fff;
}
.login-container .login-form .form-item .form-control .code-btn.data-v-45258083:disabled {
  color: #c0c4cc;
  background: #f5f7fa;
}
.login-container .login-form .form-item .form-control .code-btn.data-v-45258083:not(:disabled):hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}
.login-container .login-form .form-item .form-control .captcha-input.data-v-45258083 {
  flex: 1;
  margin-right: 20rpx;
}
.login-container .login-form .form-item .form-control .captcha-image.data-v-45258083 {
  flex-shrink: 0;
  width: 200rpx;
  height: 88rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 12rpx;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
}
.login-container .login-form .form-item .form-control .captcha-image.data-v-45258083:hover {
  border-color: #667eea;
  box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.login-container .login-form .form-item .form-control .captcha-image .captcha-img.data-v-45258083 {
  width: 100%;
  height: 100%;
}
.login-container .login-form .form-item .form-control .captcha-image .captcha-loading.data-v-45258083 {
  font-size: 24rpx;
  color: #909399;
}
.login-container .login-form .login-btn.data-v-45258083 {
  margin: 48rpx 0 36rpx;
  height: 88rpx;
  font-size: 32rpx;
  border-radius: 44rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  font-weight: 600;
}
.login-container .login-form .login-btn.data-v-45258083:disabled {
  background: #e4e7ed;
  opacity: 0.6;
}
.login-container .login-form .divider.data-v-45258083 {
  text-align: center;
  position: relative;
  margin: 36rpx 0;
  color: #909399;
  font-size: 26rpx;
}
.login-container .login-form .divider.data-v-45258083::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg, transparent, #e4e7ed, transparent);
}
.login-container .login-form .divider text.data-v-45258083 {
  background: rgba(255, 255, 255, 0.95);
  padding: 0 20rpx;
  position: relative;
  z-index: 1;
}
.login-container .login-form .wx-login-btn.data-v-45258083 {
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
  border: 2rpx solid #07c160;
  color: #07c160;
  background: #fff;
}
.login-container .login-form .wx-login-btn .i-carbon-logo-wechat.data-v-45258083 {
  font-size: 32rpx;
  margin-right: 8rpx;
}
.login-container .agreement.data-v-45258083 {
  text-align: center;
  margin-top: 60rpx;
}
.login-container .agreement .agreement-text.data-v-45258083 {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
}
.login-container .agreement .agreement-text .link.data-v-45258083 {
  color: #87ceeb;
  text-decoration: underline;
}
@keyframes fadeIn-45258083 {
from {
    opacity: 0;
    transform: translateY(20rpx);
}
to {
    opacity: 1;
    transform: translateY(0);
}
}