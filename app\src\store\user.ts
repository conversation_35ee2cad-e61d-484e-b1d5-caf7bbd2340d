import type { IPhoneLoginRequest, IUpdateProfileRequest, IUsernameLoginRequest } from '@/types'
import type { IUser } from '@/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import {
  getProfile,
  loginByPhone,
  loginByUsername,
  loginByWechat,
  sendLoginCode,
  updateProfile,
  changePassword,
} from '@/api/auth'

import {
  logout as _logout,
  getWxCode,
} from '@/api/login'

import {
  getUserFavoriteCommodities,
  setUserFavoriteCommodities,
} from '@/api/user'
import { toast } from '@/utils/toast'
import { useSocketStore } from './socket'

// 用户状态初始化
const initialUserState: IUser = {
  ID: 0,
  CreatedAt: '',
  UpdatedAt: '',
  uuid: '',
  userName: '',
  phone: '',
  nickName: '',
  headerImg: '/static/images/default-avatar.png',
  companyName: '',
  companyOrgId: '',
  companyAddress: '',
  authorityId: 888,
  enable: 1,
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 统一的用户信息状态
    const userInfo = ref<IUser>({ ...initialUserState })
    const token = ref<string>('')
    
    // 用户关注品种相关状态
    const favoriteCommodities = ref<string[]>([])
    const favoritesLoading = ref<boolean>(false)
    const favoritesLastUpdated = ref<Date | null>(null)

    // 计算属性：是否已登录
    const isLoggedIn = computed(() => !!token.value && userInfo.value.ID > 0)
    
    // 计算属性：是否有关注的品种
    const hasFavoriteCommodities = computed(() => favoriteCommodities.value.length > 0)
    
    // 计算属性：是否为首次用户
    const isFirstTimeUser = computed(() => isLoggedIn.value && !hasFavoriteCommodities.value)

    // 设置用户信息
    const setUserInfo = (val: IUser, userToken?: string) => {
      console.log('设置用户信息', val)
      // 若头像为空 则使用默认头像
      if (!val.headerImg) {
        val.headerImg = initialUserState.headerImg
      }
      userInfo.value = val

      if (userToken) {
        token.value = userToken
        uni.setStorageSync('token', userToken)
        // WebSocket认证
        const socketStore = useSocketStore()
        if (socketStore.isConnected) {
          socketStore.authenticate(userToken)
        }
      }

      uni.setStorageSync('userInfo', val)
    }

    const setUserAvatar = (avatar: string) => {
      userInfo.value.headerImg = avatar
      console.log('设置用户头像', avatar)
    }

    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...initialUserState }
      token.value = ''
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    }

    /**
     * 获取用户资料
     */
    const getUserProfile = async () => {
      const res = await getProfile()
      const userData = res.data.userInfo
      setUserInfo(userData)
      return res
    }

    /**
     * 发送登录验证码
     */
    const sendVerificationCode = async (phone: string) => {
      const res = await sendLoginCode({ phone })
      if (res.code === 0) {
        toast.success(res.msg || '验证码发送成功')
      }
      else {
        toast.error(res.msg || '验证码发送失败')
        throw new Error(res.msg)
      }
      return res
    }

    /**
     * 手机号验证码登录
     */
    const phoneLogin = async (data: IPhoneLoginRequest) => {
      const res = await loginByPhone(data)
      console.log('手机号登录信息', res)
      if (res.code === 0) {
        toast.success(res.msg || '登录成功')
      }
      else {
        toast.error(res.msg || '登录失败')
        throw new Error(res.msg)
      }

      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('登录成功')
      return res
    }


    /**
     * 微信登录（新版）
     */
    const newWxLogin = async () => {
      // 获取微信小程序登录的code
      const data = await getWxCode()
      console.log('微信登录code', data)

      const res = await loginByWechat(data)
      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('微信登录成功')
      return res
    }

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      try {
        await _logout()
      }
      catch (error) {
        console.warn('调用退出登录API失败', error)
      }
      removeUserInfo()
      toast.success('已退出登录')
    }


    /**
     * 用户名密码登录
     */
    const usernameLogin = async (data: IUsernameLoginRequest) => {
      const res = await loginByUsername(data)
      console.log('用户名密码登录信息', res)
      if (res.code === 0) {
        toast.success(res.msg || '登录成功')
      }
      else {
        toast.error(res.msg || '登录失败')
        throw new Error(res.msg)
      }

      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('登录成功')
      return res
    }

    /**
     * 更新用户资料
     */
    const updateUserProfile = async (data: IUpdateProfileRequest) => {
      const res = await updateProfile(data)
      if (res.code === 0) {
        // 更新成功后重新获取用户信息
        await getUserProfile()
        toast.success('资料更新成功')
      }
      else {
        toast.error(res.data.msg || '更新失败')
        throw new Error(res.data.msg)
      }
      return res
    }

    /**
     * 修改密码
     */
    const changeUserPassword = async (data: { oldPassword: string; newPassword: string }) => {
      const res = await changePassword(data)
      if (res.code === 0) {
        toast.success('密码修改成功')
      }
      else {
        toast.error(res.data.msg || '密码修改失败')
        throw new Error(res.data.msg)
      }
      return res
    }

    // ==================== 用户关注品种管理 ====================
    
    /**
     * 加载用户关注的品种列表
     * @param forceRefresh 是否强制刷新
     */
    async function loadFavoriteCommodities(forceRefresh: boolean = false): Promise<void> {
      // 如果用户未登录，从本地存储加载
      if (!isLoggedIn.value) {
        // favoriteCommodities 会通过 pinia 持久化自动从本地存储恢复
        return
      }

      // 如果有缓存且不强制刷新，则跳过
      if (!forceRefresh && favoriteCommodities.value.length > 0 && favoritesLastUpdated.value) {
        try {
          const lastUpdateTime = favoritesLastUpdated.value instanceof Date
            ? favoritesLastUpdated.value
            : new Date(favoritesLastUpdated.value)

          if (!isNaN(lastUpdateTime.getTime())) {
            const cacheAge = Date.now() - lastUpdateTime.getTime()
            if (cacheAge < 2 * 60 * 1000) { // 2分钟缓存
              return
            }
          }
        } catch (error) {
          console.warn('无效的缓存时间，将重新加载数据:', error)
          favoritesLastUpdated.value = null
        }
      }

      try {
        favoritesLoading.value = true
        const res = await getUserFavoriteCommodities()

        if (res.code === 0) {
          favoriteCommodities.value = res.data
          favoritesLastUpdated.value = new Date()
        } else {
          throw new Error(res.msg || '获取关注品种失败')
        }
      } catch (error) {
        console.error('加载用户关注品种失败:', error)
        // 对于关注品种加载失败，不显示错误提示，静默失败
        // 保持本地数据不变
      } finally {
        favoritesLoading.value = false
      }
    }
    
    /**
     * 更新用户关注的品种列表
     * @param commodities 关注的商品名称列表
     */
    async function updateFavoriteCommodities(commodities: string[]): Promise<void> {
      // 如果用户未登录，只保存到本地
      if (!isLoggedIn.value) {
        favoriteCommodities.value = [...commodities]
        favoritesLastUpdated.value = new Date()
        console.log('用户未登录，关注品种已保存到本地')
        return
      }

      try {
        favoritesLoading.value = true
        const res = await setUserFavoriteCommodities(commodities)

        if (res.code === 0) {
          favoriteCommodities.value = [...commodities]
          favoritesLastUpdated.value = new Date()
          toast.success('关注品种更新成功')
        } else {
          throw new Error(res.msg || '更新关注品种失败')
        }
      } catch (error) {
        console.error('更新用户关注品种失败:', error)
        // 如果服务器更新失败，仍然保存到本地
        favoriteCommodities.value = [...commodities]
        favoritesLastUpdated.value = new Date()
        toast.error('服务器更新失败，已保存到本地')
      } finally {
        favoritesLoading.value = false
      }
    }
    
    /**
     * 检查品种是否被关注
     * @param commodityName 商品名称
     */
    function isCommodityFavorited(commodityName: string): boolean {
      return favoriteCommodities.value.includes(commodityName)
    }
    
    /**
     * 清除关注品种缓存
     */
    function clearFavoritesCache(): void {
      favoriteCommodities.value = []
      favoritesLastUpdated.value = null
    }

    return {
      // 用户基础状态
      userInfo,
      token,
      isLoggedIn,
      
      // 用户关注品种状态
      favoriteCommodities,
      favoritesLoading,
      hasFavoriteCommodities,
      isFirstTimeUser,

      // 用户基础方法
      phoneLogin,
      usernameLogin,
      sendVerificationCode,
      wxLogin: newWxLogin,
      getUserProfile,
      updateUserProfile,
      changeUserPassword,
      setUserInfo,
      setUserAvatar,
      logout,
      clearUserInfo: removeUserInfo, // 为HTTP错误处理提供清理用户信息的方法
      
      // 用户关注品种方法
      loadFavoriteCommodities,
      updateFavoriteCommodities,
      isCommodityFavorited,
      clearFavoritesCache,
    }
  },
  {
    persist: {
      key: 'user-store',
      storage: uni.getStorageSync && uni.setStorageSync ? {
        getItem: (key: string) => uni.getStorageSync(key),
        setItem: (key: string, value: string) => uni.setStorageSync(key, value),
      } : undefined,
      paths: ['userInfo', 'token', 'favoriteCommodities', 'favoritesLastUpdated'],
    },
  },
)
