"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const store_user = require("../../store/user.js");
const utils_toast = require("../../utils/toast.js");
const utils_fileUpload = require("../../utils/fileUpload.js");
const utils_imageUrl = require("../../utils/imageUrl.js");
const utils_index = require("../../utils/index.js");
if (!Array) {
  const _easycom_wd_icon2 = common_vendor.resolveComponent("wd-icon");
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_popup2 = common_vendor.resolveComponent("wd-popup");
  const _component_layout_tabbar_uni = common_vendor.resolveComponent("layout-tabbar-uni");
  (_easycom_wd_icon2 + _easycom_wd_button2 + _easycom_wd_popup2 + _component_layout_tabbar_uni)();
}
const _easycom_wd_icon = () => "../../node-modules/wot-design-uni/components/wd-icon/wd-icon.js";
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_popup = () => "../../node-modules/wot-design-uni/components/wd-popup/wd-popup.js";
if (!Math) {
  (_easycom_wd_icon + _easycom_wd_button + _easycom_wd_popup)();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "index",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const { avatarUrl } = utils_imageUrl.useUserStoreAvatar();
    const showAvatarCropper = common_vendor.ref(false);
    const selectedImagePath = common_vendor.ref("");
    const uploading = common_vendor.ref(false);
    const imageLoadError = common_vendor.ref(false);
    common_vendor.onMounted(() => __async(this, null, function* () {
      try {
        yield userStore.getUserProfile();
      } catch (error) {
        console.error("获取用户信息失败:", error);
      }
    }));
    const handleAvatarClick = () => __async(this, null, function* () {
      try {
        const filePath = yield utils_fileUpload.FileUploadUtil.chooseImage({
          count: 1,
          sizeType: ["compressed"],
          // 优先使用压缩版本
          sourceType: ["album", "camera"]
        });
        console.log("选择的图片路径:", filePath);
        selectedImagePath.value = filePath;
        imageLoadError.value = false;
        showAvatarCropper.value = true;
      } catch (error) {
        console.error("选择图片失败:", error);
        utils_toast.toast.error(error.message || "选择图片失败");
      }
    });
    const closeAvatarCropper = () => {
      showAvatarCropper.value = false;
      selectedImagePath.value = "";
      imageLoadError.value = false;
    };
    const onImageLoad = () => {
      console.log("图片加载成功");
      imageLoadError.value = false;
    };
    const onImageError = () => {
      console.error("图片加载失败");
      imageLoadError.value = true;
    };
    const uploadAvatar = () => __async(this, null, function* () {
      if (!selectedImagePath.value) {
        utils_toast.toast.error("请先选择图片");
        return;
      }
      uploading.value = true;
      try {
        console.log("开始上传头像，文件路径:", selectedImagePath.value);
        const isValid = yield utils_fileUpload.FileUploadUtil.validateImage(selectedImagePath.value);
        if (!isValid) {
          return;
        }
        const fileInfo = yield utils_fileUpload.FileUploadUtil.uploadFile(
          selectedImagePath.value,
          userStore.token,
          {
            classId: "0",
            onProgress: (progress) => {
              console.log("上传进度:", progress + "%");
            }
          }
        );
        console.log("文件上传成功，获取到的URL:", fileInfo.url);
        yield userStore.updateUserProfile({
          headerImg: fileInfo.url
        });
        closeAvatarCropper();
        utils_toast.toast.success("头像更新成功");
        console.log("头像更新流程完成");
      } catch (error) {
        console.error("头像上传失败:", error);
        utils_toast.toast.error(error.message || "头像上传失败，请重试");
      } finally {
        uploading.value = false;
      }
    });
    const goToEditProfile = () => {
      utils_index.navigateToPage({
        url: "/pages/profile/edit-profile"
      });
    };
    const goToChangePassword = () => {
      utils_index.navigateToPage({
        url: "/pages/profile/change-password"
      });
    };
    const goToHelpCenter = () => {
      utils_index.navigateToPage({
        url: "/pages/support/content-viewer?key=help&title=帮助中心"
      });
    };
    const goToFeedback = () => {
      utils_index.navigateToPage({
        url: "/pages/support/feedback"
      });
    };
    const goToAbout = () => {
      utils_index.navigateToPage({
        url: "/pages/support/about"
      });
    };
    const goToPrivacyPolicy = () => {
      utils_index.navigateToPage({
        url: "/pages/support/content-viewer?url=https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/privacy_policy.md&title=隐私政策"
      });
    };
    const goToTermsOfService = () => {
      utils_index.navigateToPage({
        url: "/pages/support/content-viewer?url=https://dianhaojia-yongpai.oss-cn-beijing.aliyuncs.com/yourBasePath/service_terms.md&title=服务条款"
      });
    };
    const handleLogout = () => {
      common_vendor.index.showModal({
        title: "确认退出",
        content: "确定要退出登录吗？",
        success: (res) => __async(this, null, function* () {
          if (res.confirm) {
            yield userStore.logout();
            utils_index.navigateToPage({
              url: "/pages/login/index"
            });
          }
        })
      });
    };
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.unref(avatarUrl),
        b: common_vendor.p({
          name: "camera",
          size: "20rpx",
          color: "#fff"
        }),
        c: common_vendor.o(handleAvatarClick),
        d: common_vendor.t(common_vendor.unref(userStore).userInfo.nickName || "未设置昵称"),
        e: common_vendor.t(common_vendor.unref(userStore).userInfo.phone || "未绑定手机"),
        f: common_vendor.t(common_vendor.unref(userStore).userInfo.companyName || "未设置企业名称"),
        g: common_vendor.p({
          name: "edit",
          size: "32rpx",
          color: "#667eea"
        }),
        h: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        i: common_vendor.o(goToEditProfile),
        j: common_vendor.p({
          name: "lock-on",
          size: "32rpx",
          color: "#667eea"
        }),
        k: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        l: common_vendor.o(goToChangePassword),
        m: common_vendor.p({
          name: "help",
          size: "32rpx",
          color: "#667eea"
        }),
        n: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        o: common_vendor.o(goToHelpCenter),
        p: common_vendor.p({
          name: "edit",
          size: "32rpx",
          color: "#667eea"
        }),
        q: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        r: common_vendor.o(goToFeedback),
        s: common_vendor.p({
          name: "user-talk",
          size: "32rpx",
          color: "#667eea"
        }),
        t: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        v: common_vendor.o(goToAbout),
        w: common_vendor.p({
          name: "error-circle",
          size: "32rpx",
          color: "#667eea"
        }),
        x: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        y: common_vendor.o(goToPrivacyPolicy),
        z: common_vendor.p({
          name: "gift",
          size: "32rpx",
          color: "#667eea"
        }),
        A: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        B: common_vendor.o(goToTermsOfService),
        C: common_vendor.p({
          name: "logout",
          size: "32rpx",
          color: "#f56c6c"
        }),
        D: common_vendor.p({
          name: "arrow-right",
          size: "24rpx",
          color: "#c0c4cc"
        }),
        E: common_vendor.o(handleLogout),
        F: common_vendor.o(closeAvatarCropper),
        G: common_vendor.p({
          name: "close",
          size: "32rpx"
        }),
        H: !selectedImagePath.value
      }, !selectedImagePath.value ? {
        I: common_vendor.p({
          name: "image",
          size: "48rpx",
          color: "#c0c4cc"
        })
      } : {
        J: selectedImagePath.value,
        K: common_vendor.o(onImageLoad),
        L: common_vendor.o(onImageError)
      }, {
        M: imageLoadError.value
      }, imageLoadError.value ? {
        N: common_vendor.p({
          name: "error-circle",
          size: "32rpx",
          color: "#f56c6c"
        })
      } : {}, {
        O: common_vendor.o(closeAvatarCropper),
        P: common_vendor.p({
          type: "default",
          size: "large",
          ["custom-class"]: "cancel-btn"
        }),
        Q: common_vendor.t(uploading.value ? "上传中..." : "确认上传"),
        R: common_vendor.o(uploadAvatar),
        S: common_vendor.p({
          type: "primary",
          size: "large",
          ["custom-class"]: "upload-btn",
          loading: uploading.value
        }),
        T: common_vendor.o(($event) => showAvatarCropper.value = $event),
        U: common_vendor.p({
          position: "bottom",
          ["safe-area-inset-bottom"]: true,
          modelValue: showAvatarCropper.value
        })
      });
    };
  }
});
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-f97f9319"]]);
wx.createPage(MiniProgramPage);
