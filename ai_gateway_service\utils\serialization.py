"""
Utility functions for data serialization
"""

import json
import decimal
from typing import Any


class DecimalEncoder(json.JSONEncoder):
    """JSON encoder that can handle Decimal objects"""
    
    def default(self, obj: Any) -> Any:
        if isinstance(obj, decimal.Decimal):
            return float(obj)
        return super().default(obj)


def safe_json_dumps(data: Any, **kwargs) -> str:
    """Safely serialize data to JSON, handling Decimal objects"""
    return json.dumps(data, cls=DecimalEncoder, ensure_ascii=False, **kwargs)