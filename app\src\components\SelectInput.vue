<script lang="ts" setup>
import { ref, computed, watch } from 'vue'

// 选项类型定义
interface SelectOption {
  value: string
  label: string
  description?: string
  [key: string]: any // 允许其他自定义属性
}

// 导出类型供外部使用
export type { SelectOption }

// 组件属性
interface Props {
  modelValue?: string
  label?: string
  placeholder?: string
  labelWidth?: string
  required?: boolean
  disabled?: boolean
  clearable?: boolean
  options?: SelectOption[]
  filterKey?: string // 用于过滤的字段名，默认是 'label'
  maxResults?: number // 最大显示结果数量
  minChars?: number // 最少输入字符数才显示下拉
}

// 组件事件
interface Emits {
  (e: 'update:modelValue', value: string): void
  (e: 'change', value: string, option?: SelectOption): void
  (e: 'search', keyword: string): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  label: '',
  placeholder: '请输入或选择',
  labelWidth: '160rpx',
  required: false,
  disabled: false,
  clearable: true,
  options: () => [],
  filterKey: 'label',
  maxResults: 8,
  minChars: 1,
})

const emit = defineEmits<Emits>()

// 响应式数据
const inputValue = ref('')
const showDropdown = ref(false)
const selectedOption = ref<SelectOption | null>(null)
const filteredOptions = ref<SelectOption[]>([])
const inputFocused = ref(false)

// 计算属性
const currentValue = computed({
  get: () => props.modelValue,
  set: (value: string) => {
    emit('update:modelValue', value)
  }
})

// 显示的输入值
const displayValue = computed({
  get: () => {
    return inputValue.value
  },
  set: (value: string) => {
    inputValue.value = value
  }
})

// 是否显示推荐下拉框
const shouldShowDropdown = computed(() => {
  return showDropdown.value && 
         filteredOptions.value.length > 0 && 
         inputFocused.value
})

// 获取用于对比的标签文本
function getOptionLabel(option: SelectOption): string {
  return option[props.filterKey] || option.label || option.value
}

// 过滤选项列表
function filterOptions(keyword: string) {
  // 如果关键词为空或长度不足，显示所有选项
  if (!keyword.trim() || keyword.length < props.minChars) {
    filteredOptions.value = props.options.slice(0, props.maxResults)
    return
  }
  
  const lowerKeyword = keyword.toLowerCase()
  filteredOptions.value = props.options.filter(option => {
    const label = getOptionLabel(option)
    return label.toLowerCase().includes(lowerKeyword)
  }).slice(0, props.maxResults)
  
  // 触发搜索事件，允许父组件动态加载数据
  emit('search', keyword)
}

// 输入框获得焦点
function onInputFocus() {
  inputFocused.value = true
  
  // 获取焦点时立即显示所有选项
  filterOptions('')
  showDropdown.value = true
}

// 输入框失去焦点
function onInputBlur() {
  // 延迟隐藏下拉框，以便点击选项
  setTimeout(() => {
    inputFocused.value = false
    showDropdown.value = false
  }, 200)
}

// 输入内容变化
function onInputChange(value: string) {
  inputValue.value = value
  currentValue.value = value
  
  if (value.trim() && value.length >= props.minChars) {
    filterOptions(value)
    showDropdown.value = true
    // 确保在输入时设置焦点状态
    if (!inputFocused.value) {
      inputFocused.value = true
    }
  } else if (inputFocused.value) {
    // 如果有焦点但输入为空，显示所有选项
    filterOptions('')
    showDropdown.value = true
  } else {
    filteredOptions.value = []
    showDropdown.value = false
  }
  
  // 如果输入值不匹配任何选项，清空选中状态
  const matchedOption = props.options.find(option => 
    getOptionLabel(option) === value
  )
  
  if (!matchedOption) {
    selectedOption.value = null
  } else {
    selectedOption.value = matchedOption
  }
  
  emit('change', value, selectedOption.value)
}

// 选择推荐选项
function selectOption(option: SelectOption) {
  selectedOption.value = option
  const optionLabel = getOptionLabel(option)
  inputValue.value = optionLabel
  currentValue.value = optionLabel // v-model 绑定显示的文本，不是 value
  showDropdown.value = false
  inputFocused.value = false
  
  emit('change', optionLabel, option)
}

// 清空选择
function clearSelection() {
  selectedOption.value = null
  inputValue.value = ''
  currentValue.value = ''
  filteredOptions.value = []
  showDropdown.value = false
  
  emit('change', '', null)
}

// 监听 modelValue 变化，同步输入框显示
watch(() => props.modelValue, (newValue) => {
  if (newValue !== inputValue.value) {
    // 查找对应的选项
    const option = props.options.find(opt => opt.value === newValue)
    if (option) {
      selectedOption.value = option
      inputValue.value = getOptionLabel(option)
    } else {
      selectedOption.value = null
      inputValue.value = newValue || ''
    }
  }
}, { immediate: true })

// 监听选项变化，重新过滤
watch(() => props.options, () => {
  if (inputValue.value.trim() && showDropdown.value) {
    filterOptions(inputValue.value)
  }
}, { deep: true })
</script>

<template>
  <view 
    class="select-input-wrapper" 
    :class="{ 'dropdown-active': shouldShowDropdown }"
    :style="{ '--label-width': label ? labelWidth : '0rpx' }"
  >
    <wd-input
      :model-value="displayValue"
      :label="label"
      :label-width="labelWidth"
      :placeholder="placeholder"
      :required="required"
      :disabled="disabled"
      :clearable="clearable"
      @update:model-value="onInputChange"
      @focus="onInputFocus"
      @blur="onInputBlur"
      @clear="clearSelection"
    />

    <!-- 推荐下拉框 -->
    <view v-if="shouldShowDropdown" class="dropdown-container">
      <view class="dropdown-content">
        <view 
          v-for="option in filteredOptions" 
          :key="option.value" 
          class="dropdown-item"
          @click="selectOption(option)"
        >
          <text class="option-label">{{ getOptionLabel(option) }}</text>
          <text v-if="option.description" class="option-description">
            {{ option.description }}
          </text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 基础变量
$primary-color: #667eea;
$text-primary: #303133;
$text-secondary: #606266;
$text-light: #909399;
$border-color: #dcdfe6;
$bg-white: #ffffff;
$bg-dropdown: #f8f9fa;
$box-shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
$box-shadow-dropdown: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
$transition-base: all 0.3s ease;
$font-size-medium: 28rpx;
$font-size-small: 24rpx;

.select-input-wrapper {
  position: relative;
  z-index: 1; // 基础层级
  
  // 当显示下拉框时，提升整个组件的层级
  &.dropdown-active {
    z-index: 9998;
  }
  
  .dropdown-container {
    position: absolute;
    top: calc(100% + 4rpx);
    left: var(--label-width, 160rpx);
    right: 0;
    z-index: 9999; // 非常高的 z-index，确保在所有其他元素之上
    background: $bg-dropdown;
    border: 1rpx solid $border-color;
    border-radius: 8rpx;
    box-shadow: $box-shadow-dropdown;
    max-height: 400rpx;
    overflow-y: auto;
    
    .dropdown-content {
      .dropdown-item {
        padding: 24rpx 32rpx;
        border-bottom: 1rpx solid #e5e7eb;
        cursor: pointer;
        transition: $transition-base;
        background: $bg-white;
        
        &:hover {
          background-color: #f0f9ff;
          border-left: 3rpx solid $primary-color;
        }
        
        &:first-child {
          border-radius: 8rpx 8rpx 0 0;
        }
        
        &:last-child {
          border-bottom: none;
          border-radius: 0 0 8rpx 8rpx;
        }
        
        .option-label {
          font-size: $font-size-medium;
          color: $text-primary;
          line-height: 1.4;
          display: block;
          font-weight: 500;
        }
        
        .option-description {
          font-size: $font-size-small;
          color: $text-secondary;
          margin-top: 8rpx;
          display: block;
          line-height: 1.3;
        }
      }
    }
  }
}

</style>
