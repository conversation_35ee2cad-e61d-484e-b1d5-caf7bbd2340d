from sqlalchemy import Column, String, DateTime, BigInteger
from modules.database import Base

class Commodity(Base):
    """
    SQLAlchemy ORM model for commodity.
    商品表 - 期货品种基础信息
    """
    __tablename__ = "commodities"

    # 模拟 global.GVA_MODEL 的字段
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    created_at = Column(DateTime, comment="创建时间")
    updated_at = Column(DateTime, comment="更新时间")
    deleted_at = Column(DateTime, index=True)

    # 商品信息字段
    name = Column(String(50), nullable=False, unique=True, comment="商品名称")
    product_id = Column(String(20), nullable=False, unique=True, comment="品种ID")
    exchange_id = Column(String(20), nullable=False, comment="所属交易所ID")
    section = Column(String(50), index=True, comment="商品版块")

    def __repr__(self):
        return f"<Commodity(product_id='{self.product_id}', name='{self.name}', exchange='{self.exchange_id}')>"
