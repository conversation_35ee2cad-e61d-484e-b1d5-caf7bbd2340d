package dianjia

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Commodity 商品表 - 期货品种基础信息
type Commodity struct {
	global.GVA_MODEL
	Name       string    `json:"name" gorm:"comment:商品名称;size:50;not null" validate:"required"`
	ProductID  string    `json:"product_id" gorm:"comment:品种ID;size:20;not null;uniqueIndex" validate:"required"`
	ExchangeID string    `json:"exchange_id" gorm:"comment:所属交易所ID;size:20;not null" validate:"required"`
	Section    string    `json:"section" gorm:"comment:商品版块;size:50;index" validate:""`
	CreatedAt  time.Time `json:"created_at" gorm:"comment:创建时间"`
	UpdatedAt  time.Time `json:"updated_at" gorm:"comment:更新时间"`
}

// TableName 指定表名
func (Commodity) TableName() string {
	return "commodities"
}

// CommodityRequest 创建和更新商品的请求结构
type CommodityRequest struct {
	ID         uint   `json:"id"`
	Name       string `json:"name" validate:"required" label:"商品名称"`
	ProductID  string `json:"product_id" validate:"required" label:"品种ID"`
	ExchangeID string `json:"exchange_id" validate:"required" label:"交易所ID"`
	Section    string `json:"section" validate:"" label:"商品版块"`
}

// CommodityResponse 商品响应结构
type CommodityResponse struct {
	ID              uint      `json:"id"`
	Name            string    `json:"name"`
	ProductID       string    `json:"product_id"`
	ExchangeID      string    `json:"exchange_id"`
	Section         string    `json:"section"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
	InstrumentCount int       `json:"instrument_count,omitempty"` // 关联的合约数量
}

// CommodityListRequest 商品列表查询请求
type CommodityListRequest struct {
	Page       int    `json:"page" form:"page"`
	PageSize   int    `json:"pageSize" form:"pageSize"`
	Name       string `json:"name" form:"name"`
	ProductID  string `json:"product_id" form:"product_id"`
	ExchangeID string `json:"exchange_id" form:"exchange_id"`
	Section    string `json:"section" form:"section"`
}

// CommodityListResponse 商品列表响应
type CommodityListResponse struct {
	List     []CommodityResponse `json:"list"`
	Total    int64               `json:"total"`
	Page     int                 `json:"page"`
	PageSize int                 `json:"pageSize"`
}
