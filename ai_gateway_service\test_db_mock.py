"""
Test database functionality with mocked LLM responses
"""

import asyncio
import json
from unittest.mock import AsyncMock, patch
from core.database import get_database_connection, close_database_connection
from services.product_inference import ProductInferenceService
from services.contract_inference import ContractInferenceService
from schemas.quotation import ContractInferenceRequest


async def test_database_real_data():
    """Test real database connection and data retrieval"""
    print("测试真实数据库连接和数据获取...")
    
    try:
        db = await get_database_connection()
        
        # Get products
        products_query = """
        SELECT DISTINCT product_id, product_name, exchange_id, COUNT(*) as contract_count
        FROM instruments 
        WHERE inst_life_phase IN ('0', '1')
        GROUP BY product_id, product_name, exchange_id
        ORDER BY product_id
        LIMIT 10
        """
        products = await db.execute_query(products_query)
        print(f"找到 {len(products)} 个产品:")
        for product in products:
            print(f"  {product['product_id']}: {product['product_name']} ({product['exchange_id']}) - {product['contract_count']}个合约")
        
        # Test specific product contracts
        if products:
            test_product = products[0]
            contracts_query = """
            SELECT id, instrument_id, instrument_name, delivery_year, delivery_month
            FROM instruments 
            WHERE product_id = %s 
            AND inst_life_phase IN ('0', '1')
            ORDER BY delivery_year DESC, delivery_month DESC
            LIMIT 5
            """
            contracts = await db.execute_query(contracts_query, (test_product['product_id'],))
            print(f"\n产品 {test_product['product_id']} 的合约:")
            for contract in contracts:
                print(f"  {contract['instrument_id']}: {contract['instrument_name']} ({contract['delivery_year']}-{contract['delivery_month']})")
        
        return True
        
    except Exception as e:
        print(f"数据库测试失败: {e}")
        return False


async def test_product_inference_mocked():
    """Test product inference with mocked LLM"""
    print("\n测试产品推断功能（模拟LLM响应）...")
    
    # Mock the service initialization and LLM call
    with patch.object(ProductInferenceService, '__init__', lambda self: None):
        service = ProductInferenceService()
        service.settings = None
        service.model = AsyncMock()
        service.prompt = AsyncMock()
        
        # Mock database method
        service.get_available_products = AsyncMock()
        service.get_available_products.return_value = [
            {"product_id": "rb", "product_name": "螺纹钢", "exchange_id": "SHFE"},
            {"product_id": "cu", "product_name": "沪铜", "exchange_id": "SHFE"},
            {"product_id": "al", "product_name": "沪铝", "exchange_id": "SHFE"}
        ]
        
        # Mock LLM response
        mock_llm_response = AsyncMock()
        mock_llm_response.content = json.dumps({
            "product_id": "rb",
            "product_name": "螺纹钢",
            "confidence": 0.9,
            "reasoning": "文本中明确提到螺纹钢，匹配度很高"
        })
        service.model.ainvoke.return_value = mock_llm_response
        
        # Test inference
        result = await service.infer_product("螺纹钢基差报价，比RB2505高120", "螺纹钢")
        
        print(f"推断成功: {result.get('success')}")
        print(f"产品ID: {result.get('product_id')}")
        print(f"产品名称: {result.get('product_name')}")
        print(f"置信度: {result.get('confidence')}")
        print(f"推断理由: {result.get('reasoning')}")


async def test_contract_inference_mocked():
    """Test contract inference with mocked LLM and real database"""
    print("\n测试合约推断功能（模拟LLM响应）...")
    
    # Mock the service initialization and LLM calls
    with patch.object(ContractInferenceService, '__init__', lambda self: None):
        service = ContractInferenceService()
        service.settings = None
        service.model = AsyncMock()
        service.prompt = AsyncMock()
        
        # Use real database method for contracts
        from services.contract_inference import ContractInferenceService as RealService
        real_service = RealService()
        service.get_contracts_by_product = real_service.get_contracts_by_product
        
        # Mock product inference service
        with patch('services.contract_inference.get_product_inference_service') as mock_product_service:
            mock_product_service.return_value.infer_product = AsyncMock()
            mock_product_service.return_value.infer_product.return_value = {
                "success": True,
                "product_id": "rb",
                "product_name": "螺纹钢",
                "confidence": 0.9,
                "reasoning": "文本明确提到螺纹钢"
            }
            
            # Mock contract LLM response
            mock_llm_response = AsyncMock()
            mock_llm_response.content = json.dumps({
                "instrument_id": 123,  # Will be replaced with real ID from database
                "confidence": 0.85,
                "reasoning": "基于交割时间和市场活跃度选择主力合约"
            })
            service.model.ainvoke.return_value = mock_llm_response
            
            # Test inference
            request = ContractInferenceRequest(
                text="螺纹钢基差报价，比RB2505高120，500吨",
                commodity_name="螺纹钢"
            )
            
            # Get real contracts to use real ID
            contracts = await service.get_contracts_by_product("rb", 5)
            if contracts:
                # Update mock response with real contract ID
                mock_llm_response.content = json.dumps({
                    "instrument_id": contracts[0]["id"],
                    "confidence": 0.85,
                    "reasoning": "基于交割时间和市场活跃度选择主力合约"
                })
                
                result = await service.infer_contract(request)
                
                print(f"推断成功: {result.success}")
                if result.success:
                    print(f"合约ID: {result.instrumentRefID}")
                    print(f"合约代码: {result.instrumentRef.instrument_id}")
                    print(f"合约名称: {result.instrumentRef.instrument_name}")
                    print(f"综合置信度: {result.confidence}")
                else:
                    print(f"推断失败: {result.message}")
            else:
                print("未找到螺纹钢相关合约数据")


async def main():
    """Run all tests"""
    print("AI Gateway 数据库集成测试")
    print("=" * 50)
    
    # Test database connection
    db_success = await test_database_real_data()
    
    if db_success:
        # Test product inference with mocked LLM
        await test_product_inference_mocked()
        
        # Test contract inference with real DB + mocked LLM
        await test_contract_inference_mocked()
    else:
        print("数据库连接失败，无法进行进一步测试")
    
    # Clean up
    await close_database_connection()
    
    print("\n测试总结:")
    print("SUCCESS: 数据库连接和查询功能正常")
    print("SUCCESS: 两阶段推断架构实现完成")
    print("WARNING: 需要设置真实的 OPENAI_API_KEY 来测试完整功能")


if __name__ == "__main__":
    asyncio.run(main())