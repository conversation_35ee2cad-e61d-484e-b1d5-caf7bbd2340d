<view class="{{['basis-display', 'data-v-80bbf42c', n && 'flash-bg']}}"><transition wx:if="{{m}}" class="data-v-80bbf42c" u-s="{{['d']}}" u-i="80bbf42c-0" bind:__l="__l" u-p="{{m}}"><block wx:if="{{a}}"><view key="basis" class="basis-content data-v-80bbf42c"><text class="contract-name data-v-80bbf42c">{{b}}</text><text class="basis-value adaptive-price data-v-80bbf42c" style="{{'font-size:' + d}}" data-positive="{{e}}" data-negative="{{f}}" title="{{g}}">{{c}}</text></view></block><block wx:else><view key="final" class="final-content data-v-80bbf42c"><block wx:if="{{h}}"><text class="loading-text data-v-80bbf42c">获取价格中...</text></block><block wx:elif="{{i}}"><text class="error-text data-v-80bbf42c">未获取到期货价格</text></block><block wx:else><text class="final-price-label data-v-80bbf42c">最终价格</text><text class="final-price-value adaptive-price data-v-80bbf42c" style="{{'font-size:' + k}}" title="{{l}}">{{j}}</text></block></view></block></transition></view>