package config

type AliyunSMS struct {
	AccessKeyId  string `mapstructure:"access_key_id" json:"access_key_id" yaml:"access_key_id"`
	AccessSecret string `mapstructure:"access_secret" json:"access_secret" yaml:"access_secret"`
	SignName     string `mapstructure:"sign_name" json:"sign_name" yaml:"sign_name"`
	TemplateCode string `mapstructure:"template_code" json:"template_code" yaml:"template_code"`
	ExpireTime   int    `mapstructure:"expire_time" json:"expire_time" yaml:"expire_time"`
}