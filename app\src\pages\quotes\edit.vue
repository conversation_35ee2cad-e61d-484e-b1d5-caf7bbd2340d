<route lang="jsonc" type="page">{
  "layout": "default",
  "style": {
    "navigationBarTitleText": "编辑报价"
  }
}</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { onLoad } from '@dcloudio/uni-app'

import {
  createQuotation,
  updateQuotation,
  getQuotationDetail,
  saveQuotationDraft,
  publishQuotation as publishQuotationAPI
} from '@/api/quotation'
import type {
  ICreateQuotationRequest,
  IUpdateQuotationRequest,
  IQuotationResponse,
  QuotationPriceType,
  QuotationStatus,
  IQuotationExpiryOption,
  IInstrumentSelectItem
} from '@/types'
import InstrumentSelector from '@/components/InstrumentSelector.vue'
import SelectInput, { type SelectOption } from '@/components/SelectInput.vue'
import { useCommodityStore } from '@/store/commodity'
import { navigateBackOrTo } from '@/utils'

defineOptions({
  name: 'QuotationEdit'
})


const commodityStore = useCommodityStore()

// 页面状态
const isEdit = ref(false) // 是否为编辑模式
const quotationId = ref<number>()
const isLoading = ref(false)
const isSubmitting = ref(false)

// 表单数据
const formData = ref<ICreateQuotationRequest>({
  title: '',
  commodityName: '',
  deliveryLocation: '',
  brand: '',
  specifications: '',
  description: '',
  isBuyRequest: false, // 默认为出售
  priceType: 'Fixed',
  price: 0,
  instrumentRefID: undefined,
  expiresAt: '',
  status: 'Draft'
})

// 选择器数据 - 现在由组件内部管理
const selectedInstrument = ref<IInstrumentSelectItem>()

// 选择器引用
const expiryPicker = ref()
const customDatePicker = ref()
const formRef = ref()

// 商品选项
const commodityOptions = computed(() => {
  return commodityStore.commodityList.map(item => ({
    value: item.name,
    label: item.name,
    description: item.product_id
  }))
})

// 交货地点和品牌选项（暂时设为空）
const deliveryLocationOptions = ref<SelectOption[]>([])
const brandOptions = ref<SelectOption[]>([])

// 有效期选项
const expiryOptions = ref<IQuotationExpiryOption[]>([
  {
    label: '当日有效',
    value: 'today',
    hours: 24,
    description: '到今日23:59过期'
  },
  {
    label: '3天内有效',
    value: '3days',
    hours: 72,
    description: '72小时后过期'
  },
  {
    label: '1周内有效',
    value: '1week',
    hours: 168,
    description: '7天后过期'
  },
  {
    label: '2周内有效',
    value: '2weeks',
    hours: 336,
    description: '14天后过期'
  },
  {
    label: '1个月内有效',
    value: '1month',
    hours: 720,
    description: '30天后过期'
  },
  {
    label: '自定义',
    value: 'custom',
    hours: 0,
    description: '请选择具体的过期时间'
  }
])

const selectedExpiryOption = ref('3days')
const customExpiryDate = ref('')

// 交易意向计算属性
const tradeIntentionValue = computed({
  get: () => formData.value.isBuyRequest ? 'buy' : 'sell',
  set: (value: 'buy' | 'sell') => {
    formData.value.isBuyRequest = value === 'buy'
  }
})

// 表单验证现在由 wot-design-uni 的 wd-form 组件处理

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入报价标题' }
  ],
  commodityName: [
    { required: true, message: '请输入商品名称' }
  ],
  deliveryLocation: [
    { required: true, message: '请输入交货地点' }
  ],
  // 价格验证现在动态处理
  expiresAt: [
    { required: true, message: '请设置有效期' }
  ]
}

// 是否显示期货合约选择
const showInstrumentSelector = computed(() => {
  return formData.value.priceType === 'Basis'
})

// 是否显示价格输入
const showPriceInput = computed(() => {
  return formData.value.priceType !== 'Negotiable'
})

// 价格验证规则动态调整
const priceValidationRule = computed(() => {
  if (formData.value.priceType === 'Negotiable') {
    return [] // 商议类型不需要验证价格
  }
  return [{ required: true, message: '请输入价格' }]
})

// 生命周期
onLoad((options) => {
  if (options?.id) {
    quotationId.value = parseInt(options.id)
    isEdit.value = true
    loadQuotationDetail()
  }

  // 设置默认有效期
  updateExpiryTime()
})

onMounted(() => {
  // 基础数据现在由组件内部加载
  commodityStore.loadCommodityList()
})

// 加载报价详情（编辑模式）
async function loadQuotationDetail() {
  if (!quotationId.value) return

  try {
    isLoading.value = true
    const res = await getQuotationDetail(quotationId.value)
    const quotation = res.data

    // 填充表单数据，确保类型正确
    formData.value = {
      title: quotation.title,
      commodityName: quotation.commodityName,
      deliveryLocation: quotation.deliveryLocation,
      brand: quotation.brand || '',
      specifications: quotation.specifications || '',
      description: quotation.description || '',
      isBuyRequest: quotation.isBuyRequest || false,
      priceType: typeof quotation.priceType === 'string' ? quotation.priceType as QuotationPriceType : 'Fixed',
      price: quotation.price,
      instrumentRefID: quotation.instrumentRefID,
      expiresAt: quotation.expiresAt
    }

    // 设置选中的期货合约
    selectedInstrument.value = quotation.instrumentRef

    // 设置自定义过期时间
    selectedExpiryOption.value = 'custom'
    customExpiryDate.value = quotation.expiresAt

    uni.setNavigationBarTitle({
      title: '编辑报价'
    })

  } catch (error) {
    console.error('加载报价详情失败:', error)
    uni.showToast({
      title: '加载报价失败',
      icon: 'error'
    })
  } finally {
    isLoading.value = false
  }
}

// 更新过期时间
function updateExpiryTime() {
  if (selectedExpiryOption.value === 'custom') {
    return // 自定义时间由用户选择
  }

  const option = expiryOptions.value.find(opt => opt.value === selectedExpiryOption.value)
  if (option) {
    const now = new Date()
    const expiryTime = new Date(now.getTime() + option.hours * 60 * 60 * 1000)
    formData.value.expiresAt = expiryTime.toISOString()
  }
}


// 期货合约选择变化
function onInstrumentChange(instrumentId: number, instrument: IInstrumentSelectItem | null) {
  formData.value.instrumentRefID = instrumentId || undefined
  selectedInstrument.value = instrument || undefined
}



// 价格类型 tab 改变
function onPriceTypeTabChange(event: any) {
  const priceType = event.name as QuotationPriceType
  // 更新表单数据
  formData.value.priceType = priceType

  // 处理副作用
  if (priceType === 'Fixed') {
    formData.value.instrumentRefID = undefined
    selectedInstrument.value = undefined
  }
}

// 有效期选项改变
function onExpiryOptionChange(value: any) {
  // 处理 wd-picker 的返回值
  const selectedValue = typeof value === 'string' ? value : value?.value || value?.detail?.value
  selectedExpiryOption.value = selectedValue

  if (selectedExpiryOption.value !== 'custom') {
    updateExpiryTime()
  }
}

// 自定义过期时间改变
function onCustomExpiryChange(value: any) {
  // 处理日期时间选择器的返回值
  const dateValue = typeof value === 'string' ? value : value?.detail?.value || value?.value
  customExpiryDate.value = dateValue

  if (dateValue) {
    formData.value.expiresAt = new Date(dateValue).toISOString()
  }
}



// 保存草稿
async function saveDraft() {
  // 使用 wot-design-uni 表单验证
  try {
    const { valid } = await formRef.value.validate()
    if (!valid) {
      return
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }

  try {
    isSubmitting.value = true

    // 准备请求数据，确保数据类型正确
    const requestData = {
      ...formData.value,
      price: typeof formData.value.price === 'string' ? parseFloat(formData.value.price) || 0 : formData.value.price
    }

    if (isEdit.value && quotationId.value) {
      // 更新报价
      const updateData: IUpdateQuotationRequest = {
        id: quotationId.value,
        ...requestData
      }
      await updateQuotation(updateData)
    } else {
      // 创建草稿
      await saveQuotationDraft(requestData)
    }

    uni.showToast({
      title: '保存成功',
      icon: 'success'
    })

    // 延迟返回上一页，如果没有上一页则跳转到报价管理页面
    setTimeout(() => {
      navigateBackOrTo('/pages/quotes/my-list')
    }, 1500)

  } catch (error) {
    console.error('保存草稿失败:', error)
    uni.showToast({
      title: '保存失败',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}

// 直接发布
async function publishQuotation() {
  // 使用 wot-design-uni 表单验证
  try {
    const { valid } = await formRef.value.validate()
    if (!valid) {
      return
    }
  } catch (error) {
    console.error('表单验证失败:', error)
    return
  }

  try {
    isSubmitting.value = true

    // 准备请求数据，确保数据类型正确
    const requestData = {
      ...formData.value,
      price: typeof formData.value.price === 'string' ? parseFloat(formData.value.price) || 0 : formData.value.price
    }

    if (isEdit.value && quotationId.value) {
      // 更新报价数据
      const updateData: IUpdateQuotationRequest = {
        id: quotationId.value,
        ...requestData
      }
      await updateQuotation(updateData)

      // 发布报价（设置为激活状态并设置过期时间）
      await publishQuotationAPI({
        id: quotationId.value,
        expiresAt: requestData.expiresAt
      })
    } else {
      // 创建并直接发布
      const createData = {
        ...requestData,
        status: 'Active' as QuotationStatus
      }
      await saveQuotationDraft(createData)
    }

    uni.showToast({
      title: '发布成功',
      icon: 'success'
    })

    // 延迟返回上一页，如果没有上一页则跳转到报价管理页面
    setTimeout(() => {
      navigateBackOrTo('/pages/quotes/my-list')
    }, 1500)

  } catch (error) {
    console.error('发布报价失败:', error)
    uni.showToast({
      title: '发布失败',
      icon: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <view class="app-container">
    <view class="app-content">

      <!-- 加载状态 -->
      <view v-if="isLoading" class="loading-container animate-fade-in">
        <wd-loading type="ring" :color="'var(--app-color-primary)'" />
        <text class="app-text-body mt-2">加载中...</text>
      </view>

      <!-- 表单内容 -->
      <view v-else class="form-container animate-slide-up">
      <wd-form ref="formRef" :model="formData" :rules="formRules">
        <!-- 基本信息 -->
        <wd-cell-group title="基本信息" border custom-class="form-section">
          <!-- 交易类型 -->
          <wd-cell title="交易意向" required>
            <wd-radio-group
              v-model="tradeIntentionValue"
              inline
              shape="dot"
              custom-class="trade-intention-radio-group"
            >
              <wd-radio value="sell" custom-class="trade-intention-radio">出售</wd-radio>
              <wd-radio value="buy" custom-class="trade-intention-radio">求购</wd-radio>
            </wd-radio-group>
          </wd-cell>
          <!-- 报价标题 -->
          <wd-input
            v-model="formData.title"
            label="报价标题"
            label-width="160rpx"
            placeholder="请输入醒目的报价标题"
            required
            clearable
            :maxlength="20"
            show-word-limit
            prop="title"
          />

          <!-- 商品名称 -->
          <SelectInput
            v-model="formData.commodityName"
            label="商品名称"
            label-width="160rpx"
            placeholder="请输入或选择商品名称"
            required
            clearable
            :options="commodityOptions"
            prop="commodityName"
          />

          <!-- 交货地点 -->
          <SelectInput
            v-model="formData.deliveryLocation"
            label="交货地点"
            label-width="160rpx"
            placeholder="请输入或选择交货地点"
            required
            clearable
            :options="deliveryLocationOptions"
            prop="deliveryLocation"
          />

          <!-- 品牌 -->
          <SelectInput
            v-model="formData.brand"
            label="品牌"
            label-width="160rpx"
            placeholder="请输入或选择品牌（可选）"
            clearable
            :options="brandOptions"
          />

          <!-- 规格说明 -->
          <wd-textarea
            v-model="formData.specifications"
            label="规格说明"
            label-width="160rpx"
            placeholder="请输入规格说明（可选）"
            :maxlength="50"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>

        <!-- 价格信息 -->
        <wd-cell-group title="价格信息" border custom-class="form-section">
          <!-- 价格类型 -->
          <view class="price-type-section">
            <wd-tabs
              v-model="formData.priceType"
              @change="onPriceTypeTabChange"
              custom-class="price-type-tabs"
            >
              <wd-tab title="一口价" name="Fixed"></wd-tab>
              <wd-tab title="基差报价" name="Basis"></wd-tab>
              <wd-tab title="商议" name="Negotiable"></wd-tab>
            </wd-tabs>
          </view>

          <!-- 期货合约选择（基差报价时显示） -->
          <InstrumentSelector
            v-if="showInstrumentSelector"
            v-model="formData.instrumentRefID"
            label="期货合约"
            label-width="160rpx"
            placeholder="请选择期货合约"
            required
            @change="onInstrumentChange"
          />

          <!-- 价格 -->
          <wd-input
            v-if="showPriceInput"
            v-model="formData.price"
            :label="formData.priceType === 'Fixed' ? '价格' : '基差值'"
            label-width="160rpx"
            :placeholder="formData.priceType === 'Fixed' ? '请输入价格' : '请输入基差值'"
            type="digit"
            required
            prop="price"
            :rules="priceValidationRule"
          >
            <template #suffix>
              <text class="price-unit">{{ formData.priceType === 'Fixed' ? '元' : '点' }}</text>
            </template>
          </wd-input>
          
          <!-- 商议类型提示 -->
          <wd-cell 
            v-else
            title="价格"
            value="价格面议，可通过沟通协商"
            label=""
          />
        </wd-cell-group>

        <!-- 有效期设置 -->
        <wd-cell-group title="有效期设置" border custom-class="form-section">
          <!-- 有效期选择器 -->
          <wd-picker
            label="有效期"
            label-width="160rpx"
            placeholder="请选择有效期"
            ref="expiryPicker"
            v-model="selectedExpiryOption"
            :columns="expiryOptions"
            label-key="label"
            value-key="value"
            @confirm="onExpiryOptionChange"
            required
            prop="expiresAt"
          />

          <!-- 自定义日期时间选择器 -->
          <wd-datetime-picker
            v-if="selectedExpiryOption === 'custom'"
            label="自定义过期时间"
            label-width="160rpx"
            ref="customDatePicker"
            v-model="customExpiryDate"
            type="date"
            @confirm="onCustomExpiryChange"
            required
          />

          <!-- 补充说明 -->
          <wd-textarea
            v-model="formData.description"
            label="补充说明"
            label-width="160rpx"
            placeholder="请输入补充说明（可选）"
            :maxlength="200"
            show-word-limit
            :auto-height="true"
          />
        </wd-cell-group>
      </wd-form>
      </view>

      <!-- 底部操作按钮 -->
      <view class="form-actions">
        <wd-button
          type="info"
          custom-class="btn-draft"
          :loading="isSubmitting"
          @click="saveDraft"
        >
          {{ isEdit ? '保存修改' : '保存草稿' }}
        </wd-button>

        <wd-button
          type="primary"
          custom-class="btn-publish"
          :loading="isSubmitting"
          @click="publishQuotation"
        >
          {{ isEdit ? '更新并发布' : '直接发布' }}
        </wd-button>
      </view>

    </view>
  </view>
</template>

<style lang="scss" scoped>
// 报价编辑页面样式 - 使用新的统一样式系统

// 页面基本布局
.app-container {
  padding-bottom: calc(200rpx + env(safe-area-inset-bottom, 0px)); // 适配底部按钮和安全区域
}

// 加载状态
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

// 表单容器
.form-container {
  padding: 0 var(--app-spacing-sm) var(--app-spacing-xl); // 使用新的间距变量
  overflow-y: auto;
}

// 表单分区样式
:deep(.form-section) {
  margin-bottom: var(--app-spacing-sm);
  border-radius: var(--app-radius-md);
  box-shadow: var(--app-shadow-base);
}

// 价格单位样式
.price-unit {
  color: var(--app-text-secondary);
  font-size: var(--app-font-size-xs);
  margin-left: var(--app-spacing-xs);
}

// 底部操作按钮
.form-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  gap: var(--app-spacing-sm);
  padding: var(--app-spacing-sm);
  padding-bottom: calc(var(--app-spacing-sm) + env(safe-area-inset-bottom, 0px));
  background: var(--app-bg-primary);
  border-top: 1rpx solid var(--app-border-secondary);
  box-shadow: var(--app-shadow-lg);
  z-index: var(--app-z-fixed);
  
  /* #ifndef MP-WEIXIN */
  backdrop-filter: blur(20rpx);
  /* #endif */

  :deep(.wd-button) {
    flex: 1;
    height: 88rpx;
    border-radius: var(--app-radius-md);
  }
}

// 自定义按钮样式 - 使用新的统一样式系统
:deep() {
  // 草稿/保存按钮样式
  .btn-draft {
    background: var(--app-bg-secondary) !important;
    border: 2rpx solid var(--app-border-primary) !important;
    color: var(--app-text-primary) !important;
    font-weight: var(--app-font-weight-medium) !important;
    border-radius: var(--app-radius-lg) !important;
    transition: all var(--app-duration-base) var(--app-ease-out) !important;
    box-shadow: none !important;

    &:hover {
      background: var(--app-bg-tertiary) !important;
      border-color: var(--app-color-primary) !important;
      transform: translateY(-2rpx) !important;
      box-shadow: var(--app-shadow-sm) !important;
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: none !important;
    }

    &:disabled {
      opacity: 0.6 !important;
      transform: none !important;
    }
  }

  // 发布按钮样式
  .btn-publish {
    background: linear-gradient(135deg, var(--app-color-success), #059669) !important;
    border: none !important;
    color: var(--app-text-inverse) !important;
    font-weight: var(--app-font-weight-semibold) !important;
    border-radius: var(--app-radius-lg) !important;
    transition: all var(--app-duration-base) var(--app-ease-out) !important;
    box-shadow: var(--app-shadow-base) !important;

    &:hover {
      background: linear-gradient(135deg, #059669, #047857) !important;
      transform: translateY(-2rpx) !important;
      box-shadow: var(--app-shadow-md) !important;
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: var(--app-shadow-base) !important;
    }

    &:disabled {
      opacity: 0.6 !important;
      transform: none !important;
    }
  }

  // 交易意向选择器样式
  .trade-intention-radio-group {
    display: flex;
    align-items: center;
    gap: var(--app-spacing-lg);

    .trade-intention-radio {
      font-size: var(--app-font-size-sm);

      :deep(.wd-radio) {
        display: flex !important;
        align-items: center !important;
        gap: var(--app-spacing-xs);
        flex-direction: row !important;
      }

      :deep(.wd-radio__icon) {
        flex-shrink: 0;
        align-self: center;
        margin: 0 !important;
      }

      :deep(.wd-radio__label) {
        padding: var(--app-spacing-xs) var(--app-spacing-sm);
        border-radius: var(--app-radius-sm);
        transition: all var(--app-duration-fast) var(--app-ease-out);
        display: flex;
        align-items: center;
        line-height: var(--app-line-height-tight);
      }

      :deep(.wd-radio--checked) .wd-radio__label {
        background-color: rgba(59, 130, 246, 0.1);
        color: var(--app-color-primary);
        font-weight: var(--app-font-weight-semibold);
      }
    }
  }

  // 价格类型选择器样式
  .price-type-section {
    padding: 0 var(--app-spacing-base);

    .price-type-tabs {
      :deep(.wd-tabs__nav) {
        background-color: var(--app-bg-secondary);
        border-radius: var(--app-radius-md);
        padding: var(--app-spacing-xs);
        margin-bottom: 0;
      }

      :deep(.wd-tab) {
        flex: 1;
        border-radius: var(--app-radius-sm);
        transition: all var(--app-duration-base) var(--app-ease-out);
        font-size: var(--app-font-size-sm);
        font-weight: var(--app-font-weight-medium);

        &.is-active {
          background-color: var(--app-color-primary);
          color: var(--app-text-inverse);
          font-weight: var(--app-font-weight-semibold);
        }
      }

      :deep(.wd-tabs__line) {
        display: none;
      }

      :deep(.wd-tabs__content) {
        display: none;
      }
    }
  }

  // 字数统计层级调整
  .wd-textarea__count {
    z-index: 1 !important;
  }
}
</style>
