# 统一样式风格指南

本文档介绍了项目的统一样式系统，包括设计原则、使用方法和最佳实践。

** 注意： 不要使用css 中的 * 通配符 微信小程序不支持 **
## 🎨 设计系统概览

我们实现了一套完整的设计系统，包括：
- **CSS 变量系统** - 统一的设计 tokens
- **UnoCSS 预设** - 丰富的原子化样式类
- **组件样式库** - 标准化的组件样式
- **工具类** - 便捷的工具样式类

## 🌈 颜色系统

### 主色系 (Primary Colors)
```scss
// CSS 变量
var(--app-color-primary)       // #3b82f6 - 主色
var(--app-color-primary-50)    // #eff6ff - 最浅
var(--app-color-primary-500)   // #3b82f6 - 标准
var(--app-color-primary-900)   // #1e3a8a - 最深

// UnoCSS 类
bg-primary-500    text-primary-600    border-primary-300
```

### 功能色系 (Functional Colors)
```scss
// 成功色
var(--app-color-success)       // #10b981
bg-success                     text-success

// 警告色  
var(--app-color-warning)       // #f59e0b
bg-warning                     text-warning

// 错误色
var(--app-color-error)         // #ef4444  
bg-error                       text-error

// 信息色
var(--app-color-info)          // #3b82f6
bg-info                        text-info
```

### 中性色系 (Neutral Colors)
```scss
// 文本颜色
var(--app-text-primary)        // 主要文本
var(--app-text-secondary)      // 次要文本  
var(--app-text-tertiary)       // 辅助文本

// UnoCSS 类
text-gray-900                  text-gray-600                 text-gray-400
```

## 📏 间距系统

基于 8rpx 网格的间距系统：

```scss
// CSS 变量
var(--app-spacing-xs)          // 8rpx
var(--app-spacing-sm)          // 16rpx
var(--app-spacing-base)        // 24rpx
var(--app-spacing-md)          // 32rpx
var(--app-spacing-lg)          // 48rpx
var(--app-spacing-xl)          // 64rpx

// UnoCSS 类
p-2                            // padding: 16rpx
m-4                            // margin: 32rpx
px-3                           // padding-left/right: 24rpx
```

## 🔤 字体系统

### 字体大小
```scss
// CSS 变量
var(--app-font-size-xs)        // 24rpx
var(--app-font-size-sm)        // 28rpx  
var(--app-font-size-base)      // 32rpx
var(--app-font-size-lg)        // 36rpx
var(--app-font-size-xl)        // 40rpx

// UnoCSS 类
text-xs                        text-sm                       text-base                    text-lg                      text-xl
```

### 字重
```scss
// CSS 变量
var(--app-font-weight-normal)   // 400
var(--app-font-weight-medium)   // 500
var(--app-font-weight-semibold) // 600
var(--app-font-weight-bold)     // 700

// UnoCSS 类
font-normal                     font-medium                  font-semibold                font-bold
```

## 🎭 组件样式库

### 页面容器
```vue
<!-- 推荐：新的标准页面容器 -->
<template>
  <view class="app-container">
    <view class="app-content">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<!-- 或使用 UnoCSS shortcuts -->
<template>
  <view class="page-wrapper">
    <view class="content-container">
      <!-- 页面内容 -->
    </view>
  </view>
</template>

<!-- 向后兼容：现有页面容器 -->
<template>
  <view class="page-container modern">
    <!-- 页面内容 -->
  </view>
</template>
```

### 卡片组件
```vue
<!-- 推荐：新的标准卡片 -->
<template>
  <view class="app-card">
    <view class="app-card-header">
      <h2 class="app-text-subtitle">卡片标题</h2>
    </view>
    <view class="app-card-body">
      <p class="app-text-body">卡片内容</p>
    </view>
  </view>
</template>

<!-- 或使用 UnoCSS shortcuts -->
<template>
  <view class="app-card">
    <!-- 卡片内容 -->
  </view>
</template>

<!-- 向后兼容：现有卡片 -->
<template>
  <view class="common-card">
    <!-- 卡片内容 -->
  </view>
</template>
```

### 按钮组件
```vue
<!-- 推荐：使用 wot-design-uni 组件 + custom-class -->
<template>
  <wd-button type="primary" custom-class="mb-2">
    主要按钮
  </wd-button>
  
  <wd-button type="default" custom-class="btn-secondary">
    次要按钮
  </wd-button>
  
  <!-- 自定义颜色按钮 -->
  <wd-button custom-class="btn-edit">编辑</wd-button>
  <wd-button custom-class="btn-publish">发布</wd-button>
  <wd-button custom-class="btn-delete">删除</wd-button>
</template>

<!-- 全局样式定义 -->
<style lang="scss">
/* wot-design-uni 组件的 custom-class 需要全局样式 */
.btn-secondary {
  background: var(--app-bg-secondary);
  color: var(--app-text-primary);
  border: 2rpx solid var(--app-border-primary);
}

.btn-edit {
  background: linear-gradient(135deg, var(--app-color-primary-500), var(--app-color-primary-600)) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
}

.btn-publish {
  background: linear-gradient(135deg, var(--app-color-success), #059669) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
}

.btn-delete {
  background: linear-gradient(135deg, var(--app-color-error), #dc2626) !important;
  border: none !important;
  color: var(--app-text-inverse) !important;
  border-radius: var(--app-radius-lg) !important;
}
</style>

<!-- 或使用 UnoCSS shortcuts -->
<template>
  <button class="btn-primary">主要按钮</button>
  <button class="btn-secondary">次要按钮</button>
  <button class="btn-ghost">幽灵按钮</button>
</template>

<!-- 或使用标准样式类 -->
<template>
  <button class="app-btn app-btn-primary">
    主要按钮
  </button>
</template>
```

### 表单组件
```vue
<!-- 推荐：标准表单样式 -->
<template>
  <view class="app-form">
    <view class="app-form-item">
      <label class="app-form-label">用户名</label>
      <view class="app-form-control">
        <input class="app-form-input" placeholder="请输入用户名" />
      </view>
    </view>
    
    <view class="app-form-item">
      <label class="app-form-label">密码</label>
      <view class="app-form-control">
        <input type="password" class="app-form-input" placeholder="请输入密码" />
      </view>
      <view class="app-form-help">密码长度至少8位</view>
    </view>
  </view>
</template>

<!-- 或使用 wot-design-uni + UnoCSS -->
<template>
  <wd-cell-group>
    <wd-input
      label="用户名"
      v-model="username"
      placeholder="请输入用户名"
      class="mb-2"
    />
    <wd-input
      label="密码"
      type="password"
      v-model="password"
      placeholder="请输入密码"
    />
  </wd-cell-group>
</template>
```

## 🛠 UnoCSS Shortcuts

我们预设了许多便捷的 shortcuts：

### 布局相关
```html
<!-- 居中布局 -->
<view class="center">内容居中</view>
<view class="flex-col-center">垂直居中</view>
<view class="flex-between">两端对齐</view>

<!-- 页面容器 -->
<view class="app-container">页面容器</view>
<view class="page-wrapper">页面包装器</view>
```

### 卡片相关
```html
<!-- 各种卡片样式 -->
<view class="app-card">标准卡片</view>
<view class="app-card-hover">悬浮卡片</view>
<view class="glass-card">毛玻璃卡片</view>
```

### 按钮相关
```html
<!-- 按钮样式 -->
<button class="btn-primary">主要按钮</button>
<button class="btn-secondary">次要按钮</button>
<button class="btn-ghost">幽灵按钮</button>
```

### 文本相关
```html
<!-- 文本样式 -->
<h1 class="text-title">标题文本</h1>
<h2 class="text-subtitle">副标题</h2>
<p class="text-body">正文内容</p>
<small class="text-caption">说明文字</small>
<a class="text-link">链接文本</a>
```

## 🎬 动画系统

### 预设动画
```vue
<template>
  <!-- 淡入动画 -->
  <view class="animate-fade-in">淡入内容</view>
  
  <!-- 滑入动画 -->
  <view class="animate-slide-up">向上滑入</view>
  
  <!-- 缩放动画 -->
  <view class="animate-scale">缩放内容</view>
</template>
```

### 自定义动画
```scss
.custom-element {
  transition: all var(--app-duration-base) var(--app-ease-out);
  
  &:hover {
    transform: translateY(-2rpx);
    box-shadow: var(--app-shadow-md);
  }
}
```

## 📱 响应式设计

### 屏幕断点
```scss
// 小屏设备 (手机)
@media (max-width: 750rpx) {
  .responsive-element {
    padding: var(--app-spacing-xs);
  }
}

// 大屏设备 (平板)
@media (min-width: 751rpx) {
  .responsive-element {
    padding: var(--app-spacing-lg);
  }
}
```

### UnoCSS 响应式类
```html
<!-- 响应式间距 -->
<view class="p-2 md:p-4">响应式内边距</view>

<!-- 响应式文字 -->
<h1 class="text-lg md:text-2xl">响应式标题</h1>
```

## 📋 迁移指南

### 从旧样式迁移到新样式

#### 1. 页面容器迁移
```vue
<!-- 旧的写法 -->
<template>
  <view class="login-container">
    <!-- 内容 -->
  </view>
</template>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  padding: 100rpx 48rpx 60rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>

<!-- 新的写法 -->
<template>
  <view class="app-container">
    <view class="app-content py-20 px-6">
      <!-- 内容 -->
    </view>
  </view>
</template>

<!-- 无需自定义样式，或仅需少量调整 -->
<style lang="scss" scoped>
.custom-bg {
  background: linear-gradient(135deg, var(--app-color-primary) 0%, var(--app-color-primary-700) 100%);
}
</style>
```

#### 2. 卡片组件迁移
```vue
<!-- 旧的写法 -->
<template>
  <view class="custom-card">
    <!-- 内容 -->
  </view>
</template>

<style lang="scss" scoped>
.custom-card {
  background: rgba(255, 255, 255, 0.75);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
}
</style>

<!-- 新的写法 -->
<template>
  <view class="app-card">
    <view class="app-card-body p-8">
      <!-- 内容 -->
    </view>
  </view>
</template>

<!-- 或使用 UnoCSS -->
<template>
  <view class="glass-card p-8">
    <!-- 内容 -->
  </view>
</template>
```

#### 3. 表单组件迁移
```vue
<!-- 旧的写法 -->
<template>
  <view class="form-item">
    <text class="form-label">标签</text>
    <input class="form-input" />
  </view>
</template>

<style lang="scss" scoped>
.form-item {
  margin-bottom: 36rpx;
}
.form-label {
  font-size: 28rpx;
  color: #2c3e50;
  font-weight: 600;
}
.form-input {
  padding: 0 24rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 12rpx;
}
</style>

<!-- 新的写法 -->
<template>
  <view class="app-form-item">
    <label class="app-form-label">标签</label>
    <input class="app-form-input" />
  </view>
</template>

<!-- 或使用 UnoCSS -->
<template>
  <view class="mb-4">
    <label class="text-subtitle mb-1">标签</label>
    <input class="input-base" />
  </view>
</template>
```

## 🎯 最佳实践

### 1. 优先级原则
1. **优先使用 wot-design-uni 组件** - 保持与组件库的一致性
2. **其次使用 UnoCSS shortcuts** - 快速实现常用样式
3. **然后使用标准样式类** - app-* 系列类名
4. **最后自定义样式** - 仅在必要时使用

### 2. 命名规范
```scss
// CSS 变量命名
--app-{category}-{property}-{variant}
--app-color-primary-500
--app-spacing-lg
--app-shadow-md

// 样式类命名
.app-{component}-{element}-{modifier}
.app-card-header-primary
.app-btn-large-disabled
```

### 3. 颜色使用原则
- 主色用于重要操作和品牌元素
- 功能色用于状态反馈 (成功、警告、错误)
- 中性色用于文本和边框
- 避免直接使用色值，使用 CSS 变量

### 4. 间距使用原则
- 基于 8rpx 网格系统
- 页面级间距使用 lg (48rpx) 或 xl (64rpx)
- 组件级间距使用 base (24rpx) 或 md (32rpx)
- 元素级间距使用 xs (8rpx) 或 sm (16rpx)

### 5. 字体使用原则
- 标题使用 xl (40rpx) 或 2xl (48rpx)
- 正文使用 base (32rpx)
- 说明文字使用 sm (28rpx) 或 xs (24rpx)
- 保持合适的行高和字重

## 🔧 开发工具

### VSCode 插件推荐
- **UnoCSS** - 语法高亮和自动补全
- **CSS Peek** - 快速查看 CSS 定义
- **Color Highlight** - 颜色值高亮显示

### 调试技巧
```scss
// 临时调试样式，生产环境删除
.debug-border {
  border: 1rpx solid red !important;
}

// 查看当前使用的 CSS 变量值
.debug-vars::before {
  content: 'Primary: ' var(--app-color-primary);
}
```

## 🚀 性能优化

### 1. 减少自定义样式
- 尽量使用预设的样式类
- 避免重复定义相同的样式

### 2. 合理使用 CSS 变量
- CSS 变量会被继承，合理利用继承减少重复定义
- 在组件根元素设置主题变量

### 3. 条件编译优化
```scss
/* 根据平台优化样式 */
/* #ifdef MP-WEIXIN */
.weixin-specific {
  /* 微信小程序专用样式 */
}
/* #endif */

/* #ifdef H5 */
.h5-specific {
  /* H5 专用样式 */
}
/* #endif */
```

## 📖 参考资源

- [UnoCSS 官方文档](https://unocss.dev/)
- [wot-design-uni 组件库](https://wot-design-uni.netlify.app/)
- [Material Design 设计规范](https://material.io/design)
- [Tailwind CSS 工具类参考](https://tailwindcss.com/docs)

---

## 🔄 版本更新

### v1.0.0 (当前版本)
- ✅ 完整的 CSS 变量系统
- ✅ UnoCSS 配置和 shortcuts
- ✅ 标准化组件样式库
- ✅ 响应式设计支持
- ✅ 动画系统
- ✅ 工具类样式

### 计划中的功能
- 🔄 暗色主题支持
- 🔄 更多动画预设
- 🔄 组件主题定制工具
- 🔄 设计 tokens 可视化工具

---

这套统一样式系统让你的应用拥有一致的视觉体验，提高开发效率，便于维护和扩展。遵循本指南，你可以轻松创建美观、一致的用户界面。
